{"build": "haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-crt-tools-12.4.131-haa95532_0", "files": ["Library/bin/crt/link.stub", "Library/bin/crt/prelink.stub"], "fn": "cuda-crt-tools-12.4.131-haa95532_0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-crt-tools-12.4.131-haa95532_0", "type": 1}, "md5": "c132a2fb070840696040fe1a719a8ea5", "name": "cuda-crt-tools", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-crt-tools-12.4.131-haa95532_0.conda", "paths_data": {"paths": [{"_path": "Library/bin/crt/link.stub", "path_type": "hardlink", "sha256": "ec2fb9ea21567daba1e6aeb62211dea497600369af3e023f1ad169b6e39c4c7b", "sha256_in_prefix": "ec2fb9ea21567daba1e6aeb62211dea497600369af3e023f1ad169b6e39c4c7b", "size_in_bytes": 6637}, {"_path": "Library/bin/crt/prelink.stub", "path_type": "hardlink", "sha256": "f91db9f00463d557ceb1fbd4ce05b6d1257677dd449cfb5f02244e2a51183f13", "sha256_in_prefix": "f91db9f00463d557ceb1fbd4ce05b6d1257677dd449cfb5f02244e2a51183f13", "size_in_bytes": 4069}], "paths_version": 1}, "requested_spec": "None", "sha256": "c3174dc4fb3211368f4ac73ef61f2b7fb54c581673f22923ee57be63d1f3d19a", "size": 26085, "subdir": "win-64", "timestamp": 1714770027000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-crt-tools-12.4.131-haa95532_0.conda", "version": "12.4.131"}