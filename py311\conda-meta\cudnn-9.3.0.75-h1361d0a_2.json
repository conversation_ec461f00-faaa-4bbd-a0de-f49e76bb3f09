{"build": "h1361d0a_2", "build_number": 2, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": [], "depends": ["libcublas", "cuda-nvrtc", "ucrt >=10.0.20348.0", "vc >=14.2,<15", "vc14_runtime >=14.29.30139", "cuda-version >=12,<13.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cudnn-********-h1361d0a_2", "files": ["Library/bin/cudnn64_9.dll", "Library/bin/cudnn_adv64_9.dll", "Library/bin/cudnn_cnn64_9.dll", "Library/bin/cudnn_engines_precompiled64_9.dll", "Library/bin/cudnn_engines_runtime_compiled64_9.dll", "Library/bin/cudnn_graph64_9.dll", "Library/bin/cudnn_heuristic64_9.dll", "Library/bin/cudnn_ops64_9.dll", "Library/include/cudnn.h", "Library/include/cudnn_adv.h", "Library/include/cudnn_backend.h", "Library/include/cudnn_cnn.h", "Library/include/cudnn_graph.h", "Library/include/cudnn_ops.h", "Library/include/cudnn_version.h", "Library/lib/cudnn.lib", "Library/lib/cudnn64_9.lib", "Library/lib/cudnn_adv.lib", "Library/lib/cudnn_adv64_9.lib", "Library/lib/cudnn_cnn.lib", "Library/lib/cudnn_cnn64_9.lib", "Library/lib/cudnn_engines_precompiled.lib", "Library/lib/cudnn_engines_precompiled64_9.lib", "Library/lib/cudnn_engines_runtime_compiled.lib", "Library/lib/cudnn_engines_runtime_compiled64_9.lib", "Library/lib/cudnn_graph.lib", "Library/lib/cudnn_graph64_9.lib", "Library/lib/cudnn_heuristic.lib", "Library/lib/cudnn_heuristic64_9.lib", "Library/lib/cudnn_ops.lib", "Library/lib/cudnn_ops64_9.lib", "Scripts/.cudnn-post-link.bat"], "fn": "cudnn-********-h1361d0a_2.conda", "license": "LicenseRef-cuDNN-Software-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cudnn-********-h1361d0a_2", "type": 1}, "md5": "237dc47de5ffdb9b51b00a1f8b6932c1", "name": "cudnn", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cudnn-********-h1361d0a_2.conda", "paths_data": {"paths": [{"_path": "Library/bin/cudnn64_9.dll", "path_type": "hardlink", "sha256": "ca7ccb433938cee836c9582c0b70358a960b60d6bd0a623fd3ba22b6df136b2e", "sha256_in_prefix": "ca7ccb433938cee836c9582c0b70358a960b60d6bd0a623fd3ba22b6df136b2e", "size_in_bytes": 441896}, {"_path": "Library/bin/cudnn_adv64_9.dll", "path_type": "hardlink", "sha256": "aa63b96b253d62303a94b777a1a5fd18b427d6b8f8ae49cf7ee29035a3e9ee6e", "sha256_in_prefix": "aa63b96b253d62303a94b777a1a5fd18b427d6b8f8ae49cf7ee29035a3e9ee6e", "size_in_bytes": 244336184}, {"_path": "Library/bin/cudnn_cnn64_9.dll", "path_type": "hardlink", "sha256": "a943e6c4a5246911a56d3334ff608199ac1e0e9dd8f1c20099af1a7f0296d07b", "sha256_in_prefix": "a943e6c4a5246911a56d3334ff608199ac1e0e9dd8f1c20099af1a7f0296d07b", "size_in_bytes": 4018216}, {"_path": "Library/bin/cudnn_engines_precompiled64_9.dll", "path_type": "hardlink", "sha256": "ab72d617d73266015d369f98cb6ba10008b37b281438c50596b9ddf868e38c28", "sha256_in_prefix": "ab72d617d73266015d369f98cb6ba10008b37b281438c50596b9ddf868e38c28", "size_in_bytes": 445809720}, {"_path": "Library/bin/cudnn_engines_runtime_compiled64_9.dll", "path_type": "hardlink", "sha256": "3e501483460f0a12dcfb5fdef7f99968d212f7638d9f4a973b65f9072da70637", "sha256_in_prefix": "3e501483460f0a12dcfb5fdef7f99968d212f7638d9f4a973b65f9072da70637", "size_in_bytes": 9378872}, {"_path": "Library/bin/cudnn_graph64_9.dll", "path_type": "hardlink", "sha256": "3af11f5228c588ba77052001873dd16c493a3327e540b2902455598392a232f6", "sha256_in_prefix": "3af11f5228c588ba77052001873dd16c493a3327e540b2902455598392a232f6", "size_in_bytes": 2228280}, {"_path": "Library/bin/cudnn_heuristic64_9.dll", "path_type": "hardlink", "sha256": "bcc3fa2f94ea7284ffb369373621c92c0a0821787c5d270f42709111f259eb32", "sha256_in_prefix": "bcc3fa2f94ea7284ffb369373621c92c0a0821787c5d270f42709111f259eb32", "size_in_bytes": 53679672}, {"_path": "Library/bin/cudnn_ops64_9.dll", "path_type": "hardlink", "sha256": "3b8b00a95dc827a1aa75f7c1250b6ca99d293f0a2f110c7de8919cd95208a9d8", "sha256_in_prefix": "3b8b00a95dc827a1aa75f7c1250b6ca99d293f0a2f110c7de8919cd95208a9d8", "size_in_bytes": 107684408}, {"_path": "Library/include/cudnn.h", "path_type": "hardlink", "sha256": "2ddcf445c5c6656886bb78641e18fdfdeef96cda7ff3b7f1ac4f5da72b4d39e7", "sha256_in_prefix": "2ddcf445c5c6656886bb78641e18fdfdeef96cda7ff3b7f1ac4f5da72b4d39e7", "size_in_bytes": 2909}, {"_path": "Library/include/cudnn_adv.h", "path_type": "hardlink", "sha256": "7954cfbc5308f7a5a08aad6130d2951a585bc436f61167a03e49f78a1fed92a5", "sha256_in_prefix": "7954cfbc5308f7a5a08aad6130d2951a585bc436f61167a03e49f78a1fed92a5", "size_in_bytes": 31491}, {"_path": "Library/include/cudnn_backend.h", "path_type": "hardlink", "sha256": "da1e85ac81029ab64a42ccafa30a699785e2361be28a83e02a5ca8c23d55fd9d", "sha256_in_prefix": "da1e85ac81029ab64a42ccafa30a699785e2361be28a83e02a5ca8c23d55fd9d", "size_in_bytes": 2811}, {"_path": "Library/include/cudnn_cnn.h", "path_type": "hardlink", "sha256": "eac8ebd7d84d90e6761396f7d5d4ca298a1f2a65aca67b0cb8c01337107f9936", "sha256_in_prefix": "eac8ebd7d84d90e6761396f7d5d4ca298a1f2a65aca67b0cb8c01337107f9936", "size_in_bytes": 37392}, {"_path": "Library/include/cudnn_graph.h", "path_type": "hardlink", "sha256": "4039d1b8abc1a62b909c0242722438912cb0405b37cc42c52be52caf4ddfaa22", "sha256_in_prefix": "4039d1b8abc1a62b909c0242722438912cb0405b37cc42c52be52caf4ddfaa22", "size_in_bytes": 39079}, {"_path": "Library/include/cudnn_ops.h", "path_type": "hardlink", "sha256": "5a47a0ec8e0922c8daaa536e632e678c7e2d70364623f8f5fad44f123faff6d3", "sha256_in_prefix": "5a47a0ec8e0922c8daaa536e632e678c7e2d70364623f8f5fad44f123faff6d3", "size_in_bytes": 64949}, {"_path": "Library/include/cudnn_version.h", "path_type": "hardlink", "sha256": "79a8d4196972cbae87e2a4e9d949e50697774f2d19550fce5fc861c321318e4d", "sha256_in_prefix": "79a8d4196972cbae87e2a4e9d949e50697774f2d19550fce5fc861c321318e4d", "size_in_bytes": 3182}, {"_path": "Library/lib/cudnn.lib", "path_type": "hardlink", "sha256": "41551c685fcb7939e144d9e6463a10692b964793474551417a251e99134cc2cb", "sha256_in_prefix": "41551c685fcb7939e144d9e6463a10692b964793474551417a251e99134cc2cb", "size_in_bytes": 61160}, {"_path": "Library/lib/cudnn64_9.lib", "path_type": "hardlink", "sha256": "41551c685fcb7939e144d9e6463a10692b964793474551417a251e99134cc2cb", "sha256_in_prefix": "41551c685fcb7939e144d9e6463a10692b964793474551417a251e99134cc2cb", "size_in_bytes": 61160}, {"_path": "Library/lib/cudnn_adv.lib", "path_type": "hardlink", "sha256": "175412ba51a9771c812aab73318dec8b9c92f4104b0b44945a5df1e65d88547d", "sha256_in_prefix": "175412ba51a9771c812aab73318dec8b9c92f4104b0b44945a5df1e65d88547d", "size_in_bytes": 65182}, {"_path": "Library/lib/cudnn_adv64_9.lib", "path_type": "hardlink", "sha256": "175412ba51a9771c812aab73318dec8b9c92f4104b0b44945a5df1e65d88547d", "sha256_in_prefix": "175412ba51a9771c812aab73318dec8b9c92f4104b0b44945a5df1e65d88547d", "size_in_bytes": 65182}, {"_path": "Library/lib/cudnn_cnn.lib", "path_type": "hardlink", "sha256": "78a3f7e8db776c8aca663cf1309501b8e3b468213f3234b47b46251d9fe8d98c", "sha256_in_prefix": "78a3f7e8db776c8aca663cf1309501b8e3b468213f3234b47b46251d9fe8d98c", "size_in_bytes": 20946}, {"_path": "Library/lib/cudnn_cnn64_9.lib", "path_type": "hardlink", "sha256": "78a3f7e8db776c8aca663cf1309501b8e3b468213f3234b47b46251d9fe8d98c", "sha256_in_prefix": "78a3f7e8db776c8aca663cf1309501b8e3b468213f3234b47b46251d9fe8d98c", "size_in_bytes": 20946}, {"_path": "Library/lib/cudnn_engines_precompiled.lib", "path_type": "hardlink", "sha256": "3a699b194686e9746195b66075eb855a10d6ecc62be8eeeb45c5a26e27ef84b2", "sha256_in_prefix": "3a699b194686e9746195b66075eb855a10d6ecc62be8eeeb45c5a26e27ef84b2", "size_in_bytes": 5686}, {"_path": "Library/lib/cudnn_engines_precompiled64_9.lib", "path_type": "hardlink", "sha256": "3a699b194686e9746195b66075eb855a10d6ecc62be8eeeb45c5a26e27ef84b2", "sha256_in_prefix": "3a699b194686e9746195b66075eb855a10d6ecc62be8eeeb45c5a26e27ef84b2", "size_in_bytes": 5686}, {"_path": "Library/lib/cudnn_engines_runtime_compiled.lib", "path_type": "hardlink", "sha256": "6cc3fa4983cf7876a06a785046fc2c9e2011d037be9d939bd5f45f12943cd4fd", "sha256_in_prefix": "6cc3fa4983cf7876a06a785046fc2c9e2011d037be9d939bd5f45f12943cd4fd", "size_in_bytes": 6240}, {"_path": "Library/lib/cudnn_engines_runtime_compiled64_9.lib", "path_type": "hardlink", "sha256": "6cc3fa4983cf7876a06a785046fc2c9e2011d037be9d939bd5f45f12943cd4fd", "sha256_in_prefix": "6cc3fa4983cf7876a06a785046fc2c9e2011d037be9d939bd5f45f12943cd4fd", "size_in_bytes": 6240}, {"_path": "Library/lib/cudnn_graph.lib", "path_type": "hardlink", "sha256": "ef6153d48003866a33d63cd3d7e5cbe316c40653b3f66091c4b4029ae3353b93", "sha256_in_prefix": "ef6153d48003866a33d63cd3d7e5cbe316c40653b3f66091c4b4029ae3353b93", "size_in_bytes": 889418}, {"_path": "Library/lib/cudnn_graph64_9.lib", "path_type": "hardlink", "sha256": "ef6153d48003866a33d63cd3d7e5cbe316c40653b3f66091c4b4029ae3353b93", "sha256_in_prefix": "ef6153d48003866a33d63cd3d7e5cbe316c40653b3f66091c4b4029ae3353b93", "size_in_bytes": 889418}, {"_path": "Library/lib/cudnn_heuristic.lib", "path_type": "hardlink", "sha256": "6f322148a3517024b05c6947e41e8a6306361d47e42d80e5e43f0497673272ff", "sha256_in_prefix": "6f322148a3517024b05c6947e41e8a6306361d47e42d80e5e43f0497673272ff", "size_in_bytes": 4436}, {"_path": "Library/lib/cudnn_heuristic64_9.lib", "path_type": "hardlink", "sha256": "6f322148a3517024b05c6947e41e8a6306361d47e42d80e5e43f0497673272ff", "sha256_in_prefix": "6f322148a3517024b05c6947e41e8a6306361d47e42d80e5e43f0497673272ff", "size_in_bytes": 4436}, {"_path": "Library/lib/cudnn_ops.lib", "path_type": "hardlink", "sha256": "28e95f26bfdbfeea70c5e7ff91dfb81466c81136cb718f42dc33084726d632c8", "sha256_in_prefix": "28e95f26bfdbfeea70c5e7ff91dfb81466c81136cb718f42dc33084726d632c8", "size_in_bytes": 45720}, {"_path": "Library/lib/cudnn_ops64_9.lib", "path_type": "hardlink", "sha256": "28e95f26bfdbfeea70c5e7ff91dfb81466c81136cb718f42dc33084726d632c8", "sha256_in_prefix": "28e95f26bfdbfeea70c5e7ff91dfb81466c81136cb718f42dc33084726d632c8", "size_in_bytes": 45720}, {"_path": "Scripts/.cudnn-post-link.bat", "path_type": "hardlink", "sha256": "51d209aa7a353b7090eff367658956cb3b634ab9e470f161294f72c8231f8862", "sha256_in_prefix": "51d209aa7a353b7090eff367658956cb3b634ab9e470f161294f72c8231f8862", "size_in_bytes": 208}], "paths_version": 1}, "requested_spec": "cudnn", "sha256": "f6611c78856ecc3bacf4b85963eda848186136293bfe1aac2ee7db0418a05795", "size": 393021165, "subdir": "win-64", "timestamp": 1735784296000, "url": "https://conda.anaconda.org/conda-forge/win-64/cudnn-********-h1361d0a_2.conda", "version": "********"}