{"build": "h3b0e114_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": ["libwebp 1.5.0"], "depends": ["ucrt >=10.0.20348.0", "vc >=14.2,<15", "vc14_runtime >=14.29.30139"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libwebp-base-1.5.0-h3b0e114_0", "files": ["Library/WebP/cmake/WebPConfig.cmake", "Library/WebP/cmake/WebPConfigVersion.cmake", "Library/WebP/cmake/WebPTargets-release.cmake", "Library/WebP/cmake/WebPTargets.cmake", "Library/bin/libsharpyuv.dll", "Library/bin/libwebp.dll", "Library/bin/libwebpdecoder.dll", "Library/bin/libwebpdemux.dll", "Library/bin/libwebpmux.dll", "Library/include/webp/decode.h", "Library/include/webp/demux.h", "Library/include/webp/encode.h", "Library/include/webp/mux.h", "Library/include/webp/mux_types.h", "Library/include/webp/sharpyuv/sharpyuv.h", "Library/include/webp/sharpyuv/sharpyuv_csp.h", "Library/include/webp/types.h", "Library/lib/libsharpyuv.lib", "Library/lib/libwebp.lib", "Library/lib/libwebpdecoder.lib", "Library/lib/libwebpdemux.lib", "Library/lib/libwebpmux.lib", "Library/lib/pkgconfig/libsharpyuv.pc", "Library/lib/pkgconfig/libwebp.pc", "Library/lib/pkgconfig/libwebpdecoder.pc", "Library/lib/pkgconfig/libwebpdemux.pc", "Library/lib/pkgconfig/libwebpmux.pc"], "fn": "libwebp-base-1.5.0-h3b0e114_0.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "D:\\anaconda3\\pkgs\\libwebp-base-1.5.0-h3b0e114_0", "type": 1}, "md5": "33f7313967072c6e6d8f865f5493c7ae", "name": "libwebp-base", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libwebp-base-1.5.0-h3b0e114_0.conda", "paths_data": {"paths": [{"_path": "Library/WebP/cmake/WebPConfig.cmake", "path_type": "hardlink", "sha256": "754a29095a88c6791ae3210746ab1a11d1a2ee3265783de62baf295943ac2dcc", "sha256_in_prefix": "754a29095a88c6791ae3210746ab1a11d1a2ee3265783de62baf295943ac2dcc", "size_in_bytes": 1415}, {"_path": "Library/WebP/cmake/WebPConfigVersion.cmake", "path_type": "hardlink", "sha256": "55ea91700f6a8572c6663f4522b06e5a9430c008306f5da0b00534cf443b3406", "sha256_in_prefix": "55ea91700f6a8572c6663f4522b06e5a9430c008306f5da0b00534cf443b3406", "size_in_bytes": 1904}, {"_path": "Library/WebP/cmake/WebPTargets-release.cmake", "path_type": "hardlink", "sha256": "95bb2606c2d95e7c05d859525d695ad20be263352633da45a832ceab041f8a7b", "sha256_in_prefix": "95bb2606c2d95e7c05d859525d695ad20be263352633da45a832ceab041f8a7b", "size_in_bytes": 3088}, {"_path": "Library/WebP/cmake/WebPTargets.cmake", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "D:/bld/libwebp-base_1734777333739/_h_env", "sha256": "6cf09b1c828907da0051f0fdbef1f829971daa5c5a3fa93cb342c033f87ce004", "sha256_in_prefix": "f61d1fe0e3ce18f680375e990babb90e7301e7726d72ac9c7fd9596b3944d08c", "size_in_bytes": 5192}, {"_path": "Library/bin/libsharpyuv.dll", "path_type": "hardlink", "sha256": "3112b10f952b2c948d9d5301c9895c2069d348d36a16a4a43e7239ddc8c3b10c", "sha256_in_prefix": "3112b10f952b2c948d9d5301c9895c2069d348d36a16a4a43e7239ddc8c3b10c", "size_in_bytes": 25088}, {"_path": "Library/bin/libwebp.dll", "path_type": "hardlink", "sha256": "07eb5db34e17b80aefd346008f97c77a010abbb8177ebf7336fb04da23d73ce6", "sha256_in_prefix": "07eb5db34e17b80aefd346008f97c77a010abbb8177ebf7336fb04da23d73ce6", "size_in_bytes": 377344}, {"_path": "Library/bin/libwebpdecoder.dll", "path_type": "hardlink", "sha256": "6f1b5d074ce0660e83debc1768907278d2bf11aa428e1ca0240122d9475722ae", "sha256_in_prefix": "6f1b5d074ce0660e83debc1768907278d2bf11aa428e1ca0240122d9475722ae", "size_in_bytes": 166912}, {"_path": "Library/bin/libwebpdemux.dll", "path_type": "hardlink", "sha256": "b79b4f2e659ad24e97b509bee4fe31b040e3d3b0950f068b02b5c295538369f9", "sha256_in_prefix": "b79b4f2e659ad24e97b509bee4fe31b040e3d3b0950f068b02b5c295538369f9", "size_in_bytes": 19968}, {"_path": "Library/bin/libwebpmux.dll", "path_type": "hardlink", "sha256": "3f036b7d408ce9790f203cc9fe7739820e73ef9fc8439ba4762a04e5afc3383e", "sha256_in_prefix": "3f036b7d408ce9790f203cc9fe7739820e73ef9fc8439ba4762a04e5afc3383e", "size_in_bytes": 41472}, {"_path": "Library/include/webp/decode.h", "path_type": "hardlink", "sha256": "300db34c56d965ee8304f0f0e947ba226403362833ce5d956c6d5f9814a5297f", "sha256_in_prefix": "300db34c56d965ee8304f0f0e947ba226403362833ce5d956c6d5f9814a5297f", "size_in_bytes": 23960}, {"_path": "Library/include/webp/demux.h", "path_type": "hardlink", "sha256": "984983b09785e2af7e8c5cefd2e2f08d2116870c13108445665b11256aa82fec", "sha256_in_prefix": "984983b09785e2af7e8c5cefd2e2f08d2116870c13108445665b11256aa82fec", "size_in_bytes": 15998}, {"_path": "Library/include/webp/encode.h", "path_type": "hardlink", "sha256": "009eb07d15f117b7e26484d0bc0c2a6a571ba378c7ddac0ea4322596167aeab1", "sha256_in_prefix": "009eb07d15f117b7e26484d0bc0c2a6a571ba378c7ddac0ea4322596167aeab1", "size_in_bytes": 28480}, {"_path": "Library/include/webp/mux.h", "path_type": "hardlink", "sha256": "c72cf593f8194c671efcade33f1678294380120d45a337511d612a3acb643f35", "sha256_in_prefix": "c72cf593f8194c671efcade33f1678294380120d45a337511d612a3acb643f35", "size_in_bytes": 25966}, {"_path": "Library/include/webp/mux_types.h", "path_type": "hardlink", "sha256": "d3313ad38c26d17abd244ab683e662dedead1489ec0f910bc31eba7ce2f8838c", "sha256_in_prefix": "d3313ad38c26d17abd244ab683e662dedead1489ec0f910bc31eba7ce2f8838c", "size_in_bytes": 3259}, {"_path": "Library/include/webp/sharpyuv/sharpyuv.h", "path_type": "hardlink", "sha256": "c55431721d8800d9a9db37e3851f145f5d4402d3881645ea3a28e4ac503e0ec6", "sha256_in_prefix": "c55431721d8800d9a9db37e3851f145f5d4402d3881645ea3a28e4ac503e0ec6", "size_in_bytes": 7669}, {"_path": "Library/include/webp/sharpyuv/sharpyuv_csp.h", "path_type": "hardlink", "sha256": "80c23c727edb1ce6a42b38f9458c3d75c572856c898f0cd913ba03468afb9d3a", "sha256_in_prefix": "80c23c727edb1ce6a42b38f9458c3d75c572856c898f0cd913ba03468afb9d3a", "size_in_bytes": 2152}, {"_path": "Library/include/webp/types.h", "path_type": "hardlink", "sha256": "5ec7cd176fa921379ae463cecb2ab1616068031e58ad705f51c1f3bbc7d70aa3", "sha256_in_prefix": "5ec7cd176fa921379ae463cecb2ab1616068031e58ad705f51c1f3bbc7d70aa3", "size_in_bytes": 3133}, {"_path": "Library/lib/libsharpyuv.lib", "path_type": "hardlink", "sha256": "f6497aa2d3c3b60247a09862f0d4105c0e5089e1eaf3110273868169a580a122", "sha256_in_prefix": "f6497aa2d3c3b60247a09862f0d4105c0e5089e1eaf3110273868169a580a122", "size_in_bytes": 4780}, {"_path": "Library/lib/libwebp.lib", "path_type": "hardlink", "sha256": "d33532db716262ebe89c4321880d0a04dc994a437ae9508ee5c3cd965392c265", "sha256_in_prefix": "d33532db716262ebe89c4321880d0a04dc994a437ae9508ee5c3cd965392c265", "size_in_bytes": 85908}, {"_path": "Library/lib/libwebpdecoder.lib", "path_type": "hardlink", "sha256": "64855d7b48d2c00c3cfba0758bbedef1d5b7af8682f21ceaff7b2b45ed94c32c", "sha256_in_prefix": "64855d7b48d2c00c3cfba0758bbedef1d5b7af8682f21ceaff7b2b45ed94c32c", "size_in_bytes": 48576}, {"_path": "Library/lib/libwebpdemux.lib", "path_type": "hardlink", "sha256": "81a871c16ab4d3bea7d5d8d357a37885a432b59fe8ef39a015b05c07ebe137dd", "sha256_in_prefix": "81a871c16ab4d3bea7d5d8d357a37885a432b59fe8ef39a015b05c07ebe137dd", "size_in_bytes": 6384}, {"_path": "Library/lib/libwebpmux.lib", "path_type": "hardlink", "sha256": "87c795ca4c47af8ef46a0a1b37c8990aa2a33e01a3780688d3d9ad160312b5ba", "sha256_in_prefix": "87c795ca4c47af8ef46a0a1b37c8990aa2a33e01a3780688d3d9ad160312b5ba", "size_in_bytes": 14586}, {"_path": "Library/lib/pkgconfig/libsharpyuv.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "D:/bld/libwebp-base_1734777333739/_h_env", "sha256": "2d1eb99d68f8c9e526e03b57ca985e9207efddf9f299f9211a1f1c8919f45259", "sha256_in_prefix": "6b721d1e020c4fb4cb547218375f5380608d61590e63b156eb4bed8882ebe200", "size_in_bytes": 309}, {"_path": "Library/lib/pkgconfig/libwebp.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "D:/bld/libwebp-base_1734777333739/_h_env", "sha256": "08a35291e1da38a8d61f61b135e6bb5ce3a30ea0d38867b82756d91db78abb6f", "sha256_in_prefix": "3c195a760fab4d97cf6f6cc3602020f367704fdd781c225e5ceb6ea98412f6f7", "size_in_bytes": 324}, {"_path": "Library/lib/pkgconfig/libwebpdecoder.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "D:/bld/libwebp-base_1734777333739/_h_env", "sha256": "f63641c59546cf2c05ada6f998c1f52482e764ae2afc54db1fc39d9646cb089b", "sha256_in_prefix": "bcb3c31becbed4d4f0e430799c037c11e8f79115c66bce32e6304d8eed66a9dd", "size_in_bytes": 321}, {"_path": "Library/lib/pkgconfig/libwebpdemux.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "D:/bld/libwebp-base_1734777333739/_h_env", "sha256": "c45e8da6e792e396fe67f723811f911b652d1f25adcc30bc08f98d0db35084cf", "sha256_in_prefix": "0be88d137422e0c5b731e4938778ebf62a79979f7ff6e45d1bd4be2f02b89227", "size_in_bytes": 339}, {"_path": "Library/lib/pkgconfig/libwebpmux.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "D:/bld/libwebp-base_1734777333739/_h_env", "sha256": "9ba4279062963e63597e3fa2f32d973231654794acb7236784d901a329b8a847", "sha256_in_prefix": "8dda60529f4a71c1f6e68e9662e3823a47442c8a883f5391f7cb3cf0c2a52811", "size_in_bytes": 356}], "paths_version": 1}, "requested_spec": "None", "sha256": "1d75274614e83a5750b8b94f7bad2fc0564c2312ff407e697d99152ed095576f", "size": 273661, "subdir": "win-64", "timestamp": 1734777665000, "url": "https://conda.anaconda.org/conda-forge/win-64/libwebp-base-1.5.0-h3b0e114_0.conda", "version": "1.5.0"}