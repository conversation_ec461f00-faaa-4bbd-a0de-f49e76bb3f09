{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-nvml-dev-12.4.127-hd77b12b_1", "files": ["Library/include/nvml.h", "Library/lib/nvml.lib"], "fn": "cuda-nvml-dev-12.4.127-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-nvml-dev-12.4.127-hd77b12b_1", "type": 1}, "md5": "599d6c9f86c0b2d562994039cee17ec7", "name": "cuda-nvml-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-nvml-dev-12.4.127-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/include/nvml.h", "path_type": "hardlink", "sha256": "899f3c7d6d5ead655d32d0bca566ecf1a32039067ccf45400f4341db9a18b04f", "sha256_in_prefix": "899f3c7d6d5ead655d32d0bca566ecf1a32039067ccf45400f4341db9a18b04f", "size_in_bytes": 589454}, {"_path": "Library/lib/nvml.lib", "path_type": "hardlink", "sha256": "1d0f245dae4bffb721eb71ac787b242ebf642ebced48783b5304c00cb7f881ba", "sha256_in_prefix": "1d0f245dae4bffb721eb71ac787b242ebf642ebced48783b5304c00cb7f881ba", "size_in_bytes": 95280}], "paths_version": 1}, "requested_spec": "None", "sha256": "d8eec25cf718c9ea1bb925ee89fc5cf5bc8c671eeacc53e3791a42412e9425d2", "size": 108325, "subdir": "win-64", "timestamp": 1714772026000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-nvml-dev-12.4.127-hd77b12b_1.conda", "version": "12.4.127"}