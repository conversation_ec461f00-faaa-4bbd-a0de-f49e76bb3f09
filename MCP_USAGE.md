# BEN2 MCP Server Usage Guide

This project now supports the Model Context Protocol (MCP), allowing AI clients to use the background removal capabilities as tools.

## What is MCP?

Model Context Protocol (MCP) is a standardized way for AI applications to access external tools and data sources. With MCP support, this BEN2 background removal service can be used by AI assistants like <PERSON>, ChatGPT, or other MCP-compatible clients.

## Available MCP Tools

When running as an MCP server, BEN2 exposes the following tools:

### 1. `fn` - Image Background Removal
- **Description**: Remove background from an image and return the foreground
- **Input**: Image file path, URL, or PIL Image object
- **Output**: Processed foreground image and saved file path

### 2. `process_video` - Video Background Removal
- **Description**: Remove background from a video and return the processed video
- **Input**: Path or URL to the input video file
- **Output**: Path to the processed video file with background removed

### 3. `process_file` - File Processing
- **Description**: Process a file and save the result with background removed
- **Input**: Path or URL to the input image file
- **Output**: Path to the processed image file with background removed

## Setup Instructions

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Start the MCP Server
```bash
python app.py
```

The server will start on `http://localhost:7860` and the MCP endpoint will be available at:
`http://localhost:7860/gradio_api/mcp/sse`

### 3. Configure MCP Client

#### For Cursor or other MCP clients:
Add the following configuration to your MCP client settings:

```json
{
  "mcpServers": {
    "ben2-background-removal": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "http://localhost:7860/gradio_api/mcp/sse"
      ]
    }
  }
}
```

#### Alternative configuration (if SSE is not supported):
```json
{
  "mcpServers": {
    "ben2-background-removal": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "http://localhost:7860/gradio_api/mcp/sse"
      ]
    }
  }
}
```

## Usage Examples

Once configured, you can ask your AI assistant to:

- "Remove the background from this image: [image URL]"
- "Process this video to remove the background: [video URL]"
- "Clean up the background in this photo"

The AI assistant will use the BEN2 MCP tools to process your images and videos.

## Hosting on Hugging Face Spaces

You can deploy this as a free hosted MCP server on Hugging Face Spaces:

1. Create a new Space on Hugging Face
2. Upload your code
3. The MCP server will be available at: `https://your-username-space-name.hf.space/gradio_api/mcp/sse`

## Troubleshooting

- **Connection Issues**: Restart both the MCP client and server
- **File Handling**: Use full URLs (http:// or https://) for best compatibility
- **SSE Support**: If your MCP client doesn't support SSE, use the mcp-remote configuration shown above

## API Schema

To view the complete MCP tool schemas, visit:
`http://localhost:7860/gradio_api/mcp/schema`

Or click "View API" in the Gradio interface footer, then click "MCP".
