# BEN2 MCP服务器使用指南

本项目现已支持模型上下文协议（MCP），允许AI客户端将背景移除功能作为工具使用。

## 什么是MCP？

模型上下文协议（MCP）是AI应用程序访问外部工具和数据源的标准化方式。通过MCP支持，这个BEN2背景移除服务可以被Claude、ChatGPT或其他兼容MCP的AI助手使用。

## 可用的MCP工具

作为MCP服务器运行时，BEN2提供以下工具：

### 1. `fn` - 图像背景移除
- **描述**：从图像中移除背景并返回前景
- **输入**：图像文件路径、URL或PIL图像对象
- **输出**：处理后的前景图像和保存的文件路径

### 2. `process_video` - 视频背景移除
- **描述**：从视频中移除背景并返回处理后的视频
- **输入**：输入视频文件的路径或URL
- **输出**：移除背景后的处理视频文件路径

### 3. `process_file` - 文件处理
- **描述**：处理文件并保存移除背景后的结果
- **输入**：输入图像文件的路径或URL
- **输出**：移除背景后的处理图像文件路径

## 设置说明

### 1. 安装依赖

**方法一：自动安装（推荐）**
```bash
python install_mcp.py
```

**方法二：手动安装**
```bash
# 安装基础依赖
pip install -r requirements.txt

# 安装MCP支持的Gradio版本
pip install "gradio[mcp]"

# 安装其他必需模块
pip install spaces loadimg einops timm
```

**注意**：
- 确保安装支持MCP的Gradio版本（5.0+）
- 如果当前版本不支持MCP，应用会自动回退到标准模式
- 可能需要解决一些依赖冲突，特别是wandb相关的问题

### 2. 启动MCP服务器
```bash
python app.py
```

服务器将在 `http://localhost:7860` 启动，MCP端点将在以下地址可用：
`http://localhost:7860/gradio_api/mcp/sse`

### 3. 故障排除

**常见问题：**

1. **ModuleNotFoundError**: 运行 `python install_mcp.py` 自动安装缺失模块
2. **wandb错误**: 尝试 `pip install --upgrade wandb` 或 `pip uninstall wandb`
3. **Gradio版本不支持MCP**: 应用会自动使用标准模式，功能不受影响
4. **CUDA相关错误**: 确保安装了正确的PyTorch版本

### 3. 配置MCP客户端

#### 对于Cursor或其他MCP客户端：
将以下配置添加到您的MCP客户端设置中：

```json
{
  "mcpServers": {
    "ben2-background-removal": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "http://localhost:7860/gradio_api/mcp/sse"
      ]
    }
  }
}
```

#### 替代配置（如果不支持SSE）：
```json
{
  "mcpServers": {
    "ben2-background-removal": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "http://localhost:7860/gradio_api/mcp/sse"
      ]
    }
  }
}
```

## 使用示例

配置完成后，您可以要求AI助手：

- "从这张图片中移除背景：[图片URL]"
- "处理这个视频以移除背景：[视频URL]"
- "清理这张照片的背景"

AI助手将使用BEN2 MCP工具来处理您的图像和视频。

## 在Hugging Face Spaces上托管

您可以在Hugging Face Spaces上免费部署此MCP服务器：

1. 在Hugging Face上创建新的Space
2. 上传您的代码
3. MCP服务器将在以下地址可用：`https://your-username-space-name.hf.space/gradio_api/mcp/sse`

## 故障排除

- **连接问题**：重启MCP客户端和服务器
- **文件处理**：为了最佳兼容性，请使用完整URL（http://或https://）
- **SSE支持**：如果您的MCP客户端不支持SSE，请使用上面显示的mcp-remote配置
- **版本兼容性**：如果Gradio版本不支持MCP，应用会自动使用标准模式

## API架构

要查看完整的MCP工具架构，请访问：
`http://localhost:7860/gradio_api/mcp/schema`

或在Gradio界面页脚点击"View API"，然后点击"MCP"。
