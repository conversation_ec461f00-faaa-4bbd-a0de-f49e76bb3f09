{"build": "py311haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["python >=3.11,<3.12.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\wheel-0.44.0-py311haa95532_0", "files": ["Lib/site-packages/wheel-0.44.0.dist-info/LICENSE.txt", "Lib/site-packages/wheel-0.44.0.dist-info/METADATA", "Lib/site-packages/wheel-0.44.0.dist-info/RECORD", "Lib/site-packages/wheel-0.44.0.dist-info/WHEEL", "Lib/site-packages/wheel-0.44.0.dist-info/entry_points.txt", "Lib/site-packages/wheel/__init__.py", "Lib/site-packages/wheel/__main__.py", "Lib/site-packages/wheel/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/wheel/__pycache__/__main__.cpython-311.pyc", "Lib/site-packages/wheel/__pycache__/_bdist_wheel.cpython-311.pyc", "Lib/site-packages/wheel/__pycache__/_setuptools_logging.cpython-311.pyc", "Lib/site-packages/wheel/__pycache__/bdist_wheel.cpython-311.pyc", "Lib/site-packages/wheel/__pycache__/macosx_libfile.cpython-311.pyc", "Lib/site-packages/wheel/__pycache__/metadata.cpython-311.pyc", "Lib/site-packages/wheel/__pycache__/util.cpython-311.pyc", "Lib/site-packages/wheel/__pycache__/wheelfile.cpython-311.pyc", "Lib/site-packages/wheel/_bdist_wheel.py", "Lib/site-packages/wheel/_setuptools_logging.py", "Lib/site-packages/wheel/bdist_wheel.py", "Lib/site-packages/wheel/cli/__init__.py", "Lib/site-packages/wheel/cli/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/wheel/cli/__pycache__/convert.cpython-311.pyc", "Lib/site-packages/wheel/cli/__pycache__/pack.cpython-311.pyc", "Lib/site-packages/wheel/cli/__pycache__/tags.cpython-311.pyc", "Lib/site-packages/wheel/cli/__pycache__/unpack.cpython-311.pyc", "Lib/site-packages/wheel/cli/convert.py", "Lib/site-packages/wheel/cli/pack.py", "Lib/site-packages/wheel/cli/tags.py", "Lib/site-packages/wheel/cli/unpack.py", "Lib/site-packages/wheel/macosx_libfile.py", "Lib/site-packages/wheel/metadata.py", "Lib/site-packages/wheel/util.py", "Lib/site-packages/wheel/vendored/__init__.py", "Lib/site-packages/wheel/vendored/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/wheel/vendored/packaging/__init__.py", "Lib/site-packages/wheel/vendored/packaging/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_elffile.cpython-311.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_manylinux.cpython-311.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_musllinux.cpython-311.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_parser.cpython-311.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_structures.cpython-311.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-311.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/markers.cpython-311.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/requirements.cpython-311.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/specifiers.cpython-311.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/tags.cpython-311.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/utils.cpython-311.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/version.cpython-311.pyc", "Lib/site-packages/wheel/vendored/packaging/_elffile.py", "Lib/site-packages/wheel/vendored/packaging/_manylinux.py", "Lib/site-packages/wheel/vendored/packaging/_musllinux.py", "Lib/site-packages/wheel/vendored/packaging/_parser.py", "Lib/site-packages/wheel/vendored/packaging/_structures.py", "Lib/site-packages/wheel/vendored/packaging/_tokenizer.py", "Lib/site-packages/wheel/vendored/packaging/markers.py", "Lib/site-packages/wheel/vendored/packaging/requirements.py", "Lib/site-packages/wheel/vendored/packaging/specifiers.py", "Lib/site-packages/wheel/vendored/packaging/tags.py", "Lib/site-packages/wheel/vendored/packaging/utils.py", "Lib/site-packages/wheel/vendored/packaging/version.py", "Lib/site-packages/wheel/vendored/vendor.txt", "Lib/site-packages/wheel/wheelfile.py", "Scripts/wheel-script.py", "Scripts/wheel.exe"], "fn": "wheel-0.44.0-py311haa95532_0.conda", "license": "MIT", "link": {"source": "D:\\anaconda3\\pkgs\\wheel-0.44.0-py311haa95532_0", "type": 1}, "md5": "3ce7dda251c59835fb9a00b58cd44952", "name": "wheel", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\wheel-0.44.0-py311haa95532_0.conda", "paths_data": {"paths": [{"_path": "Lib/site-packages/wheel-0.44.0.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "sha256_in_prefix": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "size_in_bytes": 1107}, {"_path": "Lib/site-packages/wheel-0.44.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "3a3c69baae37bab03a835fa8b8a3128f08d69fb513345812beab7c6e5afee041", "sha256_in_prefix": "3a3c69baae37bab03a835fa8b8a3128f08d69fb513345812beab7c6e5afee041", "size_in_bytes": 2313}, {"_path": "Lib/site-packages/wheel-0.44.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "9b48174d9d65daf5fa1fa26508dab82445393924e77f4bbc7b27c2c3f113af97", "sha256_in_prefix": "9b48174d9d65daf5fa1fa26508dab82445393924e77f4bbc7b27c2c3f113af97", "size_in_bytes": 2908}, {"_path": "Lib/site-packages/wheel-0.44.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "Lib/site-packages/wheel-0.44.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "sha256_in_prefix": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "size_in_bytes": 104}, {"_path": "Lib/site-packages/wheel/__init__.py", "path_type": "hardlink", "sha256": "1b474a6c75845852460e464822eda21682713f4a68534da542077a524a82f9a0", "sha256_in_prefix": "1b474a6c75845852460e464822eda21682713f4a68534da542077a524a82f9a0", "size_in_bytes": 59}, {"_path": "Lib/site-packages/wheel/__main__.py", "path_type": "hardlink", "sha256": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "sha256_in_prefix": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "size_in_bytes": 455}, {"_path": "Lib/site-packages/wheel/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "307514e87445f19753a1dd0f311b19df4ede4f9d502faa29d7c357c4c692097e", "sha256_in_prefix": "307514e87445f19753a1dd0f311b19df4ede4f9d502faa29d7c357c4c692097e", "size_in_bytes": 232}, {"_path": "Lib/site-packages/wheel/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "d8f56b21af6794a6054107c1b890e1d8e2d7eebbce2ebab3ec86f3f09a5a16c4", "sha256_in_prefix": "d8f56b21af6794a6054107c1b890e1d8e2d7eebbce2ebab3ec86f3f09a5a16c4", "size_in_bytes": 1037}, {"_path": "Lib/site-packages/wheel/__pycache__/_bdist_wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "56c548508c0c7847e7da9e0bebfdfafdcdea01ac3a150f4d1691443b3622347c", "sha256_in_prefix": "56c548508c0c7847e7da9e0bebfdfafdcdea01ac3a150f4d1691443b3622347c", "size_in_bytes": 28232}, {"_path": "Lib/site-packages/wheel/__pycache__/_setuptools_logging.cpython-311.pyc", "path_type": "hardlink", "sha256": "1df7244c978fa4d8f19f014c7347c97eccbba26a083edb82a75bc7a80c8d7385", "sha256_in_prefix": "1df7244c978fa4d8f19f014c7347c97eccbba26a083edb82a75bc7a80c8d7385", "size_in_bytes": 1449}, {"_path": "Lib/site-packages/wheel/__pycache__/bdist_wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "5858c5b23a886d1d06ff57e26b282450a1f322b4aff284f260ffe4b1c35af43b", "sha256_in_prefix": "5858c5b23a886d1d06ff57e26b282450a1f322b4aff284f260ffe4b1c35af43b", "size_in_bytes": 597}, {"_path": "Lib/site-packages/wheel/__pycache__/macosx_libfile.cpython-311.pyc", "path_type": "hardlink", "sha256": "5b7a90857db7aba8b5fa215f7ef504127e3607a98eb2eb55a8d425d26229f088", "sha256_in_prefix": "5b7a90857db7aba8b5fa215f7ef504127e3607a98eb2eb55a8d425d26229f088", "size_in_bytes": 17792}, {"_path": "Lib/site-packages/wheel/__pycache__/metadata.cpython-311.pyc", "path_type": "hardlink", "sha256": "2fa7bfac3811f2d12bef581a81b83900d19b194b75f2c43127c5ae1c0aba7db4", "sha256_in_prefix": "2fa7bfac3811f2d12bef581a81b83900d19b194b75f2c43127c5ae1c0aba7db4", "size_in_bytes": 9687}, {"_path": "Lib/site-packages/wheel/__pycache__/util.cpython-311.pyc", "path_type": "hardlink", "sha256": "df37af4d42ba98cbb74184d711d2fb56f297d1caa76b1d58f09db59d238c7757", "sha256_in_prefix": "df37af4d42ba98cbb74184d711d2fb56f297d1caa76b1d58f09db59d238c7757", "size_in_bytes": 1286}, {"_path": "Lib/site-packages/wheel/__pycache__/wheelfile.cpython-311.pyc", "path_type": "hardlink", "sha256": "0d5fecf6f86e47121851e560c568e7a09c53087114a0f0306413bf974829c38a", "sha256_in_prefix": "0d5fecf6f86e47121851e560c568e7a09c53087114a0f0306413bf974829c38a", "size_in_bytes": 12531}, {"_path": "Lib/site-packages/wheel/_bdist_wheel.py", "path_type": "hardlink", "sha256": "d32e284a36db98350c0e256b84d8cd64e394ed34fce5839b32248aed3f916126", "sha256_in_prefix": "d32e284a36db98350c0e256b84d8cd64e394ed34fce5839b32248aed3f916126", "size_in_bytes": 21496}, {"_path": "Lib/site-packages/wheel/_setuptools_logging.py", "path_type": "hardlink", "sha256": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "sha256_in_prefix": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "size_in_bytes": 781}, {"_path": "Lib/site-packages/wheel/bdist_wheel.py", "path_type": "hardlink", "sha256": "f9b5159a79fb4468dc19b8ef9f94284331514e23adf994c5cb86ec23582b0b85", "sha256_in_prefix": "f9b5159a79fb4468dc19b8ef9f94284331514e23adf994c5cb86ec23582b0b85", "size_in_bytes": 376}, {"_path": "Lib/site-packages/wheel/cli/__init__.py", "path_type": "hardlink", "sha256": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "sha256_in_prefix": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "size_in_bytes": 4402}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "a22e362734d53c4df4b15f4ae7fb02b82797ff61ca51210ef2bb90475a3aa96d", "sha256_in_prefix": "a22e362734d53c4df4b15f4ae7fb02b82797ff61ca51210ef2bb90475a3aa96d", "size_in_bytes": 7755}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/convert.cpython-311.pyc", "path_type": "hardlink", "sha256": "abb7883c2eaf35d19c0be6446e1f1d2ae9a6a17329c0d1742739669ea9b92d00", "sha256_in_prefix": "abb7883c2eaf35d19c0be6446e1f1d2ae9a6a17329c0d1742739669ea9b92d00", "size_in_bytes": 12312}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/pack.cpython-311.pyc", "path_type": "hardlink", "sha256": "a7a0963a0b09d5cd96ed0e3874c82f37d5c028846c4f87bcbbdb772654a5af02", "sha256_in_prefix": "a7a0963a0b09d5cd96ed0e3874c82f37d5c028846c4f87bcbbdb772654a5af02", "size_in_bytes": 5813}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/tags.cpython-311.pyc", "path_type": "hardlink", "sha256": "c83b7b9d551f91253d93c26b7953baca8ff6ed6521bd282c1df55da2c976ef2e", "sha256_in_prefix": "c83b7b9d551f91253d93c26b7953baca8ff6ed6521bd282c1df55da2c976ef2e", "size_in_bytes": 7909}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/unpack.cpython-311.pyc", "path_type": "hardlink", "sha256": "f83727fed3dbbc1a42405c07df3115ecc936022ab9c99984d9cb0b07329a50f4", "sha256_in_prefix": "f83727fed3dbbc1a42405c07df3115ecc936022ab9c99984d9cb0b07329a50f4", "size_in_bytes": 1737}, {"_path": "Lib/site-packages/wheel/cli/convert.py", "path_type": "hardlink", "sha256": "c4d9f28a31ebc4dd3acddb5e41f338850d9f48fa20980c0226c0281c8075f412", "sha256_in_prefix": "c4d9f28a31ebc4dd3acddb5e41f338850d9f48fa20980c0226c0281c8075f412", "size_in_bytes": 9512}, {"_path": "Lib/site-packages/wheel/cli/pack.py", "path_type": "hardlink", "sha256": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "sha256_in_prefix": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "size_in_bytes": 3103}, {"_path": "Lib/site-packages/wheel/cli/tags.py", "path_type": "hardlink", "sha256": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "sha256_in_prefix": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "size_in_bytes": 4760}, {"_path": "Lib/site-packages/wheel/cli/unpack.py", "path_type": "hardlink", "sha256": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "sha256_in_prefix": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "size_in_bytes": 1021}, {"_path": "Lib/site-packages/wheel/macosx_libfile.py", "path_type": "hardlink", "sha256": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "sha256_in_prefix": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "size_in_bytes": 16572}, {"_path": "Lib/site-packages/wheel/metadata.py", "path_type": "hardlink", "sha256": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "sha256_in_prefix": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "size_in_bytes": 6171}, {"_path": "Lib/site-packages/wheel/util.py", "path_type": "hardlink", "sha256": "7b48e99ec6db33d42169a312c9aa7efd9814c5cc70a722c393a44772b76e3cb8", "sha256_in_prefix": "7b48e99ec6db33d42169a312c9aa7efd9814c5cc70a722c393a44772b76e3cb8", "size_in_bytes": 621}, {"_path": "Lib/site-packages/wheel/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/wheel/vendored/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "af6aea05455cff912857c2f038b8b6ee5b1e196ec1663aaa6894a64ffff9717d", "sha256_in_prefix": "af6aea05455cff912857c2f038b8b6ee5b1e196ec1663aaa6894a64ffff9717d", "size_in_bytes": 153}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "ce76f172e0075c3e4c6d80d2c49bcbb10341fabd31979a1fd4526c77490bbe8c", "sha256_in_prefix": "ce76f172e0075c3e4c6d80d2c49bcbb10341fabd31979a1fd4526c77490bbe8c", "size_in_bytes": 163}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_elffile.cpython-311.pyc", "path_type": "hardlink", "sha256": "d799074ba43a77862764028c48f834ee51169b6eb76f834727fb97737e5301b6", "sha256_in_prefix": "d799074ba43a77862764028c48f834ee51169b6eb76f834727fb97737e5301b6", "size_in_bytes": 5443}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_manylinux.cpython-311.pyc", "path_type": "hardlink", "sha256": "20ca1e89f191ba8abba673bc2a88d22f35b901ee1860537bfd79ccd69a6ec5aa", "sha256_in_prefix": "20ca1e89f191ba8abba673bc2a88d22f35b901ee1860537bfd79ccd69a6ec5aa", "size_in_bytes": 11039}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_musllinux.cpython-311.pyc", "path_type": "hardlink", "sha256": "d1fad4caf3989a9b27fa3b56d0241bcc1f16857e9a2333e5828bc30a1d7d6447", "sha256_in_prefix": "d1fad4caf3989a9b27fa3b56d0241bcc1f16857e9a2333e5828bc30a1d7d6447", "size_in_bytes": 5254}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_parser.cpython-311.pyc", "path_type": "hardlink", "sha256": "8811556b201fac7ab61febf42388402555da859463683bd81a4f56163d704b08", "sha256_in_prefix": "8811556b201fac7ab61febf42388402555da859463683bd81a4f56163d704b08", "size_in_bytes": 16277}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_structures.cpython-311.pyc", "path_type": "hardlink", "sha256": "74d0153d11ee1a0c56de392c97b12e68e19a24e025f28eece3dec2e1b36efc45", "sha256_in_prefix": "74d0153d11ee1a0c56de392c97b12e68e19a24e025f28eece3dec2e1b36efc45", "size_in_bytes": 3647}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-311.pyc", "path_type": "hardlink", "sha256": "11b7010a0511c93716c7ce7f9f7e05495ee6e1f4c871996937103bbcc9d02a3e", "sha256_in_prefix": "11b7010a0511c93716c7ce7f9f7e05495ee6e1f4c871996937103bbcc9d02a3e", "size_in_bytes": 8624}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/markers.cpython-311.pyc", "path_type": "hardlink", "sha256": "f7ca97189f3f9fb0ceb59160d95f964fb3166e71f7de26923260c2f9b9664cf5", "sha256_in_prefix": "f7ca97189f3f9fb0ceb59160d95f964fb3166e71f7de26923260c2f9b9664cf5", "size_in_bytes": 12012}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/requirements.cpython-311.pyc", "path_type": "hardlink", "sha256": "5c4f89919c05d31b2520ac666a9b3df21c7832eff035ddc11258881afbd25845", "sha256_in_prefix": "5c4f89919c05d31b2520ac666a9b3df21c7832eff035ddc11258881afbd25845", "size_in_bytes": 4682}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/specifiers.cpython-311.pyc", "path_type": "hardlink", "sha256": "6f19271836e38069b32e9eb0bde5fad2bde6387cdd644f41b55fd4c66dfe2b6b", "sha256_in_prefix": "6f19271836e38069b32e9eb0bde5fad2bde6387cdd644f41b55fd4c66dfe2b6b", "size_in_bytes": 41997}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/tags.cpython-311.pyc", "path_type": "hardlink", "sha256": "dcc1137a38d8d36ebf6967b6780744757e2ada18942b5664447242315dd30df7", "sha256_in_prefix": "dcc1137a38d8d36ebf6967b6780744757e2ada18942b5664447242315dd30df7", "size_in_bytes": 24593}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "1dba9da0dc72023797bd8bfa85de51cfdc7454445ae844977c66780acf36e79c", "sha256_in_prefix": "1dba9da0dc72023797bd8bfa85de51cfdc7454445ae844977c66780acf36e79c", "size_in_bytes": 8238}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/version.cpython-311.pyc", "path_type": "hardlink", "sha256": "448ea7ec15b9cd29a1c9aacae39b5e138890653ecf7e0efdbf0a450805eb6d91", "sha256_in_prefix": "448ea7ec15b9cd29a1c9aacae39b5e138890653ecf7e0efdbf0a450805eb6d91", "size_in_bytes": 21415}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_elffile.py", "path_type": "hardlink", "sha256": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "sha256_in_prefix": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "size_in_bytes": 3266}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "sha256_in_prefix": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "size_in_bytes": 9588}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "sha256_in_prefix": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "size_in_bytes": 2674}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_parser.py", "path_type": "hardlink", "sha256": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "sha256_in_prefix": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "size_in_bytes": 10347}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "sha256_in_prefix": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "size_in_bytes": 5292}, {"_path": "Lib/site-packages/wheel/vendored/packaging/markers.py", "path_type": "hardlink", "sha256": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "sha256_in_prefix": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "size_in_bytes": 8232}, {"_path": "Lib/site-packages/wheel/vendored/packaging/requirements.py", "path_type": "hardlink", "sha256": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "sha256_in_prefix": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "size_in_bytes": 2933}, {"_path": "Lib/site-packages/wheel/vendored/packaging/specifiers.py", "path_type": "hardlink", "sha256": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "sha256_in_prefix": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "size_in_bytes": 39778}, {"_path": "Lib/site-packages/wheel/vendored/packaging/tags.py", "path_type": "hardlink", "sha256": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "sha256_in_prefix": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "size_in_bytes": 18950}, {"_path": "Lib/site-packages/wheel/vendored/packaging/utils.py", "path_type": "hardlink", "sha256": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "sha256_in_prefix": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "size_in_bytes": 5268}, {"_path": "Lib/site-packages/wheel/vendored/packaging/version.py", "path_type": "hardlink", "sha256": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "sha256_in_prefix": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "size_in_bytes": 16234}, {"_path": "Lib/site-packages/wheel/vendored/vendor.txt", "path_type": "hardlink", "sha256": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "sha256_in_prefix": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "size_in_bytes": 16}, {"_path": "Lib/site-packages/wheel/wheelfile.py", "path_type": "hardlink", "sha256": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "sha256_in_prefix": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "size_in_bytes": 8411}, {"_path": "Scripts/wheel-script.py", "path_type": "hardlink", "sha256": "f81e86d226fd97f3a9fb708b803859f8922740e5b62a4577b0b6b8b1d6b8e333", "sha256_in_prefix": "f81e86d226fd97f3a9fb708b803859f8922740e5b62a4577b0b6b8b1d6b8e333", "size_in_bytes": 203}, {"_path": "Scripts/wheel.exe", "path_type": "hardlink", "sha256": "f24d102084620e54fe68d07f6e9169118b283f8d33d0c8f4b974c2e05a306059", "sha256_in_prefix": "f24d102084620e54fe68d07f6e9169118b283f8d33d0c8f4b974c2e05a306059", "size_in_bytes": 54032}], "paths_version": 1}, "requested_spec": "None", "sha256": "2e13a80d06e54e97a5bd9c927a3b1e5d9f82b2be8b6c63f269fa64021973a205", "size": 177138, "subdir": "win-64", "timestamp": 1726165294000, "url": "https://repo.anaconda.com/pkgs/main/win-64/wheel-0.44.0-py311haa95532_0.conda", "version": "0.44.0"}