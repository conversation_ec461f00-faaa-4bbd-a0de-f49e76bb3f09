{"build": "hd77b12b_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0", "cuda-cu<PERSON><PERSON>_win-64 12.4.127 hd77b12b_0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-cudart-12.4.127-hd77b12b_0", "files": ["Library/bin/cudart64_12.dll"], "fn": "cuda-cudart-12.4.127-hd77b12b_0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-cudart-12.4.127-hd77b12b_0", "type": 1}, "md5": "1e47427308eb8dbc5de40e7197959daa", "name": "cuda-cudart", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-cudart-12.4.127-hd77b12b_0.conda", "paths_data": {"paths": [{"_path": "Library/bin/cudart64_12.dll", "path_type": "hardlink", "sha256": "d28e42265da7462162a54da6b7a99ea4fa2caf8139d862bb500db875d0b32dfc", "sha256_in_prefix": "d28e42265da7462162a54da6b7a99ea4fa2caf8139d862bb500db875d0b32dfc", "size_in_bytes": 553984}], "paths_version": 1}, "requested_spec": "None", "sha256": "2d9edd03a31fb78f87b250e92edae7e6574286798c553b3e632f3f7cfbb92d17", "size": 183394, "subdir": "win-64", "timestamp": 1714768740000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-cudart-12.4.127-hd77b12b_0.conda", "version": "12.4.127"}