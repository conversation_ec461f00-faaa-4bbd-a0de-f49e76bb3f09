{"build": "py311h5da7b33_9", "build_number": 9, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["python >=3.11,<3.12.0a0", "vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\brotli-python-1.0.9-py311h5da7b33_9", "files": ["Lib/site-packages/Brotli-1.0.9.dist-info/INSTALLER", "Lib/site-packages/Brotli-1.0.9.dist-info/LICENSE", "Lib/site-packages/Brotli-1.0.9.dist-info/METADATA", "Lib/site-packages/Brotli-1.0.9.dist-info/RECORD", "Lib/site-packages/Brotli-1.0.9.dist-info/REQUESTED", "Lib/site-packages/Brotli-1.0.9.dist-info/WHEEL", "Lib/site-packages/Brotli-1.0.9.dist-info/direct_url.json", "Lib/site-packages/Brotli-1.0.9.dist-info/top_level.txt", "Lib/site-packages/__pycache__/brotli.cpython-311.pyc", "Lib/site-packages/__pycache__/brotli.cpython-313.pyc", "Lib/site-packages/_brotli.cp311-win_amd64.pyd", "Lib/site-packages/brotli.py"], "fn": "brotli-python-1.0.9-py311h5da7b33_9.conda", "license": "MIT", "link": {"source": "D:\\anaconda3\\pkgs\\brotli-python-1.0.9-py311h5da7b33_9", "type": 1}, "md5": "f1fd30e512e6e00e087ee852b4a1360f", "name": "brotli-python", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\brotli-python-1.0.9-py311h5da7b33_9.conda", "paths_data": {"paths": [{"_path": "Lib/site-packages/Brotli-1.0.9.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/Brotli-1.0.9.dist-info/LICENSE", "path_type": "hardlink", "sha256": "3d180008e36922a4e8daec11c34c7af264fed5962d07924aea928c38e8663c94", "sha256_in_prefix": "3d180008e36922a4e8daec11c34c7af264fed5962d07924aea928c38e8663c94", "size_in_bytes": 1084}, {"_path": "Lib/site-packages/Brotli-1.0.9.dist-info/METADATA", "path_type": "hardlink", "sha256": "400d6a032015afc65804af1878be279e020187b4056daec5528408aee28c805a", "sha256_in_prefix": "400d6a032015afc65804af1878be279e020187b4056daec5528408aee28c805a", "size_in_bytes": 1392}, {"_path": "Lib/site-packages/Brotli-1.0.9.dist-info/RECORD", "path_type": "hardlink", "sha256": "d8042ce9301e7b8649064b44f0e142a47367ce0c5e7d6124f776f77bf72c404b", "sha256_in_prefix": "d8042ce9301e7b8649064b44f0e142a47367ce0c5e7d6124f776f77bf72c404b", "size_in_bytes": 847}, {"_path": "Lib/site-packages/Brotli-1.0.9.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/Brotli-1.0.9.dist-info/WHEEL", "path_type": "hardlink", "sha256": "a96e110f5adf1e6f194548c96d75276470cd3c25c7b7a46ad26811f25bff2448", "sha256_in_prefix": "a96e110f5adf1e6f194548c96d75276470cd3c25c7b7a46ad26811f25bff2448", "size_in_bytes": 101}, {"_path": "Lib/site-packages/Brotli-1.0.9.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "36c4fbfe94325e61479cbf1d61c10d8dabdd69196ae6eddde103c6bf3e42ab8b", "sha256_in_prefix": "36c4fbfe94325e61479cbf1d61c10d8dabdd69196ae6eddde103c6bf3e42ab8b", "size_in_bytes": 92}, {"_path": "Lib/site-packages/Brotli-1.0.9.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "82c4b9e07ae13b766f785c5e32b2a8ffba87e129b84db43b8c62d5044a89e0d2", "sha256_in_prefix": "82c4b9e07ae13b766f785c5e32b2a8ffba87e129b84db43b8c62d5044a89e0d2", "size_in_bytes": 15}, {"_path": "Lib/site-packages/__pycache__/brotli.cpython-311.pyc", "path_type": "hardlink", "sha256": "7a747a1a2111b03a9235aa46ee3199c19591ac5ec10dff14b1a3b81c0abfa432", "sha256_in_prefix": "7a747a1a2111b03a9235aa46ee3199c19591ac5ec10dff14b1a3b81c0abfa432", "size_in_bytes": 1888}, {"_path": "Lib/site-packages/__pycache__/brotli.cpython-313.pyc", "path_type": "hardlink", "sha256": "df42345dc09c8fa6d0d257d7de95e6906e9cac312d7ec2aadb00c4068f715fcf", "sha256_in_prefix": "df42345dc09c8fa6d0d257d7de95e6906e9cac312d7ec2aadb00c4068f715fcf", "size_in_bytes": 1785}, {"_path": "Lib/site-packages/_brotli.cp311-win_amd64.pyd", "path_type": "hardlink", "sha256": "cfbab55efbe0ff10af77d69e2662366346cb4c61bb0989b5d4714e3eaaa2831f", "sha256_in_prefix": "cfbab55efbe0ff10af77d69e2662366346cb4c61bb0989b5d4714e3eaaa2831f", "size_in_bytes": 761104}, {"_path": "Lib/site-packages/brotli.py", "path_type": "hardlink", "sha256": "8b0a109cfbeb00db5a2584c9ea64a3390504347ed9f1180011b9fe1f1311fa45", "sha256_in_prefix": "8b0a109cfbeb00db5a2584c9ea64a3390504347ed9f1180011b9fe1f1311fa45", "size_in_bytes": 1857}], "paths_version": 1}, "requested_spec": "None", "sha256": "c5ebe00de0695f80eb6e6ea1261d7e1b9b3bcb8927d8874ecd2dc5766b24b508", "size": 354042, "subdir": "win-64", "timestamp": 1736183647000, "url": "https://repo.anaconda.com/pkgs/main/win-64/brotli-python-1.0.9-py311h5da7b33_9.conda", "version": "1.0.9"}