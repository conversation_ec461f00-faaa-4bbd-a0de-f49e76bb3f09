{"build": "hd77b12b_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/noarch", "constrains": [], "depends": ["cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-cudart_win-64-12.4.127-hd77b12b_0", "files": [], "fn": "cuda-cu<PERSON><PERSON>_win-64-12.4.127-hd77b12b_0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-cudart_win-64-12.4.127-hd77b12b_0", "type": 1}, "md5": "53a4925b2042da508e8ad406fd90de9c", "name": "cuda-cudar<PERSON>_win-64", "noarch": "generic", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-cudart_win-64-12.4.127-hd77b12b_0.conda", "package_type": "noarch_generic", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "cc31fbbbc591a055b8b3224c277b82853eeea7a3f3b69d62ae8a2bb116862c08", "size": 21802, "subdir": "noarch", "timestamp": 1714768671000, "url": "https://repo.anaconda.com/pkgs/main/noarch/cuda-cudart_win-64-12.4.127-hd77b12b_0.conda", "version": "12.4.127"}