{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0", "libnvjitlink >=12.4.127,<12.5.0a0", "libcublas >=12.4.5.8,<12.5.0a0", "libcusparse >=12.3.1.170,<12.4.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libcusolver-11.6.1.9-hd77b12b_1", "files": ["Library/bin/cusolver64_11.dll", "Library/bin/cusolverMg64_11.dll"], "fn": "libcusolver-11.6.1.9-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libcusolver-11.6.1.9-hd77b12b_1", "type": 1}, "md5": "c04c18accad62c013b9ad366978023a9", "name": "libcusolver", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libcusolver-11.6.1.9-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/bin/cusolver64_11.dll", "path_type": "hardlink", "sha256": "014c0326148fb0e78fc4cc6b50377e5353baabc153620e30ebb075319b8a40d2", "sha256_in_prefix": "014c0326148fb0e78fc4cc6b50377e5353baabc153620e30ebb075319b8a40d2", "size_in_bytes": 114384384}, {"_path": "Library/bin/cusolverMg64_11.dll", "path_type": "hardlink", "sha256": "de8f561c69ab46e94fbaae3d55d17d6aea42de142c6105622772f50d9b76a429", "sha256_in_prefix": "de8f561c69ab46e94fbaae3d55d17d6aea42de142c6105622772f50d9b76a429", "size_in_bytes": 77846016}], "paths_version": 1}, "requested_spec": "None", "sha256": "2aca8a9d41fa60f6a47072d5614342b6c661593aac1fc4b9235541667e336cfa", "size": 84756489, "subdir": "win-64", "timestamp": 1715202816000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libcusolver-11.6.1.9-hd77b12b_1.conda", "version": "11.6.1.9"}