{"build": "hb33846d_3", "build_number": 3, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0", "zlib >=1.2.13,<1.3.0a0", "freetype >=2.10.4,<3.0a0", "libiconv >=1.16,<2.0a0", "libxml2 >=2.13.1,<2.14.0a0", "expat >=2.6.2,<3.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\fontconfig-2.14.1-hb33846d_3", "files": ["Library/bin/fc-cache.exe", "Library/bin/fc-cat.exe", "Library/bin/fc-conflist.exe", "Library/bin/fc-list.exe", "Library/bin/fc-match.exe", "Library/bin/fc-pattern.exe", "Library/bin/fc-query.exe", "Library/bin/fc-scan.exe", "Library/bin/fc-validate.exe", "Library/bin/fontconfig-1.dll", "Library/etc/fonts/conf.d/10-hinting-slight.conf", "Library/etc/fonts/conf.d/10-scale-bitmap-fonts.conf", "Library/etc/fonts/conf.d/10-sub-pixel-rgb.conf", "Library/etc/fonts/conf.d/10-yes-antialias.conf", "Library/etc/fonts/conf.d/11-lcdfilter-default.conf", "Library/etc/fonts/conf.d/20-unhint-small-vera.conf", "Library/etc/fonts/conf.d/30-metric-aliases.conf", "Library/etc/fonts/conf.d/40-nonlatin.conf", "Library/etc/fonts/conf.d/45-generic.conf", "Library/etc/fonts/conf.d/45-latin.conf", "Library/etc/fonts/conf.d/48-spacing.conf", "Library/etc/fonts/conf.d/49-sansserif.conf", "Library/etc/fonts/conf.d/50-user.conf", "Library/etc/fonts/conf.d/51-local.conf", "Library/etc/fonts/conf.d/60-generic.conf", "Library/etc/fonts/conf.d/60-latin.conf", "Library/etc/fonts/conf.d/65-fonts-persian.conf", "Library/etc/fonts/conf.d/65-nonlatin.conf", "Library/etc/fonts/conf.d/69-unifont.conf", "Library/etc/fonts/conf.d/80-delicious.conf", "Library/etc/fonts/conf.d/90-synthetic.conf", "Library/etc/fonts/conf.d/README", "Library/etc/fonts/fonts.conf", "Library/include/fontconfig/fcfreetype.h", "Library/include/fontconfig/fcprivate.h", "Library/include/fontconfig/fontconfig.h", "Library/lib/fontconfig.lib", "Library/lib/pkgconfig/fontconfig.pc", "Library/share/fontconfig/conf.avail/05-reset-dirs-sample.conf", "Library/share/fontconfig/conf.avail/09-autohint-if-no-hinting.conf", "Library/share/fontconfig/conf.avail/10-autohint.conf", "Library/share/fontconfig/conf.avail/10-hinting-full.conf", "Library/share/fontconfig/conf.avail/10-hinting-medium.conf", "Library/share/fontconfig/conf.avail/10-hinting-none.conf", "Library/share/fontconfig/conf.avail/10-hinting-slight.conf", "Library/share/fontconfig/conf.avail/10-no-antialias.conf", "Library/share/fontconfig/conf.avail/10-no-sub-pixel.conf", "Library/share/fontconfig/conf.avail/10-scale-bitmap-fonts.conf", "Library/share/fontconfig/conf.avail/10-sub-pixel-bgr.conf", "Library/share/fontconfig/conf.avail/10-sub-pixel-rgb.conf", "Library/share/fontconfig/conf.avail/10-sub-pixel-vbgr.conf", "Library/share/fontconfig/conf.avail/10-sub-pixel-vrgb.conf", "Library/share/fontconfig/conf.avail/10-unhinted.conf", "Library/share/fontconfig/conf.avail/10-yes-antialias.conf", "Library/share/fontconfig/conf.avail/11-lcdfilter-default.conf", "Library/share/fontconfig/conf.avail/11-lcdfilter-legacy.conf", "Library/share/fontconfig/conf.avail/11-lcdfilter-light.conf", "Library/share/fontconfig/conf.avail/20-unhint-small-vera.conf", "Library/share/fontconfig/conf.avail/25-unhint-nonlatin.conf", "Library/share/fontconfig/conf.avail/30-metric-aliases.conf", "Library/share/fontconfig/conf.avail/35-lang-normalize.conf", "Library/share/fontconfig/conf.avail/40-nonlatin.conf", "Library/share/fontconfig/conf.avail/45-generic.conf", "Library/share/fontconfig/conf.avail/45-latin.conf", "Library/share/fontconfig/conf.avail/48-spacing.conf", "Library/share/fontconfig/conf.avail/49-sansserif.conf", "Library/share/fontconfig/conf.avail/50-user.conf", "Library/share/fontconfig/conf.avail/51-local.conf", "Library/share/fontconfig/conf.avail/60-generic.conf", "Library/share/fontconfig/conf.avail/60-latin.conf", "Library/share/fontconfig/conf.avail/65-fonts-persian.conf", "Library/share/fontconfig/conf.avail/65-khmer.conf", "Library/share/fontconfig/conf.avail/65-nonlatin.conf", "Library/share/fontconfig/conf.avail/69-unifont.conf", "Library/share/fontconfig/conf.avail/70-no-bitmaps.conf", "Library/share/fontconfig/conf.avail/70-yes-bitmaps.conf", "Library/share/fontconfig/conf.avail/80-delicious.conf", "Library/share/fontconfig/conf.avail/90-synthetic.conf", "Library/share/gettext/its/fontconfig.its", "Library/share/gettext/its/fontconfig.loc", "Library/share/xml/fontconfig/fonts.dtd"], "fn": "fontconfig-2.14.1-hb33846d_3.conda", "license": "MIT", "link": {"source": "D:\\anaconda3\\pkgs\\fontconfig-2.14.1-hb33846d_3", "type": 1}, "md5": "bb53f72bb1bd69b2336f323107f88d1d", "name": "fontconfig", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\fontconfig-2.14.1-hb33846d_3.conda", "paths_data": {"paths": [{"_path": "Library/bin/fc-cache.exe", "path_type": "hardlink", "sha256": "63e930fbf29eacf187629669e2deeb1c8a98275036147ac551635e0d3d1fb362", "sha256_in_prefix": "63e930fbf29eacf187629669e2deeb1c8a98275036147ac551635e0d3d1fb362", "size_in_bytes": 25872}, {"_path": "Library/bin/fc-cat.exe", "path_type": "hardlink", "sha256": "0b2413ac7c85864fe15a6cf357aaf9597cd614be9495f4f27f86cd2327f05f6f", "sha256_in_prefix": "0b2413ac7c85864fe15a6cf357aaf9597cd614be9495f4f27f86cd2327f05f6f", "size_in_bytes": 25880}, {"_path": "Library/bin/fc-conflist.exe", "path_type": "hardlink", "sha256": "d62a16d1272b5ab2d618255fd1d62ce44a4a82b213ae362385a186ec222fadca", "sha256_in_prefix": "d62a16d1272b5ab2d618255fd1d62ce44a4a82b213ae362385a186ec222fadca", "size_in_bytes": 22288}, {"_path": "Library/bin/fc-list.exe", "path_type": "hardlink", "sha256": "f98a52ac338901472b97f32d82230357ed90f7ba198c6e0d606aed0541566244", "sha256_in_prefix": "f98a52ac338901472b97f32d82230357ed90f7ba198c6e0d606aed0541566244", "size_in_bytes": 22288}, {"_path": "Library/bin/fc-match.exe", "path_type": "hardlink", "sha256": "8e9ecb3e3e4331269e821047c1226ca4aa299cb9b8740c4faead79ef33a35170", "sha256_in_prefix": "8e9ecb3e3e4331269e821047c1226ca4aa299cb9b8740c4faead79ef33a35170", "size_in_bytes": 22800}, {"_path": "Library/bin/fc-pattern.exe", "path_type": "hardlink", "sha256": "dcde83498e6eb690a337805e1133beb6d292288e301ac501409a3a2a025d3919", "sha256_in_prefix": "dcde83498e6eb690a337805e1133beb6d292288e301ac501409a3a2a025d3919", "size_in_bytes": 22296}, {"_path": "Library/bin/fc-query.exe", "path_type": "hardlink", "sha256": "d7d89e26b2db5be0c845db1ea3f70000349e04c49cf2405225b7370b271e6d82", "sha256_in_prefix": "d7d89e26b2db5be0c845db1ea3f70000349e04c49cf2405225b7370b271e6d82", "size_in_bytes": 22800}, {"_path": "Library/bin/fc-scan.exe", "path_type": "hardlink", "sha256": "0c3c41342d25621056c48883f15b5f16ee8d60a9603ceaa735b401d0fe90f070", "sha256_in_prefix": "0c3c41342d25621056c48883f15b5f16ee8d60a9603ceaa735b401d0fe90f070", "size_in_bytes": 22800}, {"_path": "Library/bin/fc-validate.exe", "path_type": "hardlink", "sha256": "8829574d3e02a06787138560b4ea19c5beea435bb8ed23f7d579dc3694689fa8", "sha256_in_prefix": "8829574d3e02a06787138560b4ea19c5beea435bb8ed23f7d579dc3694689fa8", "size_in_bytes": 24336}, {"_path": "Library/bin/fontconfig-1.dll", "path_type": "hardlink", "sha256": "83a540374dae172e87244a73a399a596690e5fe50a824fd72f39653fac91b952", "sha256_in_prefix": "83a540374dae172e87244a73a399a596690e5fe50a824fd72f39653fac91b952", "size_in_bytes": 292632}, {"_path": "Library/etc/fonts/conf.d/10-hinting-slight.conf", "path_type": "hardlink", "sha256": "d138eca2bac3f78d45ff7cfb649ad792caa4a6e61f73cea7cf0ea54b091f1ef4", "sha256_in_prefix": "d138eca2bac3f78d45ff7cfb649ad792caa4a6e61f73cea7cf0ea54b091f1ef4", "size_in_bytes": 620}, {"_path": "Library/etc/fonts/conf.d/10-scale-bitmap-fonts.conf", "path_type": "hardlink", "sha256": "df8e99abb6f82384c61978906a45a95794940545da95790d87f01d9391e0894b", "sha256_in_prefix": "df8e99abb6f82384c61978906a45a95794940545da95790d87f01d9391e0894b", "size_in_bytes": 2068}, {"_path": "Library/etc/fonts/conf.d/10-sub-pixel-rgb.conf", "path_type": "hardlink", "sha256": "e4cfc04f5fc5093d2ba20c40f8709c9dfc099997313d0f96bc8256597a15d057", "sha256_in_prefix": "e4cfc04f5fc5093d2ba20c40f8709c9dfc099997313d0f96bc8256597a15d057", "size_in_bytes": 672}, {"_path": "Library/etc/fonts/conf.d/10-yes-antialias.conf", "path_type": "hardlink", "sha256": "e6d82a642d1b37f7d24dc37f938b999f00ff7aa4303b2602d04164d53f221005", "sha256_in_prefix": "e6d82a642d1b37f7d24dc37f938b999f00ff7aa4303b2602d04164d53f221005", "size_in_bytes": 258}, {"_path": "Library/etc/fonts/conf.d/11-lcdfilter-default.conf", "path_type": "hardlink", "sha256": "76aec9d8dac48faabe1bbde9e887cfb09fa45f30be5f8826e945a394a0097e48", "sha256_in_prefix": "76aec9d8dac48faabe1bbde9e887cfb09fa45f30be5f8826e945a394a0097e48", "size_in_bytes": 695}, {"_path": "Library/etc/fonts/conf.d/20-unhint-small-vera.conf", "path_type": "hardlink", "sha256": "278732c68c16177f2aa3c7e4ea318b9f720aee13a433c32eb15ba626806c609d", "sha256_in_prefix": "278732c68c16177f2aa3c7e4ea318b9f720aee13a433c32eb15ba626806c609d", "size_in_bytes": 1377}, {"_path": "Library/etc/fonts/conf.d/30-metric-aliases.conf", "path_type": "hardlink", "sha256": "4c17a813364e7f38ca5758350cf47fead36a9f5497e4a310ebf473f2e6103661", "sha256_in_prefix": "4c17a813364e7f38ca5758350cf47fead36a9f5497e4a310ebf473f2e6103661", "size_in_bytes": 13234}, {"_path": "Library/etc/fonts/conf.d/40-nonlatin.conf", "path_type": "hardlink", "sha256": "5c3f7d62a44258d5d9e81a93b1685bee5ab78acb8688b25a43c4c1e749312a84", "sha256_in_prefix": "5c3f7d62a44258d5d9e81a93b1685bee5ab78acb8688b25a43c4c1e749312a84", "size_in_bytes": 7775}, {"_path": "Library/etc/fonts/conf.d/45-generic.conf", "path_type": "hardlink", "sha256": "24de9f68189d544bb4cc084d9bd8f3a59d8b988a9dc44b11e9877d5add5fc948", "sha256_in_prefix": "24de9f68189d544bb4cc084d9bd8f3a59d8b988a9dc44b11e9877d5add5fc948", "size_in_bytes": 3490}, {"_path": "Library/etc/fonts/conf.d/45-latin.conf", "path_type": "hardlink", "sha256": "30ee92d2633ea4ec2328975fa44e3ff4f8c6465b468b79c3b1df7002f0693d23", "sha256_in_prefix": "30ee92d2633ea4ec2328975fa44e3ff4f8c6465b468b79c3b1df7002f0693d23", "size_in_bytes": 7055}, {"_path": "Library/etc/fonts/conf.d/48-spacing.conf", "path_type": "hardlink", "sha256": "99d839cd8aa28854e8f54f26e7200b6e57951f39f56791a0ed473662488bcef6", "sha256_in_prefix": "99d839cd8aa28854e8f54f26e7200b6e57951f39f56791a0ed473662488bcef6", "size_in_bytes": 441}, {"_path": "Library/etc/fonts/conf.d/49-sansserif.conf", "path_type": "hardlink", "sha256": "7849a11ce5243e8bb3d06bcad558737a8682e5122e73c8e427f4c011a2054818", "sha256_in_prefix": "7849a11ce5243e8bb3d06bcad558737a8682e5122e73c8e427f4c011a2054818", "size_in_bytes": 639}, {"_path": "Library/etc/fonts/conf.d/50-user.conf", "path_type": "hardlink", "sha256": "449137ccce57d60bca178d57519ba54e1942d757ad171f3a88cd78df2161c970", "sha256_in_prefix": "449137ccce57d60bca178d57519ba54e1942d757ad171f3a88cd78df2161c970", "size_in_bytes": 751}, {"_path": "Library/etc/fonts/conf.d/51-local.conf", "path_type": "hardlink", "sha256": "dca9d917e1f66f73bfa495943b4d1eaab2d93810a51bc8e600cf4d4e9df03e67", "sha256_in_prefix": "dca9d917e1f66f73bfa495943b4d1eaab2d93810a51bc8e600cf4d4e9df03e67", "size_in_bytes": 263}, {"_path": "Library/etc/fonts/conf.d/60-generic.conf", "path_type": "hardlink", "sha256": "195bc8605dd23289d42c10d79cc13a5ce4a4b3cf965df5b30ecc0b7149bb951f", "sha256_in_prefix": "195bc8605dd23289d42c10d79cc13a5ce4a4b3cf965df5b30ecc0b7149bb951f", "size_in_bytes": 1911}, {"_path": "Library/etc/fonts/conf.d/60-latin.conf", "path_type": "hardlink", "sha256": "df313037dc3a13daa6eb82483aedfc236157cd3895c557250aa5e72b71ad5508", "sha256_in_prefix": "df313037dc3a13daa6eb82483aedfc236157cd3895c557250aa5e72b71ad5508", "size_in_bytes": 2136}, {"_path": "Library/etc/fonts/conf.d/65-fonts-persian.conf", "path_type": "hardlink", "sha256": "c675fab143a3638346874798be45f51f6878ebec591b4362d83cfffb828ec4ca", "sha256_in_prefix": "c675fab143a3638346874798be45f51f6878ebec591b4362d83cfffb828ec4ca", "size_in_bytes": 10132}, {"_path": "Library/etc/fonts/conf.d/65-nonlatin.conf", "path_type": "hardlink", "sha256": "62368f194f78e9d9b52c13dd605cb8bdd8d4386fc8177b72eff5fb01dcb61d48", "sha256_in_prefix": "62368f194f78e9d9b52c13dd605cb8bdd8d4386fc8177b72eff5fb01dcb61d48", "size_in_bytes": 9291}, {"_path": "Library/etc/fonts/conf.d/69-unifont.conf", "path_type": "hardlink", "sha256": "fa97c0cf5f79d70e2dc3dc48155d4c9220cd7c6a8bc95b89c4ebcc517f97479e", "sha256_in_prefix": "fa97c0cf5f79d70e2dc3dc48155d4c9220cd7c6a8bc95b89c4ebcc517f97479e", "size_in_bytes": 687}, {"_path": "Library/etc/fonts/conf.d/80-delicious.conf", "path_type": "hardlink", "sha256": "34f27b7fe9cd83b2b6d46f16ef9c477412f2d7ef63634f86b9b38b79bc4f81ad", "sha256_in_prefix": "34f27b7fe9cd83b2b6d46f16ef9c477412f2d7ef63634f86b9b38b79bc4f81ad", "size_in_bytes": 437}, {"_path": "Library/etc/fonts/conf.d/90-synthetic.conf", "path_type": "hardlink", "sha256": "cdd0aac7840c664a7127b64db66bdfe8dc22be3774d10bc22f4a41c98de5d85e", "sha256_in_prefix": "cdd0aac7840c664a7127b64db66bdfe8dc22be3774d10bc22f4a41c98de5d85e", "size_in_bytes": 1757}, {"_path": "Library/etc/fonts/conf.d/README", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_15wlimz8z0/croot/fontconfig_1722920952856/_h_env", "sha256": "e0f74c4cd0d2ad0b40cccc4aaaca83016e5f14f8210dab1e1370c52ebe0a8ce6", "sha256_in_prefix": "2b3e1c0877e9f096208164917eb821ab408b3dc29b711235cd1af16cf4c55bd3", "size_in_bytes": 1040}, {"_path": "Library/etc/fonts/fonts.conf", "path_type": "hardlink", "sha256": "f141c1b89b172d22f213531646c21e288f0ebf3ec46484698896e1b33c626756", "sha256_in_prefix": "f141c1b89b172d22f213531646c21e288f0ebf3ec46484698896e1b33c626756", "size_in_bytes": 2703}, {"_path": "Library/include/fontconfig/fcfreetype.h", "path_type": "hardlink", "sha256": "c69d08b5e9720e15291ca30fcb706cdc58515dbf21a6cfd4818bbd142976d697", "sha256_in_prefix": "c69d08b5e9720e15291ca30fcb706cdc58515dbf21a6cfd4818bbd142976d697", "size_in_bytes": 1958}, {"_path": "Library/include/fontconfig/fcprivate.h", "path_type": "hardlink", "sha256": "2d0b07968f4d7d09318d0f284dde0388f0c97f50f67bddd490a6ae5272cabd36", "sha256_in_prefix": "2d0b07968f4d7d09318d0f284dde0388f0c97f50f67bddd490a6ae5272cabd36", "size_in_bytes": 4489}, {"_path": "Library/include/fontconfig/fontconfig.h", "path_type": "hardlink", "sha256": "b4dd2a6e11dcc37264c4e0c7b47af7d50bbc33dae14360cbf3d99cd8585f1db3", "sha256_in_prefix": "b4dd2a6e11dcc37264c4e0c7b47af7d50bbc33dae14360cbf3d99cd8585f1db3", "size_in_bytes": 28746}, {"_path": "Library/lib/fontconfig.lib", "path_type": "hardlink", "sha256": "10fea1cb70482a43d96a20be0db3e669c21304cf3359fca280a000375e359ea2", "sha256_in_prefix": "10fea1cb70482a43d96a20be0db3e669c21304cf3359fca280a000375e359ea2", "size_in_bytes": 50946}, {"_path": "Library/lib/pkgconfig/fontconfig.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_15wlimz8z0/croot/fontconfig_1722920952856/_h_env", "sha256": "9be105be5246c00d0fd0a3deb0fa86b00ced7c8c7fe9a536ad529aa7993907eb", "sha256_in_prefix": "13e350933e19381815e0667b307114a840688495be5adcf7b8b638392b025d3c", "size_in_bytes": 649}, {"_path": "Library/share/fontconfig/conf.avail/05-reset-dirs-sample.conf", "path_type": "hardlink", "sha256": "7dae5045441ce7ab4ce9ee0ccbee84e2ae90daee9cfd2ac64cd887ee85c528df", "sha256_in_prefix": "7dae5045441ce7ab4ce9ee0ccbee84e2ae90daee9cfd2ac64cd887ee85c528df", "size_in_bytes": 213}, {"_path": "Library/share/fontconfig/conf.avail/09-autohint-if-no-hinting.conf", "path_type": "hardlink", "sha256": "f04d1737c2a85925a6294f4b966597d03a30c9e4c551c7a8c270eaad514b2689", "sha256_in_prefix": "f04d1737c2a85925a6294f4b966597d03a30c9e4c551c7a8c270eaad514b2689", "size_in_bytes": 717}, {"_path": "Library/share/fontconfig/conf.avail/10-autohint.conf", "path_type": "hardlink", "sha256": "9ae4308a18941969ea42f5881c202be052fe12da3fb1fb4fc4b940642ec74db0", "sha256_in_prefix": "9ae4308a18941969ea42f5881c202be052fe12da3fb1fb4fc4b940642ec74db0", "size_in_bytes": 630}, {"_path": "Library/share/fontconfig/conf.avail/10-hinting-full.conf", "path_type": "hardlink", "sha256": "aa01699d6a43a1be2996233e67daf1570b9000bd1a59763463d3a12b852cdc3a", "sha256_in_prefix": "aa01699d6a43a1be2996233e67daf1570b9000bd1a59763463d3a12b852cdc3a", "size_in_bytes": 616}, {"_path": "Library/share/fontconfig/conf.avail/10-hinting-medium.conf", "path_type": "hardlink", "sha256": "f0eaec3db6879f3460bd0e1ef3e09dd965168ca460a79d098eb0648c6740095d", "sha256_in_prefix": "f0eaec3db6879f3460bd0e1ef3e09dd965168ca460a79d098eb0648c6740095d", "size_in_bytes": 620}, {"_path": "Library/share/fontconfig/conf.avail/10-hinting-none.conf", "path_type": "hardlink", "sha256": "5970fa53c69874ee51349300ef3412592e46e7f133c06074befcf6ab2a3ef6e2", "sha256_in_prefix": "5970fa53c69874ee51349300ef3412592e46e7f133c06074befcf6ab2a3ef6e2", "size_in_bytes": 616}, {"_path": "Library/share/fontconfig/conf.avail/10-hinting-slight.conf", "path_type": "hardlink", "sha256": "d138eca2bac3f78d45ff7cfb649ad792caa4a6e61f73cea7cf0ea54b091f1ef4", "sha256_in_prefix": "d138eca2bac3f78d45ff7cfb649ad792caa4a6e61f73cea7cf0ea54b091f1ef4", "size_in_bytes": 620}, {"_path": "Library/share/fontconfig/conf.avail/10-no-antialias.conf", "path_type": "hardlink", "sha256": "75412c70f3a78ad090c79a9dc63cce6aae41b2cfc2f67f4a1a1b533526216d2e", "sha256_in_prefix": "75412c70f3a78ad090c79a9dc63cce6aae41b2cfc2f67f4a1a1b533526216d2e", "size_in_bytes": 260}, {"_path": "Library/share/fontconfig/conf.avail/10-no-sub-pixel.conf", "path_type": "hardlink", "sha256": "0f00b5cf0ce07062ec65b9b340aa888f6c5fbc215aafebd26781be2d5a13ff6a", "sha256_in_prefix": "0f00b5cf0ce07062ec65b9b340aa888f6c5fbc215aafebd26781be2d5a13ff6a", "size_in_bytes": 647}, {"_path": "Library/share/fontconfig/conf.avail/10-scale-bitmap-fonts.conf", "path_type": "hardlink", "sha256": "df8e99abb6f82384c61978906a45a95794940545da95790d87f01d9391e0894b", "sha256_in_prefix": "df8e99abb6f82384c61978906a45a95794940545da95790d87f01d9391e0894b", "size_in_bytes": 2068}, {"_path": "Library/share/fontconfig/conf.avail/10-sub-pixel-bgr.conf", "path_type": "hardlink", "sha256": "719006f7887a958da8efe5ad010a2cde689313074607ae20f0be4cd9e8bfffad", "sha256_in_prefix": "719006f7887a958da8efe5ad010a2cde689313074607ae20f0be4cd9e8bfffad", "size_in_bytes": 672}, {"_path": "Library/share/fontconfig/conf.avail/10-sub-pixel-rgb.conf", "path_type": "hardlink", "sha256": "e4cfc04f5fc5093d2ba20c40f8709c9dfc099997313d0f96bc8256597a15d057", "sha256_in_prefix": "e4cfc04f5fc5093d2ba20c40f8709c9dfc099997313d0f96bc8256597a15d057", "size_in_bytes": 672}, {"_path": "Library/share/fontconfig/conf.avail/10-sub-pixel-vbgr.conf", "path_type": "hardlink", "sha256": "15178eb45f3206433bace02053214d5750b9d74a4ece727f48c7fbc8f8c9adb3", "sha256_in_prefix": "15178eb45f3206433bace02053214d5750b9d74a4ece727f48c7fbc8f8c9adb3", "size_in_bytes": 682}, {"_path": "Library/share/fontconfig/conf.avail/10-sub-pixel-vrgb.conf", "path_type": "hardlink", "sha256": "049680ed134d34c560be493a47301bf6542050e7e89c27d3df25145b41f8133e", "sha256_in_prefix": "049680ed134d34c560be493a47301bf6542050e7e89c27d3df25145b41f8133e", "size_in_bytes": 682}, {"_path": "Library/share/fontconfig/conf.avail/10-unhinted.conf", "path_type": "hardlink", "sha256": "dca27a149268da3aeaa3416dc236fd1d76e2f997b1ba91e1292297e78c9281d4", "sha256_in_prefix": "dca27a149268da3aeaa3416dc236fd1d76e2f997b1ba91e1292297e78c9281d4", "size_in_bytes": 625}, {"_path": "Library/share/fontconfig/conf.avail/10-yes-antialias.conf", "path_type": "hardlink", "sha256": "e6d82a642d1b37f7d24dc37f938b999f00ff7aa4303b2602d04164d53f221005", "sha256_in_prefix": "e6d82a642d1b37f7d24dc37f938b999f00ff7aa4303b2602d04164d53f221005", "size_in_bytes": 258}, {"_path": "Library/share/fontconfig/conf.avail/11-lcdfilter-default.conf", "path_type": "hardlink", "sha256": "76aec9d8dac48faabe1bbde9e887cfb09fa45f30be5f8826e945a394a0097e48", "sha256_in_prefix": "76aec9d8dac48faabe1bbde9e887cfb09fa45f30be5f8826e945a394a0097e48", "size_in_bytes": 695}, {"_path": "Library/share/fontconfig/conf.avail/11-lcdfilter-legacy.conf", "path_type": "hardlink", "sha256": "0768fd57ae0a50d54f2f2e5efae98437ae8623573b652f246ac22e574d5f38b0", "sha256_in_prefix": "0768fd57ae0a50d54f2f2e5efae98437ae8623573b652f246ac22e574d5f38b0", "size_in_bytes": 692}, {"_path": "Library/share/fontconfig/conf.avail/11-lcdfilter-light.conf", "path_type": "hardlink", "sha256": "0708443f97406b100a5ccfa67ea8a1c1473ff60ff03848e536bc62e2b2ddcea3", "sha256_in_prefix": "0708443f97406b100a5ccfa67ea8a1c1473ff60ff03848e536bc62e2b2ddcea3", "size_in_bytes": 689}, {"_path": "Library/share/fontconfig/conf.avail/20-unhint-small-vera.conf", "path_type": "hardlink", "sha256": "278732c68c16177f2aa3c7e4ea318b9f720aee13a433c32eb15ba626806c609d", "sha256_in_prefix": "278732c68c16177f2aa3c7e4ea318b9f720aee13a433c32eb15ba626806c609d", "size_in_bytes": 1377}, {"_path": "Library/share/fontconfig/conf.avail/25-unhint-nonlatin.conf", "path_type": "hardlink", "sha256": "8b8c05318fb3f4893a9522e7989dd9280d14233adbc21be867bd173add4a0d5c", "sha256_in_prefix": "8b8c05318fb3f4893a9522e7989dd9280d14233adbc21be867bd173add4a0d5c", "size_in_bytes": 3329}, {"_path": "Library/share/fontconfig/conf.avail/30-metric-aliases.conf", "path_type": "hardlink", "sha256": "4c17a813364e7f38ca5758350cf47fead36a9f5497e4a310ebf473f2e6103661", "sha256_in_prefix": "4c17a813364e7f38ca5758350cf47fead36a9f5497e4a310ebf473f2e6103661", "size_in_bytes": 13234}, {"_path": "Library/share/fontconfig/conf.avail/35-lang-normalize.conf", "path_type": "hardlink", "sha256": "eac31c32f49a02e403d0168209608003b59bcd4429d1c8c6563d913134944162", "sha256_in_prefix": "eac31c32f49a02e403d0168209608003b59bcd4429d1c8c6563d913134944162", "size_in_bytes": 43151}, {"_path": "Library/share/fontconfig/conf.avail/40-nonlatin.conf", "path_type": "hardlink", "sha256": "5c3f7d62a44258d5d9e81a93b1685bee5ab78acb8688b25a43c4c1e749312a84", "sha256_in_prefix": "5c3f7d62a44258d5d9e81a93b1685bee5ab78acb8688b25a43c4c1e749312a84", "size_in_bytes": 7775}, {"_path": "Library/share/fontconfig/conf.avail/45-generic.conf", "path_type": "hardlink", "sha256": "24de9f68189d544bb4cc084d9bd8f3a59d8b988a9dc44b11e9877d5add5fc948", "sha256_in_prefix": "24de9f68189d544bb4cc084d9bd8f3a59d8b988a9dc44b11e9877d5add5fc948", "size_in_bytes": 3490}, {"_path": "Library/share/fontconfig/conf.avail/45-latin.conf", "path_type": "hardlink", "sha256": "30ee92d2633ea4ec2328975fa44e3ff4f8c6465b468b79c3b1df7002f0693d23", "sha256_in_prefix": "30ee92d2633ea4ec2328975fa44e3ff4f8c6465b468b79c3b1df7002f0693d23", "size_in_bytes": 7055}, {"_path": "Library/share/fontconfig/conf.avail/48-spacing.conf", "path_type": "hardlink", "sha256": "99d839cd8aa28854e8f54f26e7200b6e57951f39f56791a0ed473662488bcef6", "sha256_in_prefix": "99d839cd8aa28854e8f54f26e7200b6e57951f39f56791a0ed473662488bcef6", "size_in_bytes": 441}, {"_path": "Library/share/fontconfig/conf.avail/49-sansserif.conf", "path_type": "hardlink", "sha256": "7849a11ce5243e8bb3d06bcad558737a8682e5122e73c8e427f4c011a2054818", "sha256_in_prefix": "7849a11ce5243e8bb3d06bcad558737a8682e5122e73c8e427f4c011a2054818", "size_in_bytes": 639}, {"_path": "Library/share/fontconfig/conf.avail/50-user.conf", "path_type": "hardlink", "sha256": "449137ccce57d60bca178d57519ba54e1942d757ad171f3a88cd78df2161c970", "sha256_in_prefix": "449137ccce57d60bca178d57519ba54e1942d757ad171f3a88cd78df2161c970", "size_in_bytes": 751}, {"_path": "Library/share/fontconfig/conf.avail/51-local.conf", "path_type": "hardlink", "sha256": "dca9d917e1f66f73bfa495943b4d1eaab2d93810a51bc8e600cf4d4e9df03e67", "sha256_in_prefix": "dca9d917e1f66f73bfa495943b4d1eaab2d93810a51bc8e600cf4d4e9df03e67", "size_in_bytes": 263}, {"_path": "Library/share/fontconfig/conf.avail/60-generic.conf", "path_type": "hardlink", "sha256": "195bc8605dd23289d42c10d79cc13a5ce4a4b3cf965df5b30ecc0b7149bb951f", "sha256_in_prefix": "195bc8605dd23289d42c10d79cc13a5ce4a4b3cf965df5b30ecc0b7149bb951f", "size_in_bytes": 1911}, {"_path": "Library/share/fontconfig/conf.avail/60-latin.conf", "path_type": "hardlink", "sha256": "df313037dc3a13daa6eb82483aedfc236157cd3895c557250aa5e72b71ad5508", "sha256_in_prefix": "df313037dc3a13daa6eb82483aedfc236157cd3895c557250aa5e72b71ad5508", "size_in_bytes": 2136}, {"_path": "Library/share/fontconfig/conf.avail/65-fonts-persian.conf", "path_type": "hardlink", "sha256": "c675fab143a3638346874798be45f51f6878ebec591b4362d83cfffb828ec4ca", "sha256_in_prefix": "c675fab143a3638346874798be45f51f6878ebec591b4362d83cfffb828ec4ca", "size_in_bytes": 10132}, {"_path": "Library/share/fontconfig/conf.avail/65-khmer.conf", "path_type": "hardlink", "sha256": "0bc1c077ef01cb409f51f95218b97b144278c0568b5de03683047b2a1963c590", "sha256_in_prefix": "0bc1c077ef01cb409f51f95218b97b144278c0568b5de03683047b2a1963c590", "size_in_bytes": 304}, {"_path": "Library/share/fontconfig/conf.avail/65-nonlatin.conf", "path_type": "hardlink", "sha256": "62368f194f78e9d9b52c13dd605cb8bdd8d4386fc8177b72eff5fb01dcb61d48", "sha256_in_prefix": "62368f194f78e9d9b52c13dd605cb8bdd8d4386fc8177b72eff5fb01dcb61d48", "size_in_bytes": 9291}, {"_path": "Library/share/fontconfig/conf.avail/69-unifont.conf", "path_type": "hardlink", "sha256": "fa97c0cf5f79d70e2dc3dc48155d4c9220cd7c6a8bc95b89c4ebcc517f97479e", "sha256_in_prefix": "fa97c0cf5f79d70e2dc3dc48155d4c9220cd7c6a8bc95b89c4ebcc517f97479e", "size_in_bytes": 687}, {"_path": "Library/share/fontconfig/conf.avail/70-no-bitmaps.conf", "path_type": "hardlink", "sha256": "47411e21901abd71e3d076107c375e9a8ffd1957410b16d7a291cdbe0e466a0a", "sha256_in_prefix": "47411e21901abd71e3d076107c375e9a8ffd1957410b16d7a291cdbe0e466a0a", "size_in_bytes": 327}, {"_path": "Library/share/fontconfig/conf.avail/70-yes-bitmaps.conf", "path_type": "hardlink", "sha256": "d097ed60eb7e2f822c8f91ce82244b2767d4eca000f30204bc6884609b56b49a", "sha256_in_prefix": "d097ed60eb7e2f822c8f91ce82244b2767d4eca000f30204bc6884609b56b49a", "size_in_bytes": 327}, {"_path": "Library/share/fontconfig/conf.avail/80-delicious.conf", "path_type": "hardlink", "sha256": "34f27b7fe9cd83b2b6d46f16ef9c477412f2d7ef63634f86b9b38b79bc4f81ad", "sha256_in_prefix": "34f27b7fe9cd83b2b6d46f16ef9c477412f2d7ef63634f86b9b38b79bc4f81ad", "size_in_bytes": 437}, {"_path": "Library/share/fontconfig/conf.avail/90-synthetic.conf", "path_type": "hardlink", "sha256": "cdd0aac7840c664a7127b64db66bdfe8dc22be3774d10bc22f4a41c98de5d85e", "sha256_in_prefix": "cdd0aac7840c664a7127b64db66bdfe8dc22be3774d10bc22f4a41c98de5d85e", "size_in_bytes": 1757}, {"_path": "Library/share/gettext/its/fontconfig.its", "path_type": "hardlink", "sha256": "6911bec6d1c515804b4fc856e9168842c3f045d0337557a71e6f8f8d5f7515d0", "sha256_in_prefix": "6911bec6d1c515804b4fc856e9168842c3f045d0337557a71e6f8f8d5f7515d0", "size_in_bytes": 1293}, {"_path": "Library/share/gettext/its/fontconfig.loc", "path_type": "hardlink", "sha256": "e0404cb7e3496810701f3970fe7e11c2114328823de9ba8d043544590d60dcf7", "sha256_in_prefix": "e0404cb7e3496810701f3970fe7e11c2114328823de9ba8d043544590d60dcf7", "size_in_bytes": 1293}, {"_path": "Library/share/xml/fontconfig/fonts.dtd", "path_type": "hardlink", "sha256": "76afafd572f6194858eef6d28389505d591d8d2a026da46568046554a87c207f", "sha256_in_prefix": "76afafd572f6194858eef6d28389505d591d8d2a026da46568046554a87c207f", "size_in_bytes": 8254}], "paths_version": 1}, "requested_spec": "None", "sha256": "383a90f348166fa5665e62b2fcb566a06d11ea9298bcec66d1dfa6e67a3caa66", "size": 229164, "subdir": "win-64", "timestamp": 1722921093000, "url": "https://repo.anaconda.com/pkgs/main/win-64/fontconfig-2.14.1-hb33846d_3.conda", "version": "2.14.1"}