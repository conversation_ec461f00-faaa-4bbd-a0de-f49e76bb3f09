{"build": "haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": [], "extracted_package_dir": "D:\\anaconda3\\pkgs\\vswhere-2.8.4-haa95532_0", "files": ["Library/bin/vswhere.exe"], "fn": "vswhere-2.8.4-haa95532_0.conda", "license": "MIT", "link": {"source": "D:\\anaconda3\\pkgs\\vswhere-2.8.4-haa95532_0", "type": 1}, "md5": "3988097999586663cb3d0230fc4193d3", "name": "vswhere", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\vswhere-2.8.4-haa95532_0.conda", "paths_data": {"paths": [{"_path": "Library/bin/vswhere.exe", "path_type": "hardlink", "sha256": "e50a14767c27477f634a4c19709d35c27a72f541fb2ba5c3a446c80998a86419", "sha256_in_prefix": "e50a14767c27477f634a4c19709d35c27a72f541fb2ba5c3a446c80998a86419", "size_in_bytes": 455056}], "paths_version": 1}, "requested_spec": "None", "sha256": "1b201563b3f190dd9541f2ea4f5a66af5a65892fb14041d7b07d861fbb406d43", "size": 214709, "subdir": "win-64", "timestamp": 1616157172000, "url": "https://repo.anaconda.com/pkgs/main/win-64/vswhere-2.8.4-haa95532_0.conda", "version": "2.8.4"}