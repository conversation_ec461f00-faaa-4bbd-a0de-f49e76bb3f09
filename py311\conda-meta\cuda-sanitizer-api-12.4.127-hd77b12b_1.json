{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-sanitizer-api-12.4.127-hd77b12b_1", "files": ["Library/compute-sanitizer/EnableDebuggerInterface.bat", "Library/compute-sanitizer/InterceptorInjectionTarget.dll", "Library/compute-sanitizer/TreeLauncherTargetInjection.dll", "Library/compute-sanitizer/compute-sanitizer.exe", "Library/compute-sanitizer/docs/ComputeSanitizer/graphics/no-padding.png", "Library/compute-sanitizer/docs/ComputeSanitizer/graphics/padding.png", "Library/compute-sanitizer/docs/ComputeSanitizer/graphics/use-after-free.png", "Library/compute-sanitizer/docs/ComputeSanitizer/graphics/use-before-alloc.png", "Library/compute-sanitizer/docs/ComputeSanitizer/index.html", "Library/compute-sanitizer/docs/CopyrightAndLicenses/index.html", "Library/compute-sanitizer/docs/ReleaseNotes/index.html", "Library/compute-sanitizer/docs/SanitizerApi/annotated.html", "Library/compute-sanitizer/docs/SanitizerApi/classes.html", "Library/compute-sanitizer/docs/SanitizerApi/doxygen.css", "Library/compute-sanitizer/docs/SanitizerApi/doxygen.png", "Library/compute-sanitizer/docs/SanitizerApi/ftv2blank.png", "Library/compute-sanitizer/docs/SanitizerApi/ftv2doc.png", "Library/compute-sanitizer/docs/SanitizerApi/ftv2folderclosed.png", "Library/compute-sanitizer/docs/SanitizerApi/ftv2folderopen.png", "Library/compute-sanitizer/docs/SanitizerApi/ftv2lastnode.png", "Library/compute-sanitizer/docs/SanitizerApi/ftv2link.png", "Library/compute-sanitizer/docs/SanitizerApi/ftv2mlastnode.png", "Library/compute-sanitizer/docs/SanitizerApi/ftv2mnode.png", "Library/compute-sanitizer/docs/SanitizerApi/ftv2node.png", "Library/compute-sanitizer/docs/SanitizerApi/ftv2plastnode.png", "Library/compute-sanitizer/docs/SanitizerApi/ftv2pnode.png", "Library/compute-sanitizer/docs/SanitizerApi/ftv2vertline.png", "Library/compute-sanitizer/docs/SanitizerApi/functions.html", "Library/compute-sanitizer/docs/SanitizerApi/functions_vars.html", "Library/compute-sanitizer/docs/SanitizerApi/group__SANITIZER__BARRIER__API.html", "Library/compute-sanitizer/docs/SanitizerApi/group__SANITIZER__CALLBACK__API.html", "Library/compute-sanitizer/docs/SanitizerApi/group__SANITIZER__MEMORY__API.html", "Library/compute-sanitizer/docs/SanitizerApi/group__SANITIZER__PATCHING__API.html", "Library/compute-sanitizer/docs/SanitizerApi/group__SANITIZER__RESULT__API.html", "Library/compute-sanitizer/docs/SanitizerApi/group__SANITIZER__STREAM__API.html", "Library/compute-sanitizer/docs/SanitizerApi/index.html", "Library/compute-sanitizer/docs/SanitizerApi/modules.html", "Library/compute-sanitizer/docs/SanitizerApi/notices-header.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__BatchMemopData.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__CallbackData.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__EventData.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__ExternalMemoryData.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__GraphExecData.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__GraphLaunchData.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__GraphNodeLaunchData.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__LaunchData.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__MemcpyData.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__MemsetData.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceArrayData.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceContextData.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceFunctionsLazyLoadedData.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceMemoryData.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceMempoolData.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceModuleData.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceStreamData.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceVirtualRange.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__SynchronizeData.html", "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__UvmData.html", "Library/compute-sanitizer/docs/SanitizerApi/tab_b.gif", "Library/compute-sanitizer/docs/SanitizerApi/tab_l.gif", "Library/compute-sanitizer/docs/SanitizerApi/tab_r.gif", "Library/compute-sanitizer/docs/SanitizerApi/tabs.css", "Library/compute-sanitizer/docs/SanitizerApiGuide/index.html", "Library/compute-sanitizer/docs/SanitizerNvtxGuide/index.html", "Library/compute-sanitizer/docs/common/formatting/bg-head.png", "Library/compute-sanitizer/docs/common/formatting/bg-horiz.png", "Library/compute-sanitizer/docs/common/formatting/bg-left.png", "Library/compute-sanitizer/docs/common/formatting/bg-right.png", "Library/compute-sanitizer/docs/common/formatting/bg-sidehead-glow.png", "Library/compute-sanitizer/docs/common/formatting/bg-sidehead.png", "Library/compute-sanitizer/docs/common/formatting/bg-vert.png", "Library/compute-sanitizer/docs/common/formatting/common.min.js", "Library/compute-sanitizer/docs/common/formatting/commonltr.css", "Library/compute-sanitizer/docs/common/formatting/cppapiref.css", "Library/compute-sanitizer/docs/common/formatting/cuda-toolkit-documentation.png", "Library/compute-sanitizer/docs/common/formatting/devtools-documentation.png", "Library/compute-sanitizer/docs/common/formatting/devzone.png", "Library/compute-sanitizer/docs/common/formatting/dita.style.css", "Library/compute-sanitizer/docs/common/formatting/html5shiv-printshiv.min.js", "Library/compute-sanitizer/docs/common/formatting/jquery.ba-hashchange.min.js", "Library/compute-sanitizer/docs/common/formatting/jquery.min.js", "Library/compute-sanitizer/docs/common/formatting/jquery.scrollintoview.min.js", "Library/compute-sanitizer/docs/common/formatting/magnify-dropdown.png", "Library/compute-sanitizer/docs/common/formatting/magnify.png", "Library/compute-sanitizer/docs/common/formatting/nvidia.png", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-Splus.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-aea.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-agc.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-apollo.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-basic.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-cbm.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-cl.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-clj.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-css.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-dart.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-el.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-erl.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-erlang.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-fs.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-go.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-hs.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-lasso.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-lassoscript.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-latex.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-lgt.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-lisp.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-ll.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-llvm.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-logtalk.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-ls.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-lsp.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-lua.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-matlab.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-ml.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-mumps.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-n.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-nemerle.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-pascal.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-proto.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-r.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-rd.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-rkt.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-rust.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-s.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-scala.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-scm.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-sql.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-ss.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-swift.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-tcl.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-tex.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-vb.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-vbs.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-vhd.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-vhdl.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-wiki.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-xq.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-xquery.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-yaml.js", "Library/compute-sanitizer/docs/common/formatting/prettify/lang-yml.js", "Library/compute-sanitizer/docs/common/formatting/prettify/onLoad.png", "Library/compute-sanitizer/docs/common/formatting/prettify/prettify.css", "Library/compute-sanitizer/docs/common/formatting/prettify/prettify.js", "Library/compute-sanitizer/docs/common/formatting/prettify/run_prettify.js", "Library/compute-sanitizer/docs/common/formatting/qwcode.highlight.css", "Library/compute-sanitizer/docs/common/formatting/search-clear.png", "Library/compute-sanitizer/docs/common/formatting/site.css", "Library/compute-sanitizer/docs/common/scripts/google-analytics/google-analytics-tracker.js", "Library/compute-sanitizer/docs/common/scripts/google-analytics/google-analytics-write.js", "Library/compute-sanitizer/docs/common/scripts/tynt/tynt.js", "Library/compute-sanitizer/docs/index.html", "Library/compute-sanitizer/docs/pdf/ComputeSanitizer.pdf", "Library/compute-sanitizer/docs/pdf/CopyrightAndLicenses.pdf", "Library/compute-sanitizer/docs/pdf/ReleaseNotes.pdf", "Library/compute-sanitizer/docs/pdf/SanitizerApiGuide.pdf", "Library/compute-sanitizer/docs/pdf/SanitizerNvtxGuide.pdf", "Library/compute-sanitizer/docs/search/check.html", "Library/compute-sanitizer/docs/search/files.js", "Library/compute-sanitizer/docs/search/htmlFileInfoList.js", "Library/compute-sanitizer/docs/search/htmlFileList.js", "Library/compute-sanitizer/docs/search/index-1.js", "Library/compute-sanitizer/docs/search/index-2.js", "Library/compute-sanitizer/docs/search/index-3.js", "Library/compute-sanitizer/docs/search/nwSearchFnt.min.js", "Library/compute-sanitizer/docs/search/stemmers/en_stemmer.min.js", "Library/compute-sanitizer/include/generated_cudaD3D10_meta.h", "Library/compute-sanitizer/include/generated_cudaD3D11_meta.h", "Library/compute-sanitizer/include/generated_cudaD3D9_meta.h", "Library/compute-sanitizer/include/generated_cudaGL_meta.h", "Library/compute-sanitizer/include/generated_cuda_d3d10_interop_meta.h", "Library/compute-sanitizer/include/generated_cuda_d3d11_interop_meta.h", "Library/compute-sanitizer/include/generated_cuda_d3d9_interop_meta.h", "Library/compute-sanitizer/include/generated_cuda_gl_interop_meta.h", "Library/compute-sanitizer/include/generated_cuda_meta.h", "Library/compute-sanitizer/include/generated_cuda_profiler_api_meta.h", "Library/compute-sanitizer/include/generated_cuda_runtime_api_meta.h", "Library/compute-sanitizer/include/sanitizer.h", "Library/compute-sanitizer/include/sanitizer_barrier.h", "Library/compute-sanitizer/include/sanitizer_callbacks.h", "Library/compute-sanitizer/include/sanitizer_driver_cbid.h", "Library/compute-sanitizer/include/sanitizer_memory.h", "Library/compute-sanitizer/include/sanitizer_patching.h", "Library/compute-sanitizer/include/sanitizer_result.h", "Library/compute-sanitizer/include/sanitizer_runtime_cbid.h", "Library/compute-sanitizer/include/sanitizer_stream.h", "Library/compute-sanitizer/sanitizer-collection.dll", "Library/compute-sanitizer/sanitizer-public.dll", "Library/compute-sanitizer/sanitizer-public.lib", "Scripts/compute-sanitizer.bat"], "fn": "cuda-sanitizer-api-12.4.127-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-sanitizer-api-12.4.127-hd77b12b_1", "type": 1}, "md5": "a9e30c14f3960f512a4df4ce6f8a917c", "name": "cuda-sanitizer-api", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-sanitizer-api-12.4.127-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/compute-sanitizer/EnableDebuggerInterface.bat", "path_type": "hardlink", "sha256": "68c5f577c737a6784c2f25a79b37a7d08557a51c8cedc5cc89c20eaee4b5fe06", "sha256_in_prefix": "68c5f577c737a6784c2f25a79b37a7d08557a51c8cedc5cc89c20eaee4b5fe06", "size_in_bytes": 97}, {"_path": "Library/compute-sanitizer/InterceptorInjectionTarget.dll", "path_type": "hardlink", "sha256": "5769cf65c600f2c35126ee52949713fdc084c8fb2849fbc31113d3b429680be9", "sha256_in_prefix": "5769cf65c600f2c35126ee52949713fdc084c8fb2849fbc31113d3b429680be9", "size_in_bytes": 1128016}, {"_path": "Library/compute-sanitizer/TreeLauncherTargetInjection.dll", "path_type": "hardlink", "sha256": "4cd200dea5a6acdd84fd31cc740ac6fb42cc0b286e9800cd74738b61aa1d3289", "sha256_in_prefix": "4cd200dea5a6acdd84fd31cc740ac6fb42cc0b286e9800cd74738b61aa1d3289", "size_in_bytes": 1605208}, {"_path": "Library/compute-sanitizer/compute-sanitizer.exe", "path_type": "hardlink", "sha256": "bb267a05595cccdce3b5a88506d55e772f3d40fda383ff08d6a772d3e3ced834", "sha256_in_prefix": "bb267a05595cccdce3b5a88506d55e772f3d40fda383ff08d6a772d3e3ced834", "size_in_bytes": 4277344}, {"_path": "Library/compute-sanitizer/docs/ComputeSanitizer/graphics/no-padding.png", "path_type": "hardlink", "sha256": "aeb8c47be35e661ac11ff85fa78bcf06a3bb3b234f76485c2c8d52c06a531fd9", "sha256_in_prefix": "aeb8c47be35e661ac11ff85fa78bcf06a3bb3b234f76485c2c8d52c06a531fd9", "size_in_bytes": 4055}, {"_path": "Library/compute-sanitizer/docs/ComputeSanitizer/graphics/padding.png", "path_type": "hardlink", "sha256": "5160e3867cad691809961c68fa90b83022f3e20d6f2410176f910b85256956c2", "sha256_in_prefix": "5160e3867cad691809961c68fa90b83022f3e20d6f2410176f910b85256956c2", "size_in_bytes": 3753}, {"_path": "Library/compute-sanitizer/docs/ComputeSanitizer/graphics/use-after-free.png", "path_type": "hardlink", "sha256": "03cbd67be9b6a84bfb132765dc1c622d7055de94aeb55efc804dc9a95f9eade4", "sha256_in_prefix": "03cbd67be9b6a84bfb132765dc1c622d7055de94aeb55efc804dc9a95f9eade4", "size_in_bytes": 73869}, {"_path": "Library/compute-sanitizer/docs/ComputeSanitizer/graphics/use-before-alloc.png", "path_type": "hardlink", "sha256": "7bf5f067837b00c4963a61bca2c59b62c4af4996ba2dd288fd46d2fb8439e69d", "sha256_in_prefix": "7bf5f067837b00c4963a61bca2c59b62c4af4996ba2dd288fd46d2fb8439e69d", "size_in_bytes": 73157}, {"_path": "Library/compute-sanitizer/docs/ComputeSanitizer/index.html", "path_type": "hardlink", "sha256": "18a986c6a865bd83219625bd047ebeec1ac5ec16eb79ae8d4eb60848d1de839a", "sha256_in_prefix": "18a986c6a865bd83219625bd047ebeec1ac5ec16eb79ae8d4eb60848d1de839a", "size_in_bytes": 307111}, {"_path": "Library/compute-sanitizer/docs/CopyrightAndLicenses/index.html", "path_type": "hardlink", "sha256": "44f4b412b12188ef511917a4ccdf481747c09360715308c89ca36e2ae3332a86", "sha256_in_prefix": "44f4b412b12188ef511917a4ccdf481747c09360715308c89ca36e2ae3332a86", "size_in_bytes": 65752}, {"_path": "Library/compute-sanitizer/docs/ReleaseNotes/index.html", "path_type": "hardlink", "sha256": "3fb0b0815dc1be62e3b392576a660ac365663706b4f1b7c9343ed7f219ac4345", "sha256_in_prefix": "3fb0b0815dc1be62e3b392576a660ac365663706b4f1b7c9343ed7f219ac4345", "size_in_bytes": 74313}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/annotated.html", "path_type": "hardlink", "sha256": "3daa001dfca64e75312262397f0e7d4fc77f147537eaa74a3d56cabf178ca021", "sha256_in_prefix": "3daa001dfca64e75312262397f0e7d4fc77f147537eaa74a3d56cabf178ca021", "size_in_bytes": 211230}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/classes.html", "path_type": "hardlink", "sha256": "c6493c0f175fc71c8b6041d2a9e204fa39afc304cb64eb6e3d37176d97b77baf", "sha256_in_prefix": "c6493c0f175fc71c8b6041d2a9e204fa39afc304cb64eb6e3d37176d97b77baf", "size_in_bytes": 3960}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/doxygen.css", "path_type": "hardlink", "sha256": "104be0a51409d1fc06fd2c69dfe29a3f5710a1128bff5cc443d792939e3cedfd", "sha256_in_prefix": "104be0a51409d1fc06fd2c69dfe29a3f5710a1128bff5cc443d792939e3cedfd", "size_in_bytes": 5701}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/doxygen.png", "path_type": "hardlink", "sha256": "c94b0fff66087999935e12fd492b9a1b2160b44b91841860d13ce0e33d20d8f2", "sha256_in_prefix": "c94b0fff66087999935e12fd492b9a1b2160b44b91841860d13ce0e33d20d8f2", "size_in_bytes": 1281}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/ftv2blank.png", "path_type": "hardlink", "sha256": "fba45dcc498f0e705c1643720555b1c9247f8011667363f68fb94df953a8ae3f", "sha256_in_prefix": "fba45dcc498f0e705c1643720555b1c9247f8011667363f68fb94df953a8ae3f", "size_in_bytes": 174}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/ftv2doc.png", "path_type": "hardlink", "sha256": "04c67f2ba2d4341ee90c1793449598cb04bf9507cedfcf91661c8fb1d4d63888", "sha256_in_prefix": "04c67f2ba2d4341ee90c1793449598cb04bf9507cedfcf91661c8fb1d4d63888", "size_in_bytes": 255}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/ftv2folderclosed.png", "path_type": "hardlink", "sha256": "ef3c8f419ea067fdac01bd47cef0f283e8903e34172fa10ec7e5a45d3d6817ff", "sha256_in_prefix": "ef3c8f419ea067fdac01bd47cef0f283e8903e34172fa10ec7e5a45d3d6817ff", "size_in_bytes": 259}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/ftv2folderopen.png", "path_type": "hardlink", "sha256": "d47ed39698b1ae5821bdf620ade6bfd6397b5dab8fd6dfd539e0188cd3e09592", "sha256_in_prefix": "d47ed39698b1ae5821bdf620ade6bfd6397b5dab8fd6dfd539e0188cd3e09592", "size_in_bytes": 261}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/ftv2lastnode.png", "path_type": "hardlink", "sha256": "86e061efafe08ea42649b455ca14163360babe5e6ce1d0bd2dcdf2c54ccaba7c", "sha256_in_prefix": "86e061efafe08ea42649b455ca14163360babe5e6ce1d0bd2dcdf2c54ccaba7c", "size_in_bytes": 233}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/ftv2link.png", "path_type": "hardlink", "sha256": "6d7a716c7a24be8e71649a238c5519c3e5b5d0cf8384bb68177e265dca941c38", "sha256_in_prefix": "6d7a716c7a24be8e71649a238c5519c3e5b5d0cf8384bb68177e265dca941c38", "size_in_bytes": 358}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/ftv2mlastnode.png", "path_type": "hardlink", "sha256": "f02d0dd7e9633644f03b9e93d864494690508d4f5611e0df95aad99b0ccbc118", "sha256_in_prefix": "f02d0dd7e9633644f03b9e93d864494690508d4f5611e0df95aad99b0ccbc118", "size_in_bytes": 160}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/ftv2mnode.png", "path_type": "hardlink", "sha256": "758e96c897604842e2fad6c1a7ee1d0cebf6b5d4c116bbf39bf094201a30c341", "sha256_in_prefix": "758e96c897604842e2fad6c1a7ee1d0cebf6b5d4c116bbf39bf094201a30c341", "size_in_bytes": 194}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/ftv2node.png", "path_type": "hardlink", "sha256": "fd26c7ff32e383ce23b9d36097936c73df43de5712f01141054897a4781ac58a", "sha256_in_prefix": "fd26c7ff32e383ce23b9d36097936c73df43de5712f01141054897a4781ac58a", "size_in_bytes": 235}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/ftv2plastnode.png", "path_type": "hardlink", "sha256": "d9ca5dbbc01326817c55113f0506fa998fd1907518989423a02bfa444fe44260", "sha256_in_prefix": "d9ca5dbbc01326817c55113f0506fa998fd1907518989423a02bfa444fe44260", "size_in_bytes": 165}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/ftv2pnode.png", "path_type": "hardlink", "sha256": "31b0d40b12f1b174d9f123061986f1ce5f7d0d5be4685a82a3907c194b54f9bb", "sha256_in_prefix": "31b0d40b12f1b174d9f123061986f1ce5f7d0d5be4685a82a3907c194b54f9bb", "size_in_bytes": 200}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/ftv2vertline.png", "path_type": "hardlink", "sha256": "df1347a845fcab6fff32f148f69ccbe0e8535b2175d28377e0992002eb885f92", "sha256_in_prefix": "df1347a845fcab6fff32f148f69ccbe0e8535b2175d28377e0992002eb885f92", "size_in_bytes": 229}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/functions.html", "path_type": "hardlink", "sha256": "6f3ddbcec2a9c48878e1b64f5d68d7935dbc22d6cdcff862f48b3c4b972db515", "sha256_in_prefix": "6f3ddbcec2a9c48878e1b64f5d68d7935dbc22d6cdcff862f48b3c4b972db515", "size_in_bytes": 40174}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/functions_vars.html", "path_type": "hardlink", "sha256": "471b54cccbcf6e3196763ce8b5b748f98fe24cc9d2e06001cd61a8ca8420bcf4", "sha256_in_prefix": "471b54cccbcf6e3196763ce8b5b748f98fe24cc9d2e06001cd61a8ca8420bcf4", "size_in_bytes": 20080}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/group__SANITIZER__BARRIER__API.html", "path_type": "hardlink", "sha256": "d9e7ac48d8fbc357f8d65af85e1c65ed7ff4ce41fcbe8b3df09135e42b3dbb28", "sha256_in_prefix": "d9e7ac48d8fbc357f8d65af85e1c65ed7ff4ce41fcbe8b3df09135e42b3dbb28", "size_in_bytes": 3763}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/group__SANITIZER__CALLBACK__API.html", "path_type": "hardlink", "sha256": "6a90830fd3621ea65e5af5142fb6a5d8e0881b9786ced40936e76a197efb0092", "sha256_in_prefix": "6a90830fd3621ea65e5af5142fb6a5d8e0881b9786ced40936e76a197efb0092", "size_in_bytes": 217830}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/group__SANITIZER__MEMORY__API.html", "path_type": "hardlink", "sha256": "8620d14d02096ef95ccf65bd931e4dbdada024db13f12950f16acd0f392a955e", "sha256_in_prefix": "8620d14d02096ef95ccf65bd931e4dbdada024db13f12950f16acd0f392a955e", "size_in_bytes": 19056}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/group__SANITIZER__PATCHING__API.html", "path_type": "hardlink", "sha256": "9a67d19b2f1cf40fce79c07e272942e1fabd1164b81cd639bbd2bcb57ad7993e", "sha256_in_prefix": "9a67d19b2f1cf40fce79c07e272942e1fabd1164b81cd639bbd2bcb57ad7993e", "size_in_bytes": 125250}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/group__SANITIZER__RESULT__API.html", "path_type": "hardlink", "sha256": "dac34eef0e6e23ce7f3e8a2481702ec5801736b7a154c623b18f12e4c33a627d", "sha256_in_prefix": "dac34eef0e6e23ce7f3e8a2481702ec5801736b7a154c623b18f12e4c33a627d", "size_in_bytes": 14608}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/group__SANITIZER__STREAM__API.html", "path_type": "hardlink", "sha256": "a67e22e801933b6fd0488abb06c6ac2b7302cffad5b3d340d1cb0fb7937026b1", "sha256_in_prefix": "a67e22e801933b6fd0488abb06c6ac2b7302cffad5b3d340d1cb0fb7937026b1", "size_in_bytes": 8659}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/index.html", "path_type": "hardlink", "sha256": "b489fcad0f1496a6d56b122f38f3ce29c7b69ce1112798c375174e07c23788f0", "sha256_in_prefix": "b489fcad0f1496a6d56b122f38f3ce29c7b69ce1112798c375174e07c23788f0", "size_in_bytes": 17404}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/modules.html", "path_type": "hardlink", "sha256": "ec3ab780b1edc714f2d38c7dfb39c7155084bc3eb8abdd30c19f2752d87f02df", "sha256_in_prefix": "ec3ab780b1edc714f2d38c7dfb39c7155084bc3eb8abdd30c19f2752d87f02df", "size_in_bytes": 430024}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/notices-header.html", "path_type": "hardlink", "sha256": "a853153a97d5eecf7f85f832f868ca0b7203dc031269ebe4027514d3438b8362", "sha256_in_prefix": "a853153a97d5eecf7f85f832f868ca0b7203dc031269ebe4027514d3438b8362", "size_in_bytes": 14984}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__BatchMemopData.html", "path_type": "hardlink", "sha256": "26040e556f0a70bc6e6b48c1a687dd9fcb731f92531253ff3fcaa10719bc994c", "sha256_in_prefix": "26040e556f0a70bc6e6b48c1a687dd9fcb731f92531253ff3fcaa10719bc994c", "size_in_bytes": 4339}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__CallbackData.html", "path_type": "hardlink", "sha256": "7201f15865ff769759f82b0f068832d862fe27f8e46309c547dec7a6cad76e60", "sha256_in_prefix": "7201f15865ff769759f82b0f068832d862fe27f8e46309c547dec7a6cad76e60", "size_in_bytes": 5071}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__EventData.html", "path_type": "hardlink", "sha256": "7976644242f29e62bee146ff3eeda430ab4e2c5fda1841db42dc61b633e60811", "sha256_in_prefix": "7976644242f29e62bee146ff3eeda430ab4e2c5fda1841db42dc61b633e60811", "size_in_bytes": 3692}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__ExternalMemoryData.html", "path_type": "hardlink", "sha256": "c28e8070ac517c14e8f769da57a88bf9965d6a06e737bef284b564b71febea4d", "sha256_in_prefix": "c28e8070ac517c14e8f769da57a88bf9965d6a06e737bef284b564b71febea4d", "size_in_bytes": 4016}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__GraphExecData.html", "path_type": "hardlink", "sha256": "ff93e564968e76ab2f3ac1ac5cbfc6a215e97d7cbda9812a57ffffca18f80fb4", "sha256_in_prefix": "ff93e564968e76ab2f3ac1ac5cbfc6a215e97d7cbda9812a57ffffca18f80fb4", "size_in_bytes": 4073}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__GraphLaunchData.html", "path_type": "hardlink", "sha256": "f7039938f199a88c01f41fb51838a09e61e73a45f0c3ce2ebdb8fb352c0e3a2d", "sha256_in_prefix": "f7039938f199a88c01f41fb51838a09e61e73a45f0c3ce2ebdb8fb352c0e3a2d", "size_in_bytes": 4103}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__GraphNodeLaunchData.html", "path_type": "hardlink", "sha256": "11c661127367e44205f5818553b03c87758fe777b97ea3a1faca2f03b5ba78d5", "sha256_in_prefix": "11c661127367e44205f5818553b03c87758fe777b97ea3a1faca2f03b5ba78d5", "size_in_bytes": 6405}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__LaunchData.html", "path_type": "hardlink", "sha256": "b983eb2ad9b5107b8af589648f88b893136efe1d0aaad13c7db46f6a5b40dd8a", "sha256_in_prefix": "b983eb2ad9b5107b8af589648f88b893136efe1d0aaad13c7db46f6a5b40dd8a", "size_in_bytes": 7953}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__MemcpyData.html", "path_type": "hardlink", "sha256": "2fb8ac455b63a07a2c1ff121fe3deed065cec64c0b1f4d50289d00d80f8820fe", "sha256_in_prefix": "2fb8ac455b63a07a2c1ff121fe3deed065cec64c0b1f4d50289d00d80f8820fe", "size_in_bytes": 6982}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__MemsetData.html", "path_type": "hardlink", "sha256": "9f35f04359c304931eb8837286af1600be9e47f91aea2eb6989648308b1b7931", "sha256_in_prefix": "9f35f04359c304931eb8837286af1600be9e47f91aea2eb6989648308b1b7931", "size_in_bytes": 4423}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceArrayData.html", "path_type": "hardlink", "sha256": "31bded2378ab6348da167bdabe08be07e6a1af0623111991601f1a9b36dcbe83", "sha256_in_prefix": "31bded2378ab6348da167bdabe08be07e6a1af0623111991601f1a9b36dcbe83", "size_in_bytes": 3649}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceContextData.html", "path_type": "hardlink", "sha256": "47cfa967acf37ed3863fb324218d99b82359eeb7f97a6bcc2a964a20a34697c1", "sha256_in_prefix": "47cfa967acf37ed3863fb324218d99b82359eeb7f97a6bcc2a964a20a34697c1", "size_in_bytes": 3542}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceFunctionsLazyLoadedData.html", "path_type": "hardlink", "sha256": "a57c441b3731071149a2ff4feea86d7a62e1cbff0a8764476e1bf92d7c7664a5", "sha256_in_prefix": "a57c441b3731071149a2ff4feea86d7a62e1cbff0a8764476e1bf92d7c7664a5", "size_in_bytes": 4002}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceMemoryData.html", "path_type": "hardlink", "sha256": "e7522bb8ecb633123f3a2cbf71aac00135893cadde1852f54218b5818275503a", "sha256_in_prefix": "e7522bb8ecb633123f3a2cbf71aac00135893cadde1852f54218b5818275503a", "size_in_bytes": 5977}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceMempoolData.html", "path_type": "hardlink", "sha256": "b46c1111d0ec627dc41b59892f4761a82e2f25538cafb23e513aef3b58fe5198", "sha256_in_prefix": "b46c1111d0ec627dc41b59892f4761a82e2f25538cafb23e513aef3b58fe5198", "size_in_bytes": 3788}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceModuleData.html", "path_type": "hardlink", "sha256": "4f978bc14268214e18c6a4649b7b81c202b3439cff51e1dbaa037a0854049b37", "sha256_in_prefix": "4f978bc14268214e18c6a4649b7b81c202b3439cff51e1dbaa037a0854049b37", "size_in_bytes": 4153}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceStreamData.html", "path_type": "hardlink", "sha256": "fc49c805a175fabf7be60897f9bb5dedafebd8f177ccccee586fdfe5264df469", "sha256_in_prefix": "fc49c805a175fabf7be60897f9bb5dedafebd8f177ccccee586fdfe5264df469", "size_in_bytes": 3742}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__ResourceVirtualRange.html", "path_type": "hardlink", "sha256": "68ae8c0118ca97d0f9ecdb492092ebc3183f9df3c2107f49c7439f6f87734e86", "sha256_in_prefix": "68ae8c0118ca97d0f9ecdb492092ebc3183f9df3c2107f49c7439f6f87734e86", "size_in_bytes": 3483}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__SynchronizeData.html", "path_type": "hardlink", "sha256": "2c34bb68157de67db14f06facc47ee3dde6a02dfb5d05b6227b45cd9123f29fe", "sha256_in_prefix": "2c34bb68157de67db14f06facc47ee3dde6a02dfb5d05b6227b45cd9123f29fe", "size_in_bytes": 3691}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/structSanitizer__UvmData.html", "path_type": "hardlink", "sha256": "34d011fb5d14b1baad231e4b4517c1ad557ad105bbb3f88e2d8bc5f22ec6ed33", "sha256_in_prefix": "34d011fb5d14b1baad231e4b4517c1ad557ad105bbb3f88e2d8bc5f22ec6ed33", "size_in_bytes": 4062}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/tab_b.gif", "path_type": "hardlink", "sha256": "50eb5bf4441ea3073ec1ca36dd63bc2b1eb736f514c6de2bc648e98f0fe9cecd", "sha256_in_prefix": "50eb5bf4441ea3073ec1ca36dd63bc2b1eb736f514c6de2bc648e98f0fe9cecd", "size_in_bytes": 35}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/tab_l.gif", "path_type": "hardlink", "sha256": "d5ecca2b2495aa6b20a860061133a58c00942a408b7828369af4d4b7e08a54d5", "sha256_in_prefix": "d5ecca2b2495aa6b20a860061133a58c00942a408b7828369af4d4b7e08a54d5", "size_in_bytes": 706}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/tab_r.gif", "path_type": "hardlink", "sha256": "1876b505a59e66fdc10880a43b6aee462f3ca4dfefcb963b9de9c2d82539a85b", "sha256_in_prefix": "1876b505a59e66fdc10880a43b6aee462f3ca4dfefcb963b9de9c2d82539a85b", "size_in_bytes": 2585}, {"_path": "Library/compute-sanitizer/docs/SanitizerApi/tabs.css", "path_type": "hardlink", "sha256": "6b3373db3135a9f7344a699afc786eae372787ed73885e21f470999ce58bac88", "sha256_in_prefix": "6b3373db3135a9f7344a699afc786eae372787ed73885e21f470999ce58bac88", "size_in_bytes": 1838}, {"_path": "Library/compute-sanitizer/docs/SanitizerApiGuide/index.html", "path_type": "hardlink", "sha256": "23787a07754cafbd88ebc63d8391b638c46f1605175f9ce7c8b62c1d8a2583a3", "sha256_in_prefix": "23787a07754cafbd88ebc63d8391b638c46f1605175f9ce7c8b62c1d8a2583a3", "size_in_bytes": 50268}, {"_path": "Library/compute-sanitizer/docs/SanitizerNvtxGuide/index.html", "path_type": "hardlink", "sha256": "0f4a6274fea274d389640a8b74adcb34a64860cdea12afc6445ebe4f8313fdb0", "sha256_in_prefix": "0f4a6274fea274d389640a8b74adcb34a64860cdea12afc6445ebe4f8313fdb0", "size_in_bytes": 47878}, {"_path": "Library/compute-sanitizer/docs/common/formatting/bg-head.png", "path_type": "hardlink", "sha256": "d5ea8b20b42df3ca5dccf404fdda5b67b5ee7405e90f1ef288042b412cb4f6f4", "sha256_in_prefix": "d5ea8b20b42df3ca5dccf404fdda5b67b5ee7405e90f1ef288042b412cb4f6f4", "size_in_bytes": 230}, {"_path": "Library/compute-sanitizer/docs/common/formatting/bg-horiz.png", "path_type": "hardlink", "sha256": "7ed990e671720b1ef4c35d5d4c4e050229ab7e612dbe6f61bfe307d7a46dae4b", "sha256_in_prefix": "7ed990e671720b1ef4c35d5d4c4e050229ab7e612dbe6f61bfe307d7a46dae4b", "size_in_bytes": 331}, {"_path": "Library/compute-sanitizer/docs/common/formatting/bg-left.png", "path_type": "hardlink", "sha256": "7e8015b70261db70e396106855b71ebd4cec2570c4f364bf24dc1d236f6f7304", "sha256_in_prefix": "7e8015b70261db70e396106855b71ebd4cec2570c4f364bf24dc1d236f6f7304", "size_in_bytes": 132}, {"_path": "Library/compute-sanitizer/docs/common/formatting/bg-right.png", "path_type": "hardlink", "sha256": "749b5434bd89c09915a98166c2e115263cf4d2641918bfd191407a01a424c47c", "sha256_in_prefix": "749b5434bd89c09915a98166c2e115263cf4d2641918bfd191407a01a424c47c", "size_in_bytes": 131}, {"_path": "Library/compute-sanitizer/docs/common/formatting/bg-sidehead-glow.png", "path_type": "hardlink", "sha256": "8323ca5e47fa251cb5c3a7d946f8db2f39dd77faee66cac9b0522cbac28337c7", "sha256_in_prefix": "8323ca5e47fa251cb5c3a7d946f8db2f39dd77faee66cac9b0522cbac28337c7", "size_in_bytes": 153}, {"_path": "Library/compute-sanitizer/docs/common/formatting/bg-sidehead.png", "path_type": "hardlink", "sha256": "3991888eec29c5e9117e6aa1f37bfbc0ccd8d91db0b9f08944498acf1b25c814", "sha256_in_prefix": "3991888eec29c5e9117e6aa1f37bfbc0ccd8d91db0b9f08944498acf1b25c814", "size_in_bytes": 2827}, {"_path": "Library/compute-sanitizer/docs/common/formatting/bg-vert.png", "path_type": "hardlink", "sha256": "8c63966094e18042f6018f70fd1dd16e5dff159f5d9581175d08c95566b780a5", "sha256_in_prefix": "8c63966094e18042f6018f70fd1dd16e5dff159f5d9581175d08c95566b780a5", "size_in_bytes": 152}, {"_path": "Library/compute-sanitizer/docs/common/formatting/common.min.js", "path_type": "hardlink", "sha256": "4d9455011e8b9cc20b2e1251e83008d430aeaf9bc41e863a6dede0cc70ffca5e", "sha256_in_prefix": "4d9455011e8b9cc20b2e1251e83008d430aeaf9bc41e863a6dede0cc70ffca5e", "size_in_bytes": 10628}, {"_path": "Library/compute-sanitizer/docs/common/formatting/commonltr.css", "path_type": "hardlink", "sha256": "7ce842c6f02e99f86211c774d1952c82346dd93241234f2508e3bfc8fe18843b", "sha256_in_prefix": "7ce842c6f02e99f86211c774d1952c82346dd93241234f2508e3bfc8fe18843b", "size_in_bytes": 6097}, {"_path": "Library/compute-sanitizer/docs/common/formatting/cppapiref.css", "path_type": "hardlink", "sha256": "493c77adf31d93385ea75f6fb397f0559bbe0a2c2c25a6f851c316719f7f3d43", "sha256_in_prefix": "493c77adf31d93385ea75f6fb397f0559bbe0a2c2c25a6f851c316719f7f3d43", "size_in_bytes": 8927}, {"_path": "Library/compute-sanitizer/docs/common/formatting/cuda-toolkit-documentation.png", "path_type": "hardlink", "sha256": "82d8bc92cc62f02fbe75fbf41f6315ed061f3c6e3ee26f49dafe077e6d6f1c8c", "sha256_in_prefix": "82d8bc92cc62f02fbe75fbf41f6315ed061f3c6e3ee26f49dafe077e6d6f1c8c", "size_in_bytes": 9129}, {"_path": "Library/compute-sanitizer/docs/common/formatting/devtools-documentation.png", "path_type": "hardlink", "sha256": "cc7ec5dff58454e4737bc444cd94bad996750a0161ebe178b589a7fc8d7c201f", "sha256_in_prefix": "cc7ec5dff58454e4737bc444cd94bad996750a0161ebe178b589a7fc8d7c201f", "size_in_bytes": 4359}, {"_path": "Library/compute-sanitizer/docs/common/formatting/devzone.png", "path_type": "hardlink", "sha256": "fabd58f2f65a43196f795a2e722f706610b5a03edeefe2967f58ca4de5048830", "sha256_in_prefix": "fabd58f2f65a43196f795a2e722f706610b5a03edeefe2967f58ca4de5048830", "size_in_bytes": 10349}, {"_path": "Library/compute-sanitizer/docs/common/formatting/dita.style.css", "path_type": "hardlink", "sha256": "981642d4c79d4f0dae02dd76e3b1dfddc4e6f53ef9866646e5b12eabe777d854", "sha256_in_prefix": "981642d4c79d4f0dae02dd76e3b1dfddc4e6f53ef9866646e5b12eabe777d854", "size_in_bytes": 34852}, {"_path": "Library/compute-sanitizer/docs/common/formatting/html5shiv-printshiv.min.js", "path_type": "hardlink", "sha256": "d32f2b6f288cbe3c58a98de7ac8c881d737e3b0f1eee8668463ad58d81cb4406", "sha256_in_prefix": "d32f2b6f288cbe3c58a98de7ac8c881d737e3b0f1eee8668463ad58d81cb4406", "size_in_bytes": 3989}, {"_path": "Library/compute-sanitizer/docs/common/formatting/jquery.ba-hashchange.min.js", "path_type": "hardlink", "sha256": "4282e21b6be8696f0175368a9db457df1e29a3559d0c9a653920587556c1f99b", "sha256_in_prefix": "4282e21b6be8696f0175368a9db457df1e29a3559d0c9a653920587556c1f99b", "size_in_bytes": 1604}, {"_path": "Library/compute-sanitizer/docs/common/formatting/jquery.min.js", "path_type": "hardlink", "sha256": "20638e363fcc5152155f24b281303e17da62da62d24ef5dcf863b184d9a25734", "sha256_in_prefix": "20638e363fcc5152155f24b281303e17da62da62d24ef5dcf863b184d9a25734", "size_in_bytes": 92633}, {"_path": "Library/compute-sanitizer/docs/common/formatting/jquery.scrollintoview.min.js", "path_type": "hardlink", "sha256": "4da5b5d1e11984f23faf44f40368b521dc090360d51dd66a6f0b35b52c657309", "sha256_in_prefix": "4da5b5d1e11984f23faf44f40368b521dc090360d51dd66a6f0b35b52c657309", "size_in_bytes": 3501}, {"_path": "Library/compute-sanitizer/docs/common/formatting/magnify-dropdown.png", "path_type": "hardlink", "sha256": "c707d514fb80ece368e68f5a6c70c7ac6b357464be981262cc0911fc76a012df", "sha256_in_prefix": "c707d514fb80ece368e68f5a6c70c7ac6b357464be981262cc0911fc76a012df", "size_in_bytes": 1139}, {"_path": "Library/compute-sanitizer/docs/common/formatting/magnify.png", "path_type": "hardlink", "sha256": "396fcab4919640809426f31de2cba8c226f48f46beab7929b0001db96f519b53", "sha256_in_prefix": "396fcab4919640809426f31de2cba8c226f48f46beab7929b0001db96f519b53", "size_in_bytes": 1100}, {"_path": "Library/compute-sanitizer/docs/common/formatting/nvidia.png", "path_type": "hardlink", "sha256": "7f0030add67b951f07d168464059245cb9dcd01354fb5c6191e6638c1e026cbb", "sha256_in_prefix": "7f0030add67b951f07d168464059245cb9dcd01354fb5c6191e6638c1e026cbb", "size_in_bytes": 4442}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-Splus.js", "path_type": "hardlink", "sha256": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "sha256_in_prefix": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "size_in_bytes": 1369}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-aea.js", "path_type": "hardlink", "sha256": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "sha256_in_prefix": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "size_in_bytes": 1612}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-agc.js", "path_type": "hardlink", "sha256": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "sha256_in_prefix": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "size_in_bytes": 1612}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-apollo.js", "path_type": "hardlink", "sha256": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "sha256_in_prefix": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "size_in_bytes": 1612}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-basic.js", "path_type": "hardlink", "sha256": "ee03d3d4aeac8c7e4743ec5e3825c9859589b616624a02b9802745a284b1bf46", "sha256_in_prefix": "ee03d3d4aeac8c7e4743ec5e3825c9859589b616624a02b9802745a284b1bf46", "size_in_bytes": 1124}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-cbm.js", "path_type": "hardlink", "sha256": "ee03d3d4aeac8c7e4743ec5e3825c9859589b616624a02b9802745a284b1bf46", "sha256_in_prefix": "ee03d3d4aeac8c7e4743ec5e3825c9859589b616624a02b9802745a284b1bf46", "size_in_bytes": 1124}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-cl.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-clj.js", "path_type": "hardlink", "sha256": "ed3516d94150cff8e6d138891650d2e5ed888fdf9a2ba21b80066136fa9c4596", "sha256_in_prefix": "ed3516d94150cff8e6d138891650d2e5ed888fdf9a2ba21b80066136fa9c4596", "size_in_bytes": 1484}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-css.js", "path_type": "hardlink", "sha256": "75425acf4f641d7f875abc0c7e05641ce1cf27f5651e784e7bc98dd66db1d94c", "sha256_in_prefix": "75425acf4f641d7f875abc0c7e05641ce1cf27f5651e784e7bc98dd66db1d94c", "size_in_bytes": 1525}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-dart.js", "path_type": "hardlink", "sha256": "49159396e691ef19d5305e63e04a5495fae560a66f1934da248a3556b392e534", "sha256_in_prefix": "49159396e691ef19d5305e63e04a5495fae560a66f1934da248a3556b392e534", "size_in_bytes": 1626}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-el.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-erl.js", "path_type": "hardlink", "sha256": "bcfdc2cbf090e3f60a741fe8675af943c3b5c93f63b6d308235bf8a11581540f", "sha256_in_prefix": "bcfdc2cbf090e3f60a741fe8675af943c3b5c93f63b6d308235bf8a11581540f", "size_in_bytes": 1208}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-erlang.js", "path_type": "hardlink", "sha256": "bcfdc2cbf090e3f60a741fe8675af943c3b5c93f63b6d308235bf8a11581540f", "sha256_in_prefix": "bcfdc2cbf090e3f60a741fe8675af943c3b5c93f63b6d308235bf8a11581540f", "size_in_bytes": 1208}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-fs.js", "path_type": "hardlink", "sha256": "e0ba91f5f5283ac855200290d4790b96a11cdfdffeb24a8c66431d4c8c592c65", "sha256_in_prefix": "e0ba91f5f5283ac855200290d4790b96a11cdfdffeb24a8c66431d4c8c592c65", "size_in_bytes": 1711}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-go.js", "path_type": "hardlink", "sha256": "b5adc88320698d36a12fc38e4deba9b6d322842eea43a656c914c03ddb4ddc78", "sha256_in_prefix": "b5adc88320698d36a12fc38e4deba9b6d322842eea43a656c914c03ddb4ddc78", "size_in_bytes": 884}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-hs.js", "path_type": "hardlink", "sha256": "ef49ed0fc4d7ed45b2326efe60522ab042adc19c86b6900a8e69211c8e3b4413", "sha256_in_prefix": "ef49ed0fc4d7ed45b2326efe60522ab042adc19c86b6900a8e69211c8e3b4413", "size_in_bytes": 1217}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-lasso.js", "path_type": "hardlink", "sha256": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "sha256_in_prefix": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "size_in_bytes": 2742}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-lassoscript.js", "path_type": "hardlink", "sha256": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "sha256_in_prefix": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "size_in_bytes": 2742}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-latex.js", "path_type": "hardlink", "sha256": "75bcea78e9d34891ab7d17b611e0dbdcb65ec412fd1e041c74276c61a9a02da2", "sha256_in_prefix": "75bcea78e9d34891ab7d17b611e0dbdcb65ec412fd1e041c74276c61a9a02da2", "size_in_bytes": 875}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-lgt.js", "path_type": "hardlink", "sha256": "dbbedcec7a8872a1637c7e4b92f0aed39c307293ca7824b90e1450854d735eb5", "sha256_in_prefix": "dbbedcec7a8872a1637c7e4b92f0aed39c307293ca7824b90e1450854d735eb5", "size_in_bytes": 1428}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-lisp.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-ll.js", "path_type": "hardlink", "sha256": "6062b885a32525740e86fd47d38b1a32c3a688cea9af11f16cf836658d6bca64", "sha256_in_prefix": "6062b885a32525740e86fd47d38b1a32c3a688cea9af11f16cf836658d6bca64", "size_in_bytes": 971}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-llvm.js", "path_type": "hardlink", "sha256": "6062b885a32525740e86fd47d38b1a32c3a688cea9af11f16cf836658d6bca64", "sha256_in_prefix": "6062b885a32525740e86fd47d38b1a32c3a688cea9af11f16cf836658d6bca64", "size_in_bytes": 971}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-logtalk.js", "path_type": "hardlink", "sha256": "dbbedcec7a8872a1637c7e4b92f0aed39c307293ca7824b90e1450854d735eb5", "sha256_in_prefix": "dbbedcec7a8872a1637c7e4b92f0aed39c307293ca7824b90e1450854d735eb5", "size_in_bytes": 1428}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-ls.js", "path_type": "hardlink", "sha256": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "sha256_in_prefix": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "size_in_bytes": 2742}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-lsp.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-lua.js", "path_type": "hardlink", "sha256": "6cdb5f35177e81de9d84995b2bb5b4bddae18ee53f8eeb4e5acf53d166bcf990", "sha256_in_prefix": "6cdb5f35177e81de9d84995b2bb5b4bddae18ee53f8eeb4e5acf53d166bcf990", "size_in_bytes": 1162}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-matlab.js", "path_type": "hardlink", "sha256": "ad8391672b553272b0a639f949f80a4457dad4777c6d9f41fec54d5536afeb9c", "sha256_in_prefix": "ad8391672b553272b0a639f949f80a4457dad4777c6d9f41fec54d5536afeb9c", "size_in_bytes": 21092}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-ml.js", "path_type": "hardlink", "sha256": "e0ba91f5f5283ac855200290d4790b96a11cdfdffeb24a8c66431d4c8c592c65", "sha256_in_prefix": "e0ba91f5f5283ac855200290d4790b96a11cdfdffeb24a8c66431d4c8c592c65", "size_in_bytes": 1711}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-mumps.js", "path_type": "hardlink", "sha256": "a0a16488395d2be1815e241c9e4521595d2917194a5a50ec98649dc58bef199d", "sha256_in_prefix": "a0a16488395d2be1815e241c9e4521595d2917194a5a50ec98649dc58bef199d", "size_in_bytes": 1500}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-n.js", "path_type": "hardlink", "sha256": "06b1b4d8d4c93b2d536d2d85405a99c48bfc722a22354b6fa1da374df1bc01d7", "sha256_in_prefix": "06b1b4d8d4c93b2d536d2d85405a99c48bfc722a22354b6fa1da374df1bc01d7", "size_in_bytes": 2069}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-nemerle.js", "path_type": "hardlink", "sha256": "06b1b4d8d4c93b2d536d2d85405a99c48bfc722a22354b6fa1da374df1bc01d7", "sha256_in_prefix": "06b1b4d8d4c93b2d536d2d85405a99c48bfc722a22354b6fa1da374df1bc01d7", "size_in_bytes": 2069}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-pascal.js", "path_type": "hardlink", "sha256": "6eed79bfdd3179ef0ea800304c089736d1e45048785d1f771862f73f2ef4b7ec", "sha256_in_prefix": "6eed79bfdd3179ef0ea800304c089736d1e45048785d1f771862f73f2ef4b7ec", "size_in_bytes": 1332}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-proto.js", "path_type": "hardlink", "sha256": "571dc03af497e5d30ccbe4a01b315e01eda9380d8b06139ea2e219745d887e12", "sha256_in_prefix": "571dc03af497e5d30ccbe4a01b315e01eda9380d8b06139ea2e219745d887e12", "size_in_bytes": 891}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-r.js", "path_type": "hardlink", "sha256": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "sha256_in_prefix": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "size_in_bytes": 1369}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-rd.js", "path_type": "hardlink", "sha256": "50fdd7c7de82fc381b3147847eeec0980064f714e9ff45411f35cd5b1aeae7b0", "sha256_in_prefix": "50fdd7c7de82fc381b3147847eeec0980064f714e9ff45411f35cd5b1aeae7b0", "size_in_bytes": 862}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-rkt.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-rust.js", "path_type": "hardlink", "sha256": "a0dd431a0fd90648cdd47a61c2d1443059aa99f00c65c826f845a0b4eed0b7be", "sha256_in_prefix": "a0dd431a0fd90648cdd47a61c2d1443059aa99f00c65c826f845a0b4eed0b7be", "size_in_bytes": 2254}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-s.js", "path_type": "hardlink", "sha256": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "sha256_in_prefix": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "size_in_bytes": 1369}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-scala.js", "path_type": "hardlink", "sha256": "99cee57320279566c37f295a7e8a6a865936e935aba3c26a7905fb86f8de44df", "sha256_in_prefix": "99cee57320279566c37f295a7e8a6a865936e935aba3c26a7905fb86f8de44df", "size_in_bytes": 1554}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-scm.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-sql.js", "path_type": "hardlink", "sha256": "8699e27b169ce06ccb9de38687b07ce364e6fa270b9a489a117e88ba143f528d", "sha256_in_prefix": "8699e27b169ce06ccb9de38687b07ce364e6fa270b9a489a117e88ba143f528d", "size_in_bytes": 2404}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-ss.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-swift.js", "path_type": "hardlink", "sha256": "6cfa8bd06ed6a1789fc4dcc0a14c6b3ce0d1ad9c0e0af620e36475db7c7aaf5e", "sha256_in_prefix": "6cfa8bd06ed6a1789fc4dcc0a14c6b3ce0d1ad9c0e0af620e36475db7c7aaf5e", "size_in_bytes": 2050}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-tcl.js", "path_type": "hardlink", "sha256": "5b55d847b376ba4e08009dde17982c5423ad46f0d86ab5ff64d02cb1123f4d22", "sha256_in_prefix": "5b55d847b376ba4e08009dde17982c5423ad46f0d86ab5ff64d02cb1123f4d22", "size_in_bytes": 1261}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-tex.js", "path_type": "hardlink", "sha256": "75bcea78e9d34891ab7d17b611e0dbdcb65ec412fd1e041c74276c61a9a02da2", "sha256_in_prefix": "75bcea78e9d34891ab7d17b611e0dbdcb65ec412fd1e041c74276c61a9a02da2", "size_in_bytes": 875}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-vb.js", "path_type": "hardlink", "sha256": "99333f0a0c15540af9fbbc87c8d3afca5876b77759b1c9b2ad634a53bf4917a6", "sha256_in_prefix": "99333f0a0c15540af9fbbc87c8d3afca5876b77759b1c9b2ad634a53bf4917a6", "size_in_bytes": 2423}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-vbs.js", "path_type": "hardlink", "sha256": "99333f0a0c15540af9fbbc87c8d3afca5876b77759b1c9b2ad634a53bf4917a6", "sha256_in_prefix": "99333f0a0c15540af9fbbc87c8d3afca5876b77759b1c9b2ad634a53bf4917a6", "size_in_bytes": 2423}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-vhd.js", "path_type": "hardlink", "sha256": "3cc5b831500770fe70871d57f86e44d8b79b22704054198422cba0779b271ca6", "sha256_in_prefix": "3cc5b831500770fe70871d57f86e44d8b79b22704054198422cba0779b271ca6", "size_in_bytes": 2054}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-vhdl.js", "path_type": "hardlink", "sha256": "3cc5b831500770fe70871d57f86e44d8b79b22704054198422cba0779b271ca6", "sha256_in_prefix": "3cc5b831500770fe70871d57f86e44d8b79b22704054198422cba0779b271ca6", "size_in_bytes": 2054}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-wiki.js", "path_type": "hardlink", "sha256": "d1b3fef0de5de519f9a28b68949c7b5fc7a82cf77d6bfa3ff798471541de97b5", "sha256_in_prefix": "d1b3fef0de5de519f9a28b68949c7b5fc7a82cf77d6bfa3ff798471541de97b5", "size_in_bytes": 1157}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-xq.js", "path_type": "hardlink", "sha256": "929a1bc60c51777dd037a0b930fc5ef8826691f747d471d1b93ca57a85d6bcd7", "sha256_in_prefix": "929a1bc60c51777dd037a0b930fc5ef8826691f747d471d1b93ca57a85d6bcd7", "size_in_bytes": 23870}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-xquery.js", "path_type": "hardlink", "sha256": "929a1bc60c51777dd037a0b930fc5ef8826691f747d471d1b93ca57a85d6bcd7", "sha256_in_prefix": "929a1bc60c51777dd037a0b930fc5ef8826691f747d471d1b93ca57a85d6bcd7", "size_in_bytes": 23870}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-yaml.js", "path_type": "hardlink", "sha256": "292ebdeb7dbeae7d8d2ecaee62c714ac10b4620950ea19f02da214bda68b1c1d", "sha256_in_prefix": "292ebdeb7dbeae7d8d2ecaee62c714ac10b4620950ea19f02da214bda68b1c1d", "size_in_bytes": 1033}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/lang-yml.js", "path_type": "hardlink", "sha256": "292ebdeb7dbeae7d8d2ecaee62c714ac10b4620950ea19f02da214bda68b1c1d", "sha256_in_prefix": "292ebdeb7dbeae7d8d2ecaee62c714ac10b4620950ea19f02da214bda68b1c1d", "size_in_bytes": 1033}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/onLoad.png", "path_type": "hardlink", "sha256": "859e0d54ce7aae5de46f9ac67a24313fed8bd042baa8cd3135a1395db5aef5c6", "sha256_in_prefix": "859e0d54ce7aae5de46f9ac67a24313fed8bd042baa8cd3135a1395db5aef5c6", "size_in_bytes": 110}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/prettify.css", "path_type": "hardlink", "sha256": "1d2d628605d9eaa7d4712e414de849855f3511947e23943acf1e7219d12a6e9d", "sha256_in_prefix": "1d2d628605d9eaa7d4712e414de849855f3511947e23943acf1e7219d12a6e9d", "size_in_bytes": 675}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/prettify.js", "path_type": "hardlink", "sha256": "cba675a9c942285a5e742197a33b45b1ccc7b4d333ced035abbd97edc60b2302", "sha256_in_prefix": "cba675a9c942285a5e742197a33b45b1ccc7b4d333ced035abbd97edc60b2302", "size_in_bytes": 15307}, {"_path": "Library/compute-sanitizer/docs/common/formatting/prettify/run_prettify.js", "path_type": "hardlink", "sha256": "ed4b1bfd5321585d97a1f84ece8ae71c969a71791d1ec52d8dff47ede58ea91d", "sha256_in_prefix": "ed4b1bfd5321585d97a1f84ece8ae71c969a71791d1ec52d8dff47ede58ea91d", "size_in_bytes": 18100}, {"_path": "Library/compute-sanitizer/docs/common/formatting/qwcode.highlight.css", "path_type": "hardlink", "sha256": "5c59aff177b504d0b79a73c7b00ccda905c03c445489e06e12de6f7d2ec5bb41", "sha256_in_prefix": "5c59aff177b504d0b79a73c7b00ccda905c03c445489e06e12de6f7d2ec5bb41", "size_in_bytes": 908}, {"_path": "Library/compute-sanitizer/docs/common/formatting/search-clear.png", "path_type": "hardlink", "sha256": "3b9e4abc9fa8aaa24a459d69b63169f4a63a63468493e1d94a9857ae3a5a1c6c", "sha256_in_prefix": "3b9e4abc9fa8aaa24a459d69b63169f4a63a63468493e1d94a9857ae3a5a1c6c", "size_in_bytes": 3638}, {"_path": "Library/compute-sanitizer/docs/common/formatting/site.css", "path_type": "hardlink", "sha256": "458308b26bdc1f943a15431e1393361c99c627957da87a0c5236bb3a4374b73e", "sha256_in_prefix": "458308b26bdc1f943a15431e1393361c99c627957da87a0c5236bb3a4374b73e", "size_in_bytes": 12447}, {"_path": "Library/compute-sanitizer/docs/common/scripts/google-analytics/google-analytics-tracker.js", "path_type": "hardlink", "sha256": "4b2a034fa4648a9624dc4ea51bb110259ff680993243598485f7c331405b108c", "sha256_in_prefix": "4b2a034fa4648a9624dc4ea51bb110259ff680993243598485f7c331405b108c", "size_in_bytes": 117}, {"_path": "Library/compute-sanitizer/docs/common/scripts/google-analytics/google-analytics-write.js", "path_type": "hardlink", "sha256": "c144a7cdd903ddc1941166e39f23045a6bb6b9425999d2f50e5e8950757ab532", "sha256_in_prefix": "c144a7cdd903ddc1941166e39f23045a6bb6b9425999d2f50e5e8950757ab532", "size_in_bytes": 221}, {"_path": "Library/compute-sanitizer/docs/common/scripts/tynt/tynt.js", "path_type": "hardlink", "sha256": "a3ac843bf09609f24c48cec49dbd832e294437a8559a7beaf688e532b43d5e95", "sha256_in_prefix": "a3ac843bf09609f24c48cec49dbd832e294437a8559a7beaf688e532b43d5e95", "size_in_bytes": 316}, {"_path": "Library/compute-sanitizer/docs/index.html", "path_type": "hardlink", "sha256": "7dee5c227de0ae49bcfb277a0d3326adaa418a3bc3666cf3bb14655e4e005f34", "sha256_in_prefix": "7dee5c227de0ae49bcfb277a0d3326adaa418a3bc3666cf3bb14655e4e005f34", "size_in_bytes": 6400}, {"_path": "Library/compute-sanitizer/docs/pdf/ComputeSanitizer.pdf", "path_type": "hardlink", "sha256": "419625b62266f61b6bd4a03c66014bcbfb53e04f7c7405a90d48d20aeba5ee85", "sha256_in_prefix": "419625b62266f61b6bd4a03c66014bcbfb53e04f7c7405a90d48d20aeba5ee85", "size_in_bytes": 1916809}, {"_path": "Library/compute-sanitizer/docs/pdf/CopyrightAndLicenses.pdf", "path_type": "hardlink", "sha256": "609177374ee3c7c23a89eb48a907add18ce7045428ecf0625f3d1d623fe563fe", "sha256_in_prefix": "609177374ee3c7c23a89eb48a907add18ce7045428ecf0625f3d1d623fe563fe", "size_in_bytes": 1492184}, {"_path": "Library/compute-sanitizer/docs/pdf/ReleaseNotes.pdf", "path_type": "hardlink", "sha256": "f8495e2245b76f5a7c1dc586baaead7a2dcf1cc11a00e017009de514d8d72b50", "sha256_in_prefix": "f8495e2245b76f5a7c1dc586baaead7a2dcf1cc11a00e017009de514d8d72b50", "size_in_bytes": 1518372}, {"_path": "Library/compute-sanitizer/docs/pdf/SanitizerApiGuide.pdf", "path_type": "hardlink", "sha256": "75dd9eb8cc40de113709049f60834b74f22cdbf667bc6524a69de146cc3ab411", "sha256_in_prefix": "75dd9eb8cc40de113709049f60834b74f22cdbf667bc6524a69de146cc3ab411", "size_in_bytes": 1531537}, {"_path": "Library/compute-sanitizer/docs/pdf/SanitizerNvtxGuide.pdf", "path_type": "hardlink", "sha256": "95f8b1c72b2f36555a14d74bfab985219e593985d66a977d4ddaff33fe9ecdf0", "sha256_in_prefix": "95f8b1c72b2f36555a14d74bfab985219e593985d66a977d4ddaff33fe9ecdf0", "size_in_bytes": 1514190}, {"_path": "Library/compute-sanitizer/docs/search/check.html", "path_type": "hardlink", "sha256": "cd37d034cab367b7b6fbb2718759c369ae00e855022053d9dffa70331dea0762", "sha256_in_prefix": "cd37d034cab367b7b6fbb2718759c369ae00e855022053d9dffa70331dea0762", "size_in_bytes": 1252}, {"_path": "Library/compute-sanitizer/docs/search/files.js", "path_type": "hardlink", "sha256": "30f5b2cef9216532cb7b86f3e9f35223c92908ae0221425c25ad3a63a1dd2fbe", "sha256_in_prefix": "30f5b2cef9216532cb7b86f3e9f35223c92908ae0221425c25ad3a63a1dd2fbe", "size_in_bytes": 99}, {"_path": "Library/compute-sanitizer/docs/search/htmlFileInfoList.js", "path_type": "hardlink", "sha256": "5d925bbfa815d82ce7e5bfedf8d77b5e47c87f07e5a5d7d2bbcf176c8e83a8ac", "sha256_in_prefix": "5d925bbfa815d82ce7e5bfedf8d77b5e47c87f07e5a5d7d2bbcf176c8e83a8ac", "size_in_bytes": 3489}, {"_path": "Library/compute-sanitizer/docs/search/htmlFileList.js", "path_type": "hardlink", "sha256": "77d2f151478bddb5c78fb9b9d9a78f12c97bf0a672045241bd3d841ee07542db", "sha256_in_prefix": "77d2f151478bddb5c78fb9b9d9a78f12c97bf0a672045241bd3d841ee07542db", "size_in_bytes": 2220}, {"_path": "Library/compute-sanitizer/docs/search/index-1.js", "path_type": "hardlink", "sha256": "c7eb3bf1a6998923360f4e927d3b3af87e86d23368204adcc5b8a75db6bf741d", "sha256_in_prefix": "c7eb3bf1a6998923360f4e927d3b3af87e86d23368204adcc5b8a75db6bf741d", "size_in_bytes": 28267}, {"_path": "Library/compute-sanitizer/docs/search/index-2.js", "path_type": "hardlink", "sha256": "7b6d00ed1dccfeeb11e5bc487d296cc60193f166e252accc14a41690ec56eaec", "sha256_in_prefix": "7b6d00ed1dccfeeb11e5bc487d296cc60193f166e252accc14a41690ec56eaec", "size_in_bytes": 32499}, {"_path": "Library/compute-sanitizer/docs/search/index-3.js", "path_type": "hardlink", "sha256": "3c282c8ad30a8f1f93456be46cb48cd0fefd0160a9c2df71ac439c84d4fe265b", "sha256_in_prefix": "3c282c8ad30a8f1f93456be46cb48cd0fefd0160a9c2df71ac439c84d4fe265b", "size_in_bytes": 39178}, {"_path": "Library/compute-sanitizer/docs/search/nwSearchFnt.min.js", "path_type": "hardlink", "sha256": "aa150eb8635cb8df0e268e67a405cbb84a01033c5c5f4172fe947179899a4a86", "sha256_in_prefix": "aa150eb8635cb8df0e268e67a405cbb84a01033c5c5f4172fe947179899a4a86", "size_in_bytes": 12073}, {"_path": "Library/compute-sanitizer/docs/search/stemmers/en_stemmer.min.js", "path_type": "hardlink", "sha256": "ce0241b6853bb62e6baefaaee419baa458b5ec05181a7107636855411f1fb263", "sha256_in_prefix": "ce0241b6853bb62e6baefaaee419baa458b5ec05181a7107636855411f1fb263", "size_in_bytes": 3531}, {"_path": "Library/compute-sanitizer/include/generated_cudaD3D10_meta.h", "path_type": "hardlink", "sha256": "3d21cf541fc64a3e8d111477fc94dc9ebbfed929aea5609eb69602f32745db8a", "sha256_in_prefix": "3d21cf541fc64a3e8d111477fc94dc9ebbfed929aea5609eb69602f32745db8a", "size_in_bytes": 4439}, {"_path": "Library/compute-sanitizer/include/generated_cudaD3D11_meta.h", "path_type": "hardlink", "sha256": "fa87ddc95dab6f95e25d319aea7c64020f673e42e6b1ab963a3710bbc461d16d", "sha256_in_prefix": "fa87ddc95dab6f95e25d319aea7c64020f673e42e6b1ab963a3710bbc461d16d", "size_in_bytes": 1823}, {"_path": "Library/compute-sanitizer/include/generated_cudaD3D9_meta.h", "path_type": "hardlink", "sha256": "89a25b6eee7cbc560ecaee18b97d9007122d1f4f62a096dae0296f30655fddd0", "sha256_in_prefix": "89a25b6eee7cbc560ecaee18b97d9007122d1f4f62a096dae0296f30655fddd0", "size_in_bytes": 5379}, {"_path": "Library/compute-sanitizer/include/generated_cudaGL_meta.h", "path_type": "hardlink", "sha256": "67e4b157a27f114e8cdd3c6cffac917ac38672a6ce0931f47c640eb99b371117", "sha256_in_prefix": "67e4b157a27f114e8cdd3c6cffac917ac38672a6ce0931f47c640eb99b371117", "size_in_bytes": 3344}, {"_path": "Library/compute-sanitizer/include/generated_cuda_d3d10_interop_meta.h", "path_type": "hardlink", "sha256": "f5f27e2aaff104e93316f7c11f158a65bd358b982952be13e25bde0cb7238ca0", "sha256_in_prefix": "f5f27e2aaff104e93316f7c11f158a65bd358b982952be13e25bde0cb7238ca0", "size_in_bytes": 3366}, {"_path": "Library/compute-sanitizer/include/generated_cuda_d3d11_interop_meta.h", "path_type": "hardlink", "sha256": "36cb61c510245590b0cb862997d405d69714b48ff8bdc85ddf7c9a7aedfad637", "sha256_in_prefix": "36cb61c510245590b0cb862997d405d69714b48ff8bdc85ddf7c9a7aedfad637", "size_in_bytes": 1502}, {"_path": "Library/compute-sanitizer/include/generated_cuda_d3d9_interop_meta.h", "path_type": "hardlink", "sha256": "b57f91a7968819fc04f271af446ee2fc58dcc1624d623cc69fa0262e4a9c117f", "sha256_in_prefix": "b57f91a7968819fc04f271af446ee2fc58dcc1624d623cc69fa0262e4a9c117f", "size_in_bytes": 4180}, {"_path": "Library/compute-sanitizer/include/generated_cuda_gl_interop_meta.h", "path_type": "hardlink", "sha256": "e78afc412e339da36d5e3fb66250f9a0c97eef86eee10fa3dffbbc7a1cd3ee4d", "sha256_in_prefix": "e78afc412e339da36d5e3fb66250f9a0c97eef86eee10fa3dffbbc7a1cd3ee4d", "size_in_bytes": 2494}, {"_path": "Library/compute-sanitizer/include/generated_cuda_meta.h", "path_type": "hardlink", "sha256": "c068cda14a366604523abc7a7bb2f65921336286875dd7e0ccf3bace22d600ec", "sha256_in_prefix": "c068cda14a366604523abc7a7bb2f65921336286875dd7e0ccf3bace22d600ec", "size_in_bytes": 86914}, {"_path": "Library/compute-sanitizer/include/generated_cuda_profiler_api_meta.h", "path_type": "hardlink", "sha256": "9fc1cd434c21fc94b3a710dfbcd5c2c67aa82982c83b86e017f95e4cccaa2645", "sha256_in_prefix": "9fc1cd434c21fc94b3a710dfbcd5c2c67aa82982c83b86e017f95e4cccaa2645", "size_in_bytes": 578}, {"_path": "Library/compute-sanitizer/include/generated_cuda_runtime_api_meta.h", "path_type": "hardlink", "sha256": "7b5e438d8069c1f3878a4f9bb4b2d9350d574ce48c8ae94b9adabdfffef69151", "sha256_in_prefix": "7b5e438d8069c1f3878a4f9bb4b2d9350d574ce48c8ae94b9adabdfffef69151", "size_in_bytes": 68695}, {"_path": "Library/compute-sanitizer/include/sanitizer.h", "path_type": "hardlink", "sha256": "c9578a7b0eb637bb2a4c0fa2fca9dd4270851186a3593f92b337644a066bfcf8", "sha256_in_prefix": "c9578a7b0eb637bb2a4c0fa2fca9dd4270851186a3593f92b337644a066bfcf8", "size_in_bytes": 3525}, {"_path": "Library/compute-sanitizer/include/sanitizer_barrier.h", "path_type": "hardlink", "sha256": "7e72a82a4569a02d1ac2f972f672ef7696948491e5c81098a22ee7e2683bf53a", "sha256_in_prefix": "7e72a82a4569a02d1ac2f972f672ef7696948491e5c81098a22ee7e2683bf53a", "size_in_bytes": 3864}, {"_path": "Library/compute-sanitizer/include/sanitizer_callbacks.h", "path_type": "hardlink", "sha256": "f777a343cd671311e449d765b8a3a8293c14ab198f841e90d3820b727f2f96ff", "sha256_in_prefix": "f777a343cd671311e449d765b8a3a8293c14ab198f841e90d3820b727f2f96ff", "size_in_bytes": 59745}, {"_path": "Library/compute-sanitizer/include/sanitizer_driver_cbid.h", "path_type": "hardlink", "sha256": "65c6d6f6f17b9664708f02a4ad5f990375b3b9082e6cf80dab79e7463403ae5e", "sha256_in_prefix": "65c6d6f6f17b9664708f02a4ad5f990375b3b9082e6cf80dab79e7463403ae5e", "size_in_bytes": 74560}, {"_path": "Library/compute-sanitizer/include/sanitizer_memory.h", "path_type": "hardlink", "sha256": "9b4dde39dd9957b82b71ee115d3f39cb6c1c4f385353cd6b229c73af8699d24e", "sha256_in_prefix": "9b4dde39dd9957b82b71ee115d3f39cb6c1c4f385353cd6b229c73af8699d24e", "size_in_bytes": 7565}, {"_path": "Library/compute-sanitizer/include/sanitizer_patching.h", "path_type": "hardlink", "sha256": "534267293412388e7d33b5b74583e2feeea573096bd981804df617efd0078176", "sha256_in_prefix": "534267293412388e7d33b5b74583e2feeea573096bd981804df617efd0078176", "size_in_bytes": 40428}, {"_path": "Library/compute-sanitizer/include/sanitizer_result.h", "path_type": "hardlink", "sha256": "cf8c57e9e0824b7f22cf37430dc8b0354770c41137069b286e99d72e925791cc", "sha256_in_prefix": "cf8c57e9e0824b7f22cf37430dc8b0354770c41137069b286e99d72e925791cc", "size_in_bytes": 6251}, {"_path": "Library/compute-sanitizer/include/sanitizer_runtime_cbid.h", "path_type": "hardlink", "sha256": "30f58465958959243ea3f1ff9db226a82d7e14bdb394dc96e61ab5bad917870e", "sha256_in_prefix": "30f58465958959243ea3f1ff9db226a82d7e14bdb394dc96e61ab5bad917870e", "size_in_bytes": 45517}, {"_path": "Library/compute-sanitizer/include/sanitizer_stream.h", "path_type": "hardlink", "sha256": "b0b23a6f18b4b7111475126d62bcc7e643e03ef13ac0a02d1770697b975a894f", "sha256_in_prefix": "b0b23a6f18b4b7111475126d62bcc7e643e03ef13ac0a02d1770697b975a894f", "size_in_bytes": 4965}, {"_path": "Library/compute-sanitizer/sanitizer-collection.dll", "path_type": "hardlink", "sha256": "1721e7681a71f358e5cd52c79576cb64114a6f2e4fce27ec850fedd8094c884e", "sha256_in_prefix": "1721e7681a71f358e5cd52c79576cb64114a6f2e4fce27ec850fedd8094c884e", "size_in_bytes": 5743704}, {"_path": "Library/compute-sanitizer/sanitizer-public.dll", "path_type": "hardlink", "sha256": "318529eb50b3dd9667a540b3936c88512f16c8025aa70c426e785759b0e89999", "sha256_in_prefix": "318529eb50b3dd9667a540b3936c88512f16c8025aa70c426e785759b0e89999", "size_in_bytes": 930904}, {"_path": "Library/compute-sanitizer/sanitizer-public.lib", "path_type": "hardlink", "sha256": "5e8e763d9a4b7912e1ddaf70936a64e5c0d59a097a7802061d23196d60c0a90c", "sha256_in_prefix": "5e8e763d9a4b7912e1ddaf70936a64e5c0d59a097a7802061d23196d60c0a90c", "size_in_bytes": 9692}, {"_path": "Scripts/compute-sanitizer.bat", "path_type": "hardlink", "sha256": "380a8d162fbc6b20b5560b17c05518c3a27d65eda6a8efdf8dde13fcecef6660", "sha256_in_prefix": "380a8d162fbc6b20b5560b17c05518c3a27d65eda6a8efdf8dde13fcecef6660", "size_in_bytes": 73}], "paths_version": 1}, "requested_spec": "None", "sha256": "d5f7bbb1bb4e4f736694a50933bdd9ebe7092fb0da7c12edf7e5358b7d3f9452", "size": 6848121, "subdir": "win-64", "timestamp": 1715632653000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-sanitizer-api-12.4.127-hd77b12b_1.conda", "version": "12.4.127"}