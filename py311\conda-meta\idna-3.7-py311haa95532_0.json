{"build": "py311haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["python >=3.11,<3.12.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\idna-3.7-py311haa95532_0", "files": ["Lib/site-packages/idna-3.7.dist-info/INSTALLER", "Lib/site-packages/idna-3.7.dist-info/LICENSE.md", "Lib/site-packages/idna-3.7.dist-info/METADATA", "Lib/site-packages/idna-3.7.dist-info/RECORD", "Lib/site-packages/idna-3.7.dist-info/REQUESTED", "Lib/site-packages/idna-3.7.dist-info/WHEEL", "Lib/site-packages/idna-3.7.dist-info/direct_url.json", "Lib/site-packages/idna/__init__.py", "Lib/site-packages/idna/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/idna/__pycache__/codec.cpython-311.pyc", "Lib/site-packages/idna/__pycache__/compat.cpython-311.pyc", "Lib/site-packages/idna/__pycache__/core.cpython-311.pyc", "Lib/site-packages/idna/__pycache__/idnadata.cpython-311.pyc", "Lib/site-packages/idna/__pycache__/intranges.cpython-311.pyc", "Lib/site-packages/idna/__pycache__/package_data.cpython-311.pyc", "Lib/site-packages/idna/__pycache__/uts46data.cpython-311.pyc", "Lib/site-packages/idna/codec.py", "Lib/site-packages/idna/compat.py", "Lib/site-packages/idna/core.py", "Lib/site-packages/idna/idnadata.py", "Lib/site-packages/idna/intranges.py", "Lib/site-packages/idna/package_data.py", "Lib/site-packages/idna/py.typed", "Lib/site-packages/idna/uts46data.py"], "fn": "idna-3.7-py311haa95532_0.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "D:\\anaconda3\\pkgs\\idna-3.7-py311haa95532_0", "type": 1}, "md5": "05eef0b8a98aae41fffb2bc576530e8f", "name": "idna", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\idna-3.7-py311haa95532_0.conda", "paths_data": {"paths": [{"_path": "Lib/site-packages/idna-3.7.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/idna-3.7.dist-info/LICENSE.md", "path_type": "hardlink", "sha256": "a59f0b0ef3635874109a4461ca44ff7a70d50696e814767bfaf721d4c9b0db0f", "sha256_in_prefix": "a59f0b0ef3635874109a4461ca44ff7a70d50696e814767bfaf721d4c9b0db0f", "size_in_bytes": 1541}, {"_path": "Lib/site-packages/idna-3.7.dist-info/METADATA", "path_type": "hardlink", "sha256": "3a2c4293e74a2d990fcbe31fbe23a688fbf02753b62bff2ba82ac58c2feec72e", "sha256_in_prefix": "3a2c4293e74a2d990fcbe31fbe23a688fbf02753b62bff2ba82ac58c2feec72e", "size_in_bytes": 9888}, {"_path": "Lib/site-packages/idna-3.7.dist-info/RECORD", "path_type": "hardlink", "sha256": "7239bf1e5ff1e2f31a01f47106fe24cbb2235a21bad82b1d98b98a0c58caae29", "sha256_in_prefix": "7239bf1e5ff1e2f31a01f47106fe24cbb2235a21bad82b1d98b98a0c58caae29", "size_in_bytes": 1551}, {"_path": "Lib/site-packages/idna-3.7.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/idna-3.7.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "Lib/site-packages/idna-3.7.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "28bfbe0dc4bed7c1d92c70c87bc6c5bcf3140acc5dd9736ad2f17ebac2058a17", "sha256_in_prefix": "28bfbe0dc4bed7c1d92c70c87bc6c5bcf3140acc5dd9736ad2f17ebac2058a17", "size_in_bytes": 84}, {"_path": "Lib/site-packages/idna/__init__.py", "path_type": "hardlink", "sha256": "28940dd5e401afc8882b948aac9e3b957bf11b4049ecb9b7f16e334f4bfff259", "sha256_in_prefix": "28940dd5e401afc8882b948aac9e3b957bf11b4049ecb9b7f16e334f4bfff259", "size_in_bytes": 849}, {"_path": "Lib/site-packages/idna/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "c9c4ecd1d07ad2df9a09cfe12b92b320a2c93673aadd456a61be182c4e2c8cf2", "sha256_in_prefix": "c9c4ecd1d07ad2df9a09cfe12b92b320a2c93673aadd456a61be182c4e2c8cf2", "size_in_bytes": 1045}, {"_path": "Lib/site-packages/idna/__pycache__/codec.cpython-311.pyc", "path_type": "hardlink", "sha256": "14088e2b0baa9a1869b990969c3968476025aece5421895adec727c5b299847f", "sha256_in_prefix": "14088e2b0baa9a1869b990969c3968476025aece5421895adec727c5b299847f", "size_in_bytes": 5732}, {"_path": "Lib/site-packages/idna/__pycache__/compat.cpython-311.pyc", "path_type": "hardlink", "sha256": "41f833193f8c0d8e8c97cc7b2f391e7f2e15fe606348deac0405d0e191beb2da", "sha256_in_prefix": "41f833193f8c0d8e8c97cc7b2f391e7f2e15fe606348deac0405d0e191beb2da", "size_in_bytes": 962}, {"_path": "Lib/site-packages/idna/__pycache__/core.cpython-311.pyc", "path_type": "hardlink", "sha256": "f535620ddd23253934f71616231e0349ac186629dd183f996d48fe22ea81f17d", "sha256_in_prefix": "f535620ddd23253934f71616231e0349ac186629dd183f996d48fe22ea81f17d", "size_in_bytes": 18847}, {"_path": "Lib/site-packages/idna/__pycache__/idnadata.cpython-311.pyc", "path_type": "hardlink", "sha256": "061784731af98ef0394ee5dc83cd064ac43bebf82b57fd3dd97e6e0196de0b1f", "sha256_in_prefix": "061784731af98ef0394ee5dc83cd064ac43bebf82b57fd3dd97e6e0196de0b1f", "size_in_bytes": 101491}, {"_path": "Lib/site-packages/idna/__pycache__/intranges.cpython-311.pyc", "path_type": "hardlink", "sha256": "eb4d87efa70ddd47cf9100344e61b5cbe0c380ffdfaa16365bf17227f238a223", "sha256_in_prefix": "eb4d87efa70ddd47cf9100344e61b5cbe0c380ffdfaa16365bf17227f238a223", "size_in_bytes": 2930}, {"_path": "Lib/site-packages/idna/__pycache__/package_data.cpython-311.pyc", "path_type": "hardlink", "sha256": "542e13bc5a761f84be7a475f981e224ecea1e92d365a056888bd1f12229decca", "sha256_in_prefix": "542e13bc5a761f84be7a475f981e224ecea1e92d365a056888bd1f12229decca", "size_in_bytes": 165}, {"_path": "Lib/site-packages/idna/__pycache__/uts46data.cpython-311.pyc", "path_type": "hardlink", "sha256": "cc34ca77f9d6a79f6ff506fbee19bbe532adee01a3b99e0cdce3217c1db0c9de", "sha256_in_prefix": "cc34ca77f9d6a79f6ff506fbee19bbe532adee01a3b99e0cdce3217c1db0c9de", "size_in_bytes": 163123}, {"_path": "Lib/site-packages/idna/codec.py", "path_type": "hardlink", "sha256": "3d2ea6f9799d493ed68fb27bba544c6a43c3b7910127262b4f708fb6387eeede", "sha256_in_prefix": "3d2ea6f9799d493ed68fb27bba544c6a43c3b7910127262b4f708fb6387eeede", "size_in_bytes": 3426}, {"_path": "Lib/site-packages/idna/compat.py", "path_type": "hardlink", "sha256": "d3fb0e114313e02570f5da03defc91857f345f5f4fc2a168501b3b816b05304e", "sha256_in_prefix": "d3fb0e114313e02570f5da03defc91857f345f5f4fc2a168501b3b816b05304e", "size_in_bytes": 321}, {"_path": "Lib/site-packages/idna/core.py", "path_type": "hardlink", "sha256": "972869a1edafba511a07feb9c615e6a0a80efb152a143bdcc31bb986934d3b81", "sha256_in_prefix": "972869a1edafba511a07feb9c615e6a0a80efb152a143bdcc31bb986934d3b81", "size_in_bytes": 12663}, {"_path": "Lib/site-packages/idna/idnadata.py", "path_type": "hardlink", "sha256": "76a470cadce48c81cc05ad91d6562f1c3c0009e9d93edf1e195bb563c50113e1", "sha256_in_prefix": "76a470cadce48c81cc05ad91d6562f1c3c0009e9d93edf1e195bb563c50113e1", "size_in_bytes": 78320}, {"_path": "Lib/site-packages/idna/intranges.py", "path_type": "hardlink", "sha256": "601af87d162e587ee44ca4b6b579458ccdb8645d4f76f722afe6b2c278889ea8", "sha256_in_prefix": "601af87d162e587ee44ca4b6b579458ccdb8645d4f76f722afe6b2c278889ea8", "size_in_bytes": 1881}, {"_path": "Lib/site-packages/idna/package_data.py", "path_type": "hardlink", "sha256": "4e4b742a721ec889671dd74e6b3f564a4922b25360a24240b84fa9e46a2b32aa", "sha256_in_prefix": "4e4b742a721ec889671dd74e6b3f564a4922b25360a24240b84fa9e46a2b32aa", "size_in_bytes": 21}, {"_path": "Lib/site-packages/idna/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/idna/uts46data.py", "path_type": "hardlink", "sha256": "d4aba4b16a8bb9c70f5e6daec9156485f8852cd22133f1f69b86b309c9cea845", "sha256_in_prefix": "d4aba4b16a8bb9c70f5e6daec9156485f8852cd22133f1f69b86b309c9cea845", "size_in_bytes": 206503}], "paths_version": 1}, "requested_spec": "None", "sha256": "36b9053c54a55a9a4d7ed8d27470a7fed70ed0a4a6e72e4273e6f02e7408e18f", "size": 136001, "subdir": "win-64", "timestamp": 1714399061000, "url": "https://repo.anaconda.com/pkgs/main/win-64/idna-3.7-py311haa95532_0.conda", "version": "3.7"}