{"build": "haa95532_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["cuda-cupti-dev 12.4.127.*", "cuda-nvdisasm 12.4.127.*", "cuda-nvprof 12.4.127.*", "cuda-sanitizer-api 12.4.127.*"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-command-line-tools-12.4.1-haa95532_1", "files": [], "fn": "cuda-command-line-tools-12.4.1-haa95532_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-command-line-tools-12.4.1-haa95532_1", "type": 1}, "md5": "75096621de5ad7c5f85e72b27c89ca32", "name": "cuda-command-line-tools", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-command-line-tools-12.4.1-haa95532_1.conda", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "2ebc1638dba25cdd97e7c6acd0f7e5147ad75d18a2454d5b848a2339dc90bdc7", "size": 19913, "subdir": "win-64", "timestamp": 1715724453000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-command-line-tools-12.4.1-haa95532_1.conda", "version": "12.4.1"}