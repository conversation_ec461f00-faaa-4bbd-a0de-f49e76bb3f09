{"build": "hd77b12b_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-nvvm-tools-12.4.131-hd77b12b_0", "files": ["Library/nvvm/bin/cicc.exe", "Library/nvvm/bin/nvvm64_40_0.dll", "Library/nvvm/libdevice/libdevice.10.bc"], "fn": "cuda-nvvm-tools-12.4.131-hd77b12b_0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-nvvm-tools-12.4.131-hd77b12b_0", "type": 1}, "md5": "00b8610066b7a9a676558bfa2cc72825", "name": "cuda-nvvm-tools", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-nvvm-tools-12.4.131-hd77b12b_0.conda", "paths_data": {"paths": [{"_path": "Library/nvvm/bin/cicc.exe", "path_type": "hardlink", "sha256": "a5f70fd7076807724c0065cff4bc2f19730b492a6e4556188c56f77fcd3e9a9e", "sha256_in_prefix": "a5f70fd7076807724c0065cff4bc2f19730b492a6e4556188c56f77fcd3e9a9e", "size_in_bytes": 28270080}, {"_path": "Library/nvvm/bin/nvvm64_40_0.dll", "path_type": "hardlink", "sha256": "6ac7cae59a686a963316140023bf12f68966942e9bfc6dc4381a0483ce17644b", "sha256_in_prefix": "6ac7cae59a686a963316140023bf12f68966942e9bfc6dc4381a0483ce17644b", "size_in_bytes": 18457600}, {"_path": "Library/nvvm/libdevice/libdevice.10.bc", "path_type": "hardlink", "sha256": "81eff5dfc5f9ffcbc7b03b6887f3d087f03b1cd6177fd6302c3405142fcc99a8", "sha256_in_prefix": "81eff5dfc5f9ffcbc7b03b6887f3d087f03b1cd6177fd6302c3405142fcc99a8", "size_in_bytes": 483768}], "paths_version": 1}, "requested_spec": "None", "sha256": "2a180b0514003f97530947448ce7d426ac0913737c6ce43b4be53a0b9c3aaf58", "size": 17750579, "subdir": "win-64", "timestamp": 1714770078000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-nvvm-tools-12.4.131-hd77b12b_0.conda", "version": "12.4.131"}