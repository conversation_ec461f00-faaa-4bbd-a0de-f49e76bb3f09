{"build": "h63175ca_1003", "build_number": 1003, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": [], "depends": ["ucrt >=10.0.20348.0", "vc >=14.2,<15", "vc14_runtime >=14.29.30139"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\graphite2-1.3.13-h63175ca_1003", "files": ["Library/bin/gr2fonttest.exe", "Library/bin/graphite2.dll", "Library/include/graphite2/Font.h", "Library/include/graphite2/Log.h", "Library/include/graphite2/Segment.h", "Library/include/graphite2/Types.h", "Library/lib/graphite2.lib", "Library/lib/pkgconfig/graphite2.pc", "Library/share/graphite2/graphite2-release.cmake", "Library/share/graphite2/graphite2.cmake"], "fn": "graphite2-1.3.13-h63175ca_1003.conda", "license": "LGPL-2.0-or-later", "link": {"source": "D:\\anaconda3\\pkgs\\graphite2-1.3.13-h63175ca_1003", "type": 1}, "md5": "3194499ee7d1a67404a87d0eefdd92c6", "name": "graphite2", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\graphite2-1.3.13-h63175ca_1003.conda", "paths_data": {"paths": [{"_path": "Library/bin/gr2fonttest.exe", "path_type": "hardlink", "sha256": "3c062618901a26b3c302bb1d71a396a4196bb0196ef6cc938d75aea17068884e", "sha256_in_prefix": "3c062618901a26b3c302bb1d71a396a4196bb0196ef6cc938d75aea17068884e", "size_in_bytes": 22528}, {"_path": "Library/bin/graphite2.dll", "path_type": "hardlink", "sha256": "7926a54c380923f4cd79a5411a44c7f98c1de281ab27fdd5add11cf21db6778e", "sha256_in_prefix": "7926a54c380923f4cd79a5411a44c7f98c1de281ab27fdd5add11cf21db6778e", "size_in_bytes": 129536}, {"_path": "Library/include/graphite2/Font.h", "path_type": "hardlink", "sha256": "60bb74b198922c7995c8bf7c3b809117d7b6176a8bc45999d2e1022c96913db4", "sha256_in_prefix": "60bb74b198922c7995c8bf7c3b809117d7b6176a8bc45999d2e1022c96913db4", "size_in_bytes": 18738}, {"_path": "Library/include/graphite2/Log.h", "path_type": "hardlink", "sha256": "db33ed8c1299d74130d3153ece07afe6b7886108f95ce351786bb01720a24ab0", "sha256_in_prefix": "db33ed8c1299d74130d3153ece07afe6b7886108f95ce351786bb01720a24ab0", "size_in_bytes": 2995}, {"_path": "Library/include/graphite2/Segment.h", "path_type": "hardlink", "sha256": "6654971a70a6ed798d25fdfc5d858a957fed29f9eb5d3c90f5cd9b8b65b8cca5", "sha256_in_prefix": "6654971a70a6ed798d25fdfc5d858a957fed29f9eb5d3c90f5cd9b8b65b8cca5", "size_in_bytes": 20303}, {"_path": "Library/include/graphite2/Types.h", "path_type": "hardlink", "sha256": "97dccf9d5420941775e5efc0a86f201a63d0dfdf8a6046096c37ec42ef2d758a", "sha256_in_prefix": "97dccf9d5420941775e5efc0a86f201a63d0dfdf8a6046096c37ec42ef2d758a", "size_in_bytes": 3857}, {"_path": "Library/lib/graphite2.lib", "path_type": "hardlink", "sha256": "45abdacdb851beb75185e8e66246de310d628fa7f7db418cad0c6342f465bc2c", "sha256_in_prefix": "45abdacdb851beb75185e8e66246de310d628fa7f7db418cad0c6342f465bc2c", "size_in_bytes": 16762}, {"_path": "Library/lib/pkgconfig/graphite2.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "D:/bld/graphite2_1711634137655/_h_env", "sha256": "79dcffaebba143ff91a41e49bb2c9af9b0ef6b76c103e03cf2e35159161525b3", "sha256_in_prefix": "728e61f06c196ed068ddbb863d8c218a7d5b315deee33598c6fb7ac1d2b821e1", "size_in_bytes": 466}, {"_path": "Library/share/graphite2/graphite2-release.cmake", "path_type": "hardlink", "sha256": "975141c2bfe2f96515661cc94d4d03a4281965af425939b554cbc40248318146", "sha256_in_prefix": "975141c2bfe2f96515661cc94d4d03a4281965af425939b554cbc40248318146", "size_in_bytes": 913}, {"_path": "Library/share/graphite2/graphite2.cmake", "path_type": "hardlink", "sha256": "12e33ea32624a0f88dbbcfc2d89267a0ec2ee0d961ca6a81a180adaca5465469", "sha256_in_prefix": "12e33ea32624a0f88dbbcfc2d89267a0ec2ee0d961ca6a81a180adaca5465469", "size_in_bytes": 4035}], "paths_version": 1}, "requested_spec": "None", "sha256": "25040a4f371b9b51663f546bac620122c237fa1d5d32968e21b0751af9b7f56f", "size": 95406, "subdir": "win-64", "timestamp": 1711634622000, "url": "https://conda.anaconda.org/conda-forge/win-64/graphite2-1.3.13-h63175ca_1003.conda", "version": "1.3.13"}