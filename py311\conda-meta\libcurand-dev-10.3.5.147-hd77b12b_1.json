{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0", "libcurand ********** hd77b12b_1"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libcurand-dev-**********-hd77b12b_1", "files": ["Library/include/curand.h", "Library/include/curand_discrete.h", "Library/include/curand_discrete2.h", "Library/include/curand_globals.h", "Library/include/curand_kernel.h", "Library/include/curand_lognormal.h", "Library/include/curand_mrg32k3a.h", "Library/include/curand_mtgp32.h", "Library/include/curand_mtgp32_host.h", "Library/include/curand_mtgp32_kernel.h", "Library/include/curand_mtgp32dc_p_11213.h", "Library/include/curand_normal.h", "Library/include/curand_normal_static.h", "Library/include/curand_philox4x32_x.h", "Library/include/curand_poisson.h", "Library/include/curand_precalc.h", "Library/include/curand_uniform.h", "Library/lib/curand.lib"], "fn": "libcurand-dev-**********-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libcurand-dev-**********-hd77b12b_1", "type": 1}, "md5": "bd8c3ffc7c25a35f34e5161084ac7285", "name": "libcurand-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libcurand-dev-**********-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/include/curand.h", "path_type": "hardlink", "sha256": "52096ae2b7b997e2866a4cc2e328fcf626bde038038b997b2ef1cca2a4f7bf63", "sha256_in_prefix": "52096ae2b7b997e2866a4cc2e328fcf626bde038038b997b2ef1cca2a4f7bf63", "size_in_bytes": 45043}, {"_path": "Library/include/curand_discrete.h", "path_type": "hardlink", "sha256": "b5ff00452127fead9867f5e9355510616b98cabed82b088ecf055e2946b839d2", "sha256_in_prefix": "b5ff00452127fead9867f5e9355510616b98cabed82b088ecf055e2946b839d2", "size_in_bytes": 3573}, {"_path": "Library/include/curand_discrete2.h", "path_type": "hardlink", "sha256": "96212a4e22ea9bb81805e13c8c5b70f8a66326f03589784b3fed96602c5e6033", "sha256_in_prefix": "96212a4e22ea9bb81805e13c8c5b70f8a66326f03589784b3fed96602c5e6033", "size_in_bytes": 11136}, {"_path": "Library/include/curand_globals.h", "path_type": "hardlink", "sha256": "36d0388b7336e05928e87590b75ff8954b8c98b1292ae82e89b00d72c2f4a2cd", "sha256_in_prefix": "36d0388b7336e05928e87590b75ff8954b8c98b1292ae82e89b00d72c2f4a2cd", "size_in_bytes": 3810}, {"_path": "Library/include/curand_kernel.h", "path_type": "hardlink", "sha256": "f34e0cca3dfc2a4183d970854b5a4ed4464827033cee227a93198aa9e424c728", "sha256_in_prefix": "f34e0cca3dfc2a4183d970854b5a4ed4464827033cee227a93198aa9e424c728", "size_in_bytes": 54810}, {"_path": "Library/include/curand_lognormal.h", "path_type": "hardlink", "sha256": "0f466c1c313091e692ec4d248f4bc694d4c1de1492ba2c26cb63b33945afe35e", "sha256_in_prefix": "0f466c1c313091e692ec4d248f4bc694d4c1de1492ba2c26cb63b33945afe35e", "size_in_bytes": 28839}, {"_path": "Library/include/curand_mrg32k3a.h", "path_type": "hardlink", "sha256": "3abf04755a64953fd88d0f92839d09238c63da66fe56a5ec597017f6d7854d86", "sha256_in_prefix": "3abf04755a64953fd88d0f92839d09238c63da66fe56a5ec597017f6d7854d86", "size_in_bytes": 174017}, {"_path": "Library/include/curand_mtgp32.h", "path_type": "hardlink", "sha256": "6e25999ffb0f236d28c16c578a529380bf3425e48bcb7cd1b126adbf69396767", "sha256_in_prefix": "6e25999ffb0f236d28c16c578a529380bf3425e48bcb7cd1b126adbf69396767", "size_in_bytes": 8055}, {"_path": "Library/include/curand_mtgp32_host.h", "path_type": "hardlink", "sha256": "2775e273d89f3d50bfc398b8636a8c91e5372e7977ba6a16911d9d8398ac1a0f", "sha256_in_prefix": "2775e273d89f3d50bfc398b8636a8c91e5372e7977ba6a16911d9d8398ac1a0f", "size_in_bytes": 18790}, {"_path": "Library/include/curand_mtgp32_kernel.h", "path_type": "hardlink", "sha256": "81174be52adf2cfc91679279ef9e974a9d3858aa162ab52723b5887bb101383c", "sha256_in_prefix": "81174be52adf2cfc91679279ef9e974a9d3858aa162ab52723b5887bb101383c", "size_in_bytes": 14117}, {"_path": "Library/include/curand_mtgp32dc_p_11213.h", "path_type": "hardlink", "sha256": "0e0046f20aa47cb5be78ab6e0106f4da082b7c6430e093a1624fe8fcf9160513", "sha256_in_prefix": "0e0046f20aa47cb5be78ab6e0106f4da082b7c6430e093a1624fe8fcf9160513", "size_in_bytes": 288599}, {"_path": "Library/include/curand_normal.h", "path_type": "hardlink", "sha256": "96fd3efb1dc6ee653ce0779edf1dd3ba6d000c86d4d7d6609736e6f8be3858ca", "sha256_in_prefix": "96fd3efb1dc6ee653ce0779edf1dd3ba6d000c86d4d7d6609736e6f8be3858ca", "size_in_bytes": 27793}, {"_path": "Library/include/curand_normal_static.h", "path_type": "hardlink", "sha256": "f7f5ea154e68064442acc9885b161e4b19fe34b5b53e65dd4d32197484ac8d7f", "sha256_in_prefix": "f7f5ea154e68064442acc9885b161e4b19fe34b5b53e65dd4d32197484ac8d7f", "size_in_bytes": 4861}, {"_path": "Library/include/curand_philox4x32_x.h", "path_type": "hardlink", "sha256": "84a5dd9ff4fbdb5b782b6b1aecb052dcb258ee4efe23cb250e08aa939eee106d", "sha256_in_prefix": "84a5dd9ff4fbdb5b782b6b1aecb052dcb258ee4efe23cb250e08aa939eee106d", "size_in_bytes": 7361}, {"_path": "Library/include/curand_poisson.h", "path_type": "hardlink", "sha256": "8087c293816811a293ab6830214184513e4693d2c38de75d588404d745c43e39", "sha256_in_prefix": "8087c293816811a293ab6830214184513e4693d2c38de75d588404d745c43e39", "size_in_bytes": 26224}, {"_path": "Library/include/curand_precalc.h", "path_type": "hardlink", "sha256": "ccc3ba083d873a034c544275b1eb769ebd91e7583e8652f7f0252f3892c30cfb", "sha256_in_prefix": "ccc3ba083d873a034c544275b1eb769ebd91e7583e8652f7f0252f3892c30cfb", "size_in_bytes": 1395941}, {"_path": "Library/include/curand_uniform.h", "path_type": "hardlink", "sha256": "34ce6f6c3e4e83d6a7b12926331ec78c9f19ec7cda22d6b638a772123b63a1d5", "sha256_in_prefix": "34ce6f6c3e4e83d6a7b12926331ec78c9f19ec7cda22d6b638a772123b63a1d5", "size_in_bytes": 17970}, {"_path": "Library/lib/curand.lib", "path_type": "hardlink", "sha256": "947527aaeec7364727077681c29abe06bddc043c868d6ac890bbcbb16b913b9d", "sha256_in_prefix": "947527aaeec7364727077681c29abe06bddc043c868d6ac890bbcbb16b913b9d", "size_in_bytes": 8534}], "paths_version": 1}, "requested_spec": "None", "sha256": "917412ac77bf6411fbdd840b3c76089facb1be1344e0d654b765524d4b90ef20", "size": 292062, "subdir": "win-64", "timestamp": 1714678011000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libcurand-dev-**********-hd77b12b_1.conda", "version": "**********"}