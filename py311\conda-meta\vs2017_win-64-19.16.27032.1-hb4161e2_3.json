{"build": "hb4161e2_3", "build_number": 3, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vswhere"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\vs2017_win-64-19.16.27032.1-hb4161e2_3", "files": ["etc/conda/activate.d/vs2017_compiler_vars.bat", "etc/conda/activate.d/vs2017_get_vsinstall_dir.bat"], "fn": "vs2017_win-64-19.16.27032.1-hb4161e2_3.conda", "license": "BSD 3-clause", "link": {"source": "D:\\anaconda3\\pkgs\\vs2017_win-64-19.16.27032.1-hb4161e2_3", "type": 1}, "md5": "300b3e74bccba893cf7109a21d8a3ff2", "name": "vs2017_win-64", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\vs2017_win-64-19.16.27032.1-hb4161e2_3.conda", "paths_data": {"paths": [{"_path": "etc/conda/activate.d/vs2017_compiler_vars.bat", "path_type": "hardlink", "sha256": "4bc3b5e1ff7284cafb4f1128d7850cc99e00f923c1f6fd47e0390d0f9503e8a7", "sha256_in_prefix": "4bc3b5e1ff7284cafb4f1128d7850cc99e00f923c1f6fd47e0390d0f9503e8a7", "size_in_bytes": 2448}, {"_path": "etc/conda/activate.d/vs2017_get_vsinstall_dir.bat", "path_type": "hardlink", "sha256": "4f6d84d8dcf9f00666adc8dc4642237d6cf2e2a8ab4c33daf29817c628e92bc0", "sha256_in_prefix": "4f6d84d8dcf9f00666adc8dc4642237d6cf2e2a8ab4c33daf29817c628e92bc0", "size_in_bytes": 1666}], "paths_version": 1}, "requested_spec": "None", "sha256": "a7bafb4b944ef101f9f404f9998987a3b5f688b242012a7d9fd2f7d63d4a7a32", "size": 202656, "subdir": "win-64", "timestamp": 1593770730000, "track_features": "vc14", "url": "https://repo.anaconda.com/pkgs/main/win-64/vs2017_win-64-19.16.27032.1-hb4161e2_3.conda", "version": "19.16.27032.1"}