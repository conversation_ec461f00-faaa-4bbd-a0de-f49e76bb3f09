{"build": "haa95532_2", "build_number": 2, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["cuda-version >=12.4,<12.5.0a0", "cccl 2.3.2", "cuda-cccl_win-64 12.4.127"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-cccl-12.4.127-haa95532_2", "files": [], "fn": "cuda-cccl-12.4.127-haa95532_2.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-cccl-12.4.127-haa95532_2", "type": 1}, "md5": "bad14ca5e70ba1800ab2fc3aa4026bb5", "name": "cuda-cccl", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-cccl-12.4.127-haa95532_2.conda", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "faef7dd4d9560e7ffc0c654a72755148abd7df76153004cc14c11aa0ee1f5ae6", "size": 21105, "subdir": "win-64", "timestamp": 1714767593000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-cccl-12.4.127-haa95532_2.conda", "version": "12.4.127"}