{"build": "h35fed64_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": ["vc >=14.2"], "depends": ["cuda-cudart-dev", "cuda-version >=12.4,<12.5.0a0", "cuda-cudart >=12.4.127,<13.0a0", "cuda-nvcc-dev_win-64 12.4.131 haa95532_0", "cuda-nvcc-tools 12.4.131 hd77b12b_0", "cuda-nvvm-impl 12.4.131 hd77b12b_0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-nvcc-impl-12.4.131-h35fed64_0", "files": [], "fn": "cuda-nvcc-impl-12.4.131-h35fed64_0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-nvcc-impl-12.4.131-h35fed64_0", "type": 1}, "md5": "5cfbd053d1b432e4117704fe3dfb1d19", "name": "cuda-nvcc-impl", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-nvcc-impl-12.4.131-h35fed64_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "ac35adff4832b3c8da877c3186dc3fdc5c7cacc209ecfec76c5b3aeac71aa69c", "size": 24111, "subdir": "win-64", "timestamp": 1714770185000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-nvcc-impl-12.4.131-h35fed64_0.conda", "version": "12.4.131"}