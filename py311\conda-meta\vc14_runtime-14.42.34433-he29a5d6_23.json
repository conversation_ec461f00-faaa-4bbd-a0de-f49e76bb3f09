{"build": "he29a5d6_23", "build_number": 23, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": ["vs2015_runtime 14.42.34433.* *_23"], "depends": ["ucrt >=10.0.20348.0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\vc14_runtime-14.42.34433-he29a5d6_23", "files": ["Library/bin/concrt140.dll", "Library/bin/msvcp140.dll", "Library/bin/msvcp140_1.dll", "Library/bin/msvcp140_2.dll", "Library/bin/msvcp140_atomic_wait.dll", "Library/bin/msvcp140_codecvt_ids.dll", "Library/bin/vcamp140.dll", "Library/bin/vccorlib140.dll", "Library/bin/vcomp140.dll", "Library/bin/vcruntime140.dll", "Library/bin/vcruntime140_1.dll", "Library/bin/vcruntime140_threads.dll", "concrt140.dll", "msvcp140.dll", "msvcp140_1.dll", "msvcp140_2.dll", "msvcp140_atomic_wait.dll", "msvcp140_codecvt_ids.dll", "vcamp140.dll", "vccorlib140.dll", "vcomp140.dll", "vcruntime140.dll", "vcruntime140_1.dll", "vcruntime140_threads.dll"], "fn": "vc14_runtime-14.42.34433-he29a5d6_23.conda", "license": "LicenseRef-MicrosoftVisualCpp2015-2022Runtime", "link": {"source": "D:\\anaconda3\\pkgs\\vc14_runtime-14.42.34433-he29a5d6_23", "type": 1}, "md5": "32b37d0cfa80da34548501cdc913a832", "name": "vc14_runtime", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\vc14_runtime-14.42.34433-he29a5d6_23.conda", "paths_data": {"paths": [{"_path": "Library/bin/concrt140.dll", "no_link": true, "path_type": "hardlink", "sha256": "e2e4609c569c69f7b1686f6d0e81ce62187ac5df05e0247954500053b3c3de3f", "sha256_in_prefix": "e2e4609c569c69f7b1686f6d0e81ce62187ac5df05e0247954500053b3c3de3f", "size_in_bytes": 322640}, {"_path": "Library/bin/msvcp140.dll", "no_link": true, "path_type": "hardlink", "sha256": "9057d39b36b6c7d054865ee2bf9cde7a490fe3b01ec4e82514687e24f576269f", "sha256_in_prefix": "9057d39b36b6c7d054865ee2bf9cde7a490fe3b01ec4e82514687e24f576269f", "size_in_bytes": 575592}, {"_path": "Library/bin/msvcp140_1.dll", "no_link": true, "path_type": "hardlink", "sha256": "a65249861238e1c18b84ae5d112617c438d83a76b67eddc170ad82dbc2338665", "sha256_in_prefix": "a65249861238e1c18b84ae5d112617c438d83a76b67eddc170ad82dbc2338665", "size_in_bytes": 35944}, {"_path": "Library/bin/msvcp140_2.dll", "no_link": true, "path_type": "hardlink", "sha256": "89e2e9a163165e20c540f9adea081e927ddfe4a556547b0f45f11586d4cce165", "sha256_in_prefix": "89e2e9a163165e20c540f9adea081e927ddfe4a556547b0f45f11586d4cce165", "size_in_bytes": 267880}, {"_path": "Library/bin/msvcp140_atomic_wait.dll", "no_link": true, "path_type": "hardlink", "sha256": "fbf41e4b53f51bbf73fee37b6120103fea6b7d5ae29916f8ef50c50cfdedeead", "sha256_in_prefix": "fbf41e4b53f51bbf73fee37b6120103fea6b7d5ae29916f8ef50c50cfdedeead", "size_in_bytes": 50256}, {"_path": "Library/bin/msvcp140_codecvt_ids.dll", "no_link": true, "path_type": "hardlink", "sha256": "0e1d3d76e899a89fb3893fb13abae232ff62ad4f573214dd2f02b8398166bcc6", "sha256_in_prefix": "0e1d3d76e899a89fb3893fb13abae232ff62ad4f573214dd2f02b8398166bcc6", "size_in_bytes": 31824}, {"_path": "Library/bin/vcamp140.dll", "no_link": true, "path_type": "hardlink", "sha256": "192e80290753e5a79c56367700a09181321c0984515f4c35e7ea8a0f245877bf", "sha256_in_prefix": "192e80290753e5a79c56367700a09181321c0984515f4c35e7ea8a0f245877bf", "size_in_bytes": 408656}, {"_path": "Library/bin/vccorlib140.dll", "no_link": true, "path_type": "hardlink", "sha256": "8903b5d88968791d2a93648a54a1ac3d1c708c579a72311ffe194f6d66903043", "sha256_in_prefix": "8903b5d88968791d2a93648a54a1ac3d1c708c579a72311ffe194f6d66903043", "size_in_bytes": 351824}, {"_path": "Library/bin/vcomp140.dll", "no_link": true, "path_type": "hardlink", "sha256": "036b9b3f7ece8dfd48aeccd77113721c5305043aaa9c64d1e72812252727aa7c", "sha256_in_prefix": "036b9b3f7ece8dfd48aeccd77113721c5305043aaa9c64d1e72812252727aa7c", "size_in_bytes": 192104}, {"_path": "Library/bin/vcruntime140.dll", "no_link": true, "path_type": "hardlink", "sha256": "da72e6677bd1bcd01c453c1998aaa19aeaf6659f4774cf6848409da8232a95b2", "sha256_in_prefix": "da72e6677bd1bcd01c453c1998aaa19aeaf6659f4774cf6848409da8232a95b2", "size_in_bytes": 120432}, {"_path": "Library/bin/vcruntime140_1.dll", "no_link": true, "path_type": "hardlink", "sha256": "26e470b29bed3d873e0c328186e53f95e9edbfe0b0fd0cda44743a0b1a04a828", "sha256_in_prefix": "26e470b29bed3d873e0c328186e53f95e9edbfe0b0fd0cda44743a0b1a04a828", "size_in_bytes": 49744}, {"_path": "Library/bin/vcruntime140_threads.dll", "no_link": true, "path_type": "hardlink", "sha256": "326110c8c5cac836cfed1643304cb6bdc4a8737a7a535d6b1eff4d63878aef9d", "sha256_in_prefix": "326110c8c5cac836cfed1643304cb6bdc4a8737a7a535d6b1eff4d63878aef9d", "size_in_bytes": 38504}, {"_path": "concrt140.dll", "path_type": "hardlink", "sha256": "e2e4609c569c69f7b1686f6d0e81ce62187ac5df05e0247954500053b3c3de3f", "sha256_in_prefix": "e2e4609c569c69f7b1686f6d0e81ce62187ac5df05e0247954500053b3c3de3f", "size_in_bytes": 322640}, {"_path": "msvcp140.dll", "path_type": "hardlink", "sha256": "9057d39b36b6c7d054865ee2bf9cde7a490fe3b01ec4e82514687e24f576269f", "sha256_in_prefix": "9057d39b36b6c7d054865ee2bf9cde7a490fe3b01ec4e82514687e24f576269f", "size_in_bytes": 575592}, {"_path": "msvcp140_1.dll", "path_type": "hardlink", "sha256": "a65249861238e1c18b84ae5d112617c438d83a76b67eddc170ad82dbc2338665", "sha256_in_prefix": "a65249861238e1c18b84ae5d112617c438d83a76b67eddc170ad82dbc2338665", "size_in_bytes": 35944}, {"_path": "msvcp140_2.dll", "path_type": "hardlink", "sha256": "89e2e9a163165e20c540f9adea081e927ddfe4a556547b0f45f11586d4cce165", "sha256_in_prefix": "89e2e9a163165e20c540f9adea081e927ddfe4a556547b0f45f11586d4cce165", "size_in_bytes": 267880}, {"_path": "msvcp140_atomic_wait.dll", "path_type": "hardlink", "sha256": "fbf41e4b53f51bbf73fee37b6120103fea6b7d5ae29916f8ef50c50cfdedeead", "sha256_in_prefix": "fbf41e4b53f51bbf73fee37b6120103fea6b7d5ae29916f8ef50c50cfdedeead", "size_in_bytes": 50256}, {"_path": "msvcp140_codecvt_ids.dll", "path_type": "hardlink", "sha256": "0e1d3d76e899a89fb3893fb13abae232ff62ad4f573214dd2f02b8398166bcc6", "sha256_in_prefix": "0e1d3d76e899a89fb3893fb13abae232ff62ad4f573214dd2f02b8398166bcc6", "size_in_bytes": 31824}, {"_path": "vcamp140.dll", "path_type": "hardlink", "sha256": "192e80290753e5a79c56367700a09181321c0984515f4c35e7ea8a0f245877bf", "sha256_in_prefix": "192e80290753e5a79c56367700a09181321c0984515f4c35e7ea8a0f245877bf", "size_in_bytes": 408656}, {"_path": "vccorlib140.dll", "path_type": "hardlink", "sha256": "8903b5d88968791d2a93648a54a1ac3d1c708c579a72311ffe194f6d66903043", "sha256_in_prefix": "8903b5d88968791d2a93648a54a1ac3d1c708c579a72311ffe194f6d66903043", "size_in_bytes": 351824}, {"_path": "vcomp140.dll", "path_type": "hardlink", "sha256": "036b9b3f7ece8dfd48aeccd77113721c5305043aaa9c64d1e72812252727aa7c", "sha256_in_prefix": "036b9b3f7ece8dfd48aeccd77113721c5305043aaa9c64d1e72812252727aa7c", "size_in_bytes": 192104}, {"_path": "vcruntime140.dll", "path_type": "hardlink", "sha256": "da72e6677bd1bcd01c453c1998aaa19aeaf6659f4774cf6848409da8232a95b2", "sha256_in_prefix": "da72e6677bd1bcd01c453c1998aaa19aeaf6659f4774cf6848409da8232a95b2", "size_in_bytes": 120432}, {"_path": "vcruntime140_1.dll", "path_type": "hardlink", "sha256": "26e470b29bed3d873e0c328186e53f95e9edbfe0b0fd0cda44743a0b1a04a828", "sha256_in_prefix": "26e470b29bed3d873e0c328186e53f95e9edbfe0b0fd0cda44743a0b1a04a828", "size_in_bytes": 49744}, {"_path": "vcruntime140_threads.dll", "path_type": "hardlink", "sha256": "326110c8c5cac836cfed1643304cb6bdc4a8737a7a535d6b1eff4d63878aef9d", "sha256_in_prefix": "326110c8c5cac836cfed1643304cb6bdc4a8737a7a535d6b1eff4d63878aef9d", "size_in_bytes": 38504}], "paths_version": 1}, "requested_spec": "None", "sha256": "c483b090c4251a260aba6ff3e83a307bcfb5fb24ad7ced872ab5d02971bd3a49", "size": 754247, "subdir": "win-64", "timestamp": 1731710681000, "url": "https://conda.anaconda.org/conda-forge/win-64/vc14_runtime-14.42.34433-he29a5d6_23.conda", "version": "14.42.34433"}