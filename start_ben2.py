#!/usr/bin/env python3
"""
BEN2启动脚本 - 自动处理依赖和启动应用
"""

import subprocess
import sys
import os

def check_dependencies():
    """检查必需的依赖是否已安装"""
    required_modules = [
        'spaces',
        'loadimg', 
        'gradio',
        'torch',
        'einops',
        'timm'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} 已安装")
        except ImportError:
            print(f"❌ {module} 未安装")
            missing_modules.append(module)
    
    return missing_modules

def install_missing_dependencies(missing_modules):
    """安装缺失的依赖"""
    if not missing_modules:
        return True
    
    print(f"\n发现缺失的模块：{', '.join(missing_modules)}")
    response = input("是否要自动安装缺失的依赖？(y/n): ").lower().strip()
    
    if response in ['y', 'yes', '是', '确定']:
        try:
            print("正在安装缺失的依赖...")
            subprocess.check_call([sys.executable, "-m", "pip", "install"] + missing_modules)
            print("✅ 依赖安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装失败：{e}")
            return False
    else:
        print("跳过依赖安装")
        return False

def start_application():
    """启动BEN2应用"""
    print("\n正在启动BEN2应用...")
    try:
        # 检查app.py是否存在
        if not os.path.exists("app.py"):
            print("❌ 找不到app.py文件")
            return False
        
        # 启动应用
        subprocess.run([sys.executable, "app.py"])
        return True
        
    except KeyboardInterrupt:
        print("\n用户中断，正在退出...")
        return True
    except Exception as e:
        print(f"❌ 启动失败：{e}")
        return False

def main():
    print("BEN2 背景移除工具启动器")
    print("=" * 40)
    
    # 检查依赖
    print("正在检查依赖...")
    missing_modules = check_dependencies()
    
    # 安装缺失的依赖
    if missing_modules:
        if not install_missing_dependencies(missing_modules):
            print("\n❌ 无法安装所有依赖，请手动安装：")
            print(f"pip install {' '.join(missing_modules)}")
            return
    
    # 启动应用
    print("\n所有依赖已就绪！")
    if start_application():
        print("应用已退出")
    else:
        print("应用启动失败")

if __name__ == "__main__":
    main()
