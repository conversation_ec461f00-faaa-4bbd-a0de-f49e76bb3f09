{"build": "hd77b12b_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0", "cuda-crt-tools 12.4.131 haa95532_0", "cuda-nvvm-tools 12.4.131 hd77b12b_0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-nvcc-tools-12.4.131-hd77b12b_0", "files": [], "fn": "cuda-nvcc-tools-12.4.131-hd77b12b_0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-nvcc-tools-12.4.131-hd77b12b_0", "type": 1}, "md5": "e7ea0ff995e4ba9ba2525787ade358dd", "name": "cuda-nvcc-tools", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-nvcc-tools-12.4.131-hd77b12b_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "403185c99f9d7bb1b73ec8cd285ff5ae4fb39d3cf9f17975466f3bc9717d06ee", "size": 23840, "subdir": "win-64", "timestamp": 1714770096000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-nvcc-tools-12.4.131-hd77b12b_0.conda", "version": "12.4.131"}