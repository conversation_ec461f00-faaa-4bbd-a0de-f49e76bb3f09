{"build": "py311haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["python >=3.11,<3.12.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\setuptools-75.1.0-py311haa95532_0", "files": ["Lib/site-packages/_distutils_hack/__init__.py", "Lib/site-packages/_distutils_hack/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/_distutils_hack/__pycache__/override.cpython-311.pyc", "Lib/site-packages/_distutils_hack/override.py", "Lib/site-packages/distutils-precedence.pth", "Lib/site-packages/pkg_resources/__init__.py", "Lib/site-packages/pkg_resources/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/pkg_resources/api_tests.txt", "Lib/site-packages/pkg_resources/py.typed", "Lib/site-packages/pkg_resources/tests/__init__.py", "Lib/site-packages/pkg_resources/tests/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_find_distributions.cpython-311.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_integration_zope_interface.cpython-311.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_markers.cpython-311.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_pkg_resources.cpython-311.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_resources.cpython-311.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_working_set.cpython-311.pyc", "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/__pycache__/setup.cpython-311.pyc", "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.cfg", "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py", "Lib/site-packages/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe", "Lib/site-packages/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg", "Lib/site-packages/pkg_resources/tests/test_find_distributions.py", "Lib/site-packages/pkg_resources/tests/test_integration_zope_interface.py", "Lib/site-packages/pkg_resources/tests/test_markers.py", "Lib/site-packages/pkg_resources/tests/test_pkg_resources.py", "Lib/site-packages/pkg_resources/tests/test_resources.py", "Lib/site-packages/pkg_resources/tests/test_working_set.py", "Lib/site-packages/setuptools-75.1.0-py3.11.egg-info/PKG-INFO", "Lib/site-packages/setuptools-75.1.0-py3.11.egg-info/SOURCES.txt", "Lib/site-packages/setuptools-75.1.0-py3.11.egg-info/dependency_links.txt", "Lib/site-packages/setuptools-75.1.0-py3.11.egg-info/entry_points.txt", "Lib/site-packages/setuptools-75.1.0-py3.11.egg-info/requires.txt", "Lib/site-packages/setuptools-75.1.0-py3.11.egg-info/top_level.txt", "Lib/site-packages/setuptools/__init__.py", "Lib/site-packages/setuptools/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/_core_metadata.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/_entry_points.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/_imp.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/_importlib.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/_itertools.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/_normalization.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/_path.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/_reqs.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/archive_util.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/build_meta.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/depends.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/discovery.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/dist.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/errors.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/extension.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/glob.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/installer.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/launch.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/logging.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/modified.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/monkey.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/msvc.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/namespaces.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/package_index.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/sandbox.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/unicode_utils.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/version.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/warnings.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/wheel.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/windows_support.cpython-311.pyc", "Lib/site-packages/setuptools/_core_metadata.py", "Lib/site-packages/setuptools/_distutils/__init__.py", "Lib/site-packages/setuptools/_distutils/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_log.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_macos_compat.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_modified.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_msvccompiler.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/archive_util.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/ccompiler.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/cmd.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/core.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/cygwinccompiler.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/debug.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/dep_util.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/dir_util.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/dist.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/errors.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/extension.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/fancy_getopt.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/file_util.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/filelist.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/log.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/spawn.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/sysconfig.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/text_file.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/unixccompiler.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/util.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/version.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/versionpredicate.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/zosccompiler.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/_log.py", "Lib/site-packages/setuptools/_distutils/_macos_compat.py", "Lib/site-packages/setuptools/_distutils/_modified.py", "Lib/site-packages/setuptools/_distutils/_msvccompiler.py", "Lib/site-packages/setuptools/_distutils/archive_util.py", "Lib/site-packages/setuptools/_distutils/ccompiler.py", "Lib/site-packages/setuptools/_distutils/cmd.py", "Lib/site-packages/setuptools/_distutils/command/__init__.py", "Lib/site-packages/setuptools/_distutils/command/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/_framework_compat.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_dumb.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_rpm.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_clib.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_ext.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_py.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_scripts.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/check.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/clean.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/config.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_data.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_egg_info.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_headers.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_lib.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_scripts.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/sdist.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/_framework_compat.py", "Lib/site-packages/setuptools/_distutils/command/bdist.py", "Lib/site-packages/setuptools/_distutils/command/bdist_dumb.py", "Lib/site-packages/setuptools/_distutils/command/bdist_rpm.py", "Lib/site-packages/setuptools/_distutils/command/build.py", "Lib/site-packages/setuptools/_distutils/command/build_clib.py", "Lib/site-packages/setuptools/_distutils/command/build_ext.py", "Lib/site-packages/setuptools/_distutils/command/build_py.py", "Lib/site-packages/setuptools/_distutils/command/build_scripts.py", "Lib/site-packages/setuptools/_distutils/command/check.py", "Lib/site-packages/setuptools/_distutils/command/clean.py", "Lib/site-packages/setuptools/_distutils/command/config.py", "Lib/site-packages/setuptools/_distutils/command/install.py", "Lib/site-packages/setuptools/_distutils/command/install_data.py", "Lib/site-packages/setuptools/_distutils/command/install_egg_info.py", "Lib/site-packages/setuptools/_distutils/command/install_headers.py", "Lib/site-packages/setuptools/_distutils/command/install_lib.py", "Lib/site-packages/setuptools/_distutils/command/install_scripts.py", "Lib/site-packages/setuptools/_distutils/command/sdist.py", "Lib/site-packages/setuptools/_distutils/compat/__init__.py", "Lib/site-packages/setuptools/_distutils/compat/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/compat/__pycache__/py38.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/compat/__pycache__/py39.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/compat/py38.py", "Lib/site-packages/setuptools/_distutils/compat/py39.py", "Lib/site-packages/setuptools/_distutils/core.py", "Lib/site-packages/setuptools/_distutils/cygwinccompiler.py", "Lib/site-packages/setuptools/_distutils/debug.py", "Lib/site-packages/setuptools/_distutils/dep_util.py", "Lib/site-packages/setuptools/_distutils/dir_util.py", "Lib/site-packages/setuptools/_distutils/dist.py", "Lib/site-packages/setuptools/_distutils/errors.py", "Lib/site-packages/setuptools/_distutils/extension.py", "Lib/site-packages/setuptools/_distutils/fancy_getopt.py", "Lib/site-packages/setuptools/_distutils/file_util.py", "Lib/site-packages/setuptools/_distutils/filelist.py", "Lib/site-packages/setuptools/_distutils/log.py", "Lib/site-packages/setuptools/_distutils/spawn.py", "Lib/site-packages/setuptools/_distutils/sysconfig.py", "Lib/site-packages/setuptools/_distutils/tests/__init__.py", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/support.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_archive_util.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_dumb.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_rpm.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_clib.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_ext.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_py.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_scripts.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_ccompiler.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_check.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_clean.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_cmd.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_config_cmd.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_core.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_cygwinccompiler.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dir_util.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dist.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_extension.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_file_util.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_filelist.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_data.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_headers.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_lib.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_scripts.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_log.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_mingwccompiler.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_modified.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_msvccompiler.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sdist.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_spawn.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sysconfig.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_text_file.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_unixccompiler.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_util.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_version.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_versionpredicate.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/unix_compat.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/compat/__init__.py", "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/py38.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/compat/py38.py", "Lib/site-packages/setuptools/_distutils/tests/support.py", "Lib/site-packages/setuptools/_distutils/tests/test_archive_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_bdist.py", "Lib/site-packages/setuptools/_distutils/tests/test_bdist_dumb.py", "Lib/site-packages/setuptools/_distutils/tests/test_bdist_rpm.py", "Lib/site-packages/setuptools/_distutils/tests/test_build.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_clib.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_ext.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_py.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_scripts.py", "Lib/site-packages/setuptools/_distutils/tests/test_ccompiler.py", "Lib/site-packages/setuptools/_distutils/tests/test_check.py", "Lib/site-packages/setuptools/_distutils/tests/test_clean.py", "Lib/site-packages/setuptools/_distutils/tests/test_cmd.py", "Lib/site-packages/setuptools/_distutils/tests/test_config_cmd.py", "Lib/site-packages/setuptools/_distutils/tests/test_core.py", "Lib/site-packages/setuptools/_distutils/tests/test_cygwinccompiler.py", "Lib/site-packages/setuptools/_distutils/tests/test_dir_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_dist.py", "Lib/site-packages/setuptools/_distutils/tests/test_extension.py", "Lib/site-packages/setuptools/_distutils/tests/test_file_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_filelist.py", "Lib/site-packages/setuptools/_distutils/tests/test_install.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_data.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_headers.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_lib.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_scripts.py", "Lib/site-packages/setuptools/_distutils/tests/test_log.py", "Lib/site-packages/setuptools/_distutils/tests/test_mingwccompiler.py", "Lib/site-packages/setuptools/_distutils/tests/test_modified.py", "Lib/site-packages/setuptools/_distutils/tests/test_msvccompiler.py", "Lib/site-packages/setuptools/_distutils/tests/test_sdist.py", "Lib/site-packages/setuptools/_distutils/tests/test_spawn.py", "Lib/site-packages/setuptools/_distutils/tests/test_sysconfig.py", "Lib/site-packages/setuptools/_distutils/tests/test_text_file.py", "Lib/site-packages/setuptools/_distutils/tests/test_unixccompiler.py", "Lib/site-packages/setuptools/_distutils/tests/test_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_version.py", "Lib/site-packages/setuptools/_distutils/tests/test_versionpredicate.py", "Lib/site-packages/setuptools/_distutils/tests/unix_compat.py", "Lib/site-packages/setuptools/_distutils/text_file.py", "Lib/site-packages/setuptools/_distutils/unixccompiler.py", "Lib/site-packages/setuptools/_distutils/util.py", "Lib/site-packages/setuptools/_distutils/version.py", "Lib/site-packages/setuptools/_distutils/versionpredicate.py", "Lib/site-packages/setuptools/_distutils/zosccompiler.py", "Lib/site-packages/setuptools/_entry_points.py", "Lib/site-packages/setuptools/_imp.py", "Lib/site-packages/setuptools/_importlib.py", "Lib/site-packages/setuptools/_itertools.py", "Lib/site-packages/setuptools/_normalization.py", "Lib/site-packages/setuptools/_path.py", "Lib/site-packages/setuptools/_reqs.py", "Lib/site-packages/setuptools/_vendor/__pycache__/typing_extensions.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/autocommand/__init__.py", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoasync.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autocommand.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/automain.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoparse.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/errors.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/autoasync.py", "Lib/site-packages/setuptools/_vendor/autocommand/autocommand.py", "Lib/site-packages/setuptools/_vendor/autocommand/automain.py", "Lib/site-packages/setuptools/_vendor/autocommand/autoparse.py", "Lib/site-packages/setuptools/_vendor/autocommand/errors.py", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/backports/__init__.py", "Lib/site-packages/setuptools/_vendor/backports/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__init__.py", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__main__.py", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__main__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/py38.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_adapters.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_collections.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_compat.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_functools.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_itertools.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_meta.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_text.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/diagnose.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_collections.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_compat.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_functools.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_meta.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_text.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py311.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py39.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/diagnose.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/py.typed", "Lib/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/importlib_resources/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/__pycache__/_adapters.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/__pycache__/_common.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/__pycache__/_itertools.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/__pycache__/abc.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/__pycache__/functional.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/__pycache__/readers.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/__pycache__/simple.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/_adapters.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/_common.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/_itertools.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/abc.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/compat/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/compat/__pycache__/py38.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/compat/__pycache__/py39.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/compat/py38.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/compat/py39.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/functional.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/future/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/future/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/future/__pycache__/adapters.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/future/adapters.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/py.typed", "Lib/site-packages/setuptools/_vendor/importlib_resources/readers.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/simple.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/_path.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_compatibilty_files.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_contents.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_custom.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_files.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_functional.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_open.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_path.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_read.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_reader.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_resource.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/util.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/zip.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/_path.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/compat/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/compat/__pycache__/py312.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/compat/__pycache__/py39.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/compat/py312.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/compat/py39.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data01/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data01/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data01/binary.file", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data01/subdirectory/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data01/subdirectory/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data01/subdirectory/binary.file", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data01/utf-16.file", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data01/utf-8.file", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data02/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data02/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data02/one/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data02/one/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data02/one/resource1.txt", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data02/subdirectory/subsubdir/resource.txt", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data02/two/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data02/two/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data02/two/resource2.txt", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/namespacedata01/binary.file", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/namespacedata01/subdirectory/binary.file", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/namespacedata01/utf-16.file", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/namespacedata01/utf-8.file", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_compatibilty_files.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_contents.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_custom.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_files.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_functional.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_open.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_path.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_read.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_reader.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_resource.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/util.py", "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/zip.py", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/inflect/__init__.py", "Lib/site-packages/setuptools/_vendor/inflect/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/inflect/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/py38.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/inflect/compat/py38.py", "Lib/site-packages/setuptools/_vendor/inflect/py.typed", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco/__pycache__/context.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/collections/__init__.py", "Lib/site-packages/setuptools/_vendor/jaraco/collections/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/collections/py.typed", "Lib/site-packages/setuptools/_vendor/jaraco/context.py", "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.py", "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.pyi", "Lib/site-packages/setuptools/_vendor/jaraco/functools/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/functools/py.typed", "Lib/site-packages/setuptools/_vendor/jaraco/text/Lorem ipsum.txt", "Lib/site-packages/setuptools/_vendor/jaraco/text/__init__.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/layouts.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/show-newlines.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/strip-prefix.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-dvorak.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-qwerty.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/layouts.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/show-newlines.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.py", "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.pyi", "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/more.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/recipes.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/more_itertools/more.py", "Lib/site-packages/setuptools/_vendor/more_itertools/more.pyi", "Lib/site-packages/setuptools/_vendor/more_itertools/py.typed", "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.py", "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.pyi", "Lib/site-packages/setuptools/_vendor/packaging-24.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/packaging-24.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/packaging-24.1.dist-info/LICENSE.APACHE", "Lib/site-packages/setuptools/_vendor/packaging-24.1.dist-info/LICENSE.BSD", "Lib/site-packages/setuptools/_vendor/packaging-24.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/packaging-24.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/packaging-24.1.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/packaging-24.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/packaging/__init__.py", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_elffile.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_manylinux.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_musllinux.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_parser.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_structures.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_tokenizer.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/markers.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/metadata.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/requirements.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/specifiers.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/tags.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/utils.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/version.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/_elffile.py", "Lib/site-packages/setuptools/_vendor/packaging/_manylinux.py", "Lib/site-packages/setuptools/_vendor/packaging/_musllinux.py", "Lib/site-packages/setuptools/_vendor/packaging/_parser.py", "Lib/site-packages/setuptools/_vendor/packaging/_structures.py", "Lib/site-packages/setuptools/_vendor/packaging/_tokenizer.py", "Lib/site-packages/setuptools/_vendor/packaging/markers.py", "Lib/site-packages/setuptools/_vendor/packaging/metadata.py", "Lib/site-packages/setuptools/_vendor/packaging/py.typed", "Lib/site-packages/setuptools/_vendor/packaging/requirements.py", "Lib/site-packages/setuptools/_vendor/packaging/specifiers.py", "Lib/site-packages/setuptools/_vendor/packaging/tags.py", "Lib/site-packages/setuptools/_vendor/packaging/utils.py", "Lib/site-packages/setuptools/_vendor/packaging/version.py", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE", "Lib/site-packages/setuptools/_vendor/platformdirs/__init__.py", "Lib/site-packages/setuptools/_vendor/platformdirs/__main__.py", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__main__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/android.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/api.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/macos.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/unix.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/version.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/windows.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/android.py", "Lib/site-packages/setuptools/_vendor/platformdirs/api.py", "Lib/site-packages/setuptools/_vendor/platformdirs/macos.py", "Lib/site-packages/setuptools/_vendor/platformdirs/py.typed", "Lib/site-packages/setuptools/_vendor/platformdirs/unix.py", "Lib/site-packages/setuptools/_vendor/platformdirs/version.py", "Lib/site-packages/setuptools/_vendor/platformdirs/windows.py", "Lib/site-packages/setuptools/_vendor/ruff.toml", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/tomli/__init__.py", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_re.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_types.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/tomli/_parser.py", "Lib/site-packages/setuptools/_vendor/tomli/_re.py", "Lib/site-packages/setuptools/_vendor/tomli/_types.py", "Lib/site-packages/setuptools/_vendor/tomli/py.typed", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/typeguard/__init__.py", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_checkers.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_config.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_decorators.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_exceptions.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_functions.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_importhook.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_memo.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_pytest_plugin.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_suppression.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_transformer.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_union_transformer.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_utils.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/_checkers.py", "Lib/site-packages/setuptools/_vendor/typeguard/_config.py", "Lib/site-packages/setuptools/_vendor/typeguard/_decorators.py", "Lib/site-packages/setuptools/_vendor/typeguard/_exceptions.py", "Lib/site-packages/setuptools/_vendor/typeguard/_functions.py", "Lib/site-packages/setuptools/_vendor/typeguard/_importhook.py", "Lib/site-packages/setuptools/_vendor/typeguard/_memo.py", "Lib/site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py", "Lib/site-packages/setuptools/_vendor/typeguard/_suppression.py", "Lib/site-packages/setuptools/_vendor/typeguard/_transformer.py", "Lib/site-packages/setuptools/_vendor/typeguard/_union_transformer.py", "Lib/site-packages/setuptools/_vendor/typeguard/_utils.py", "Lib/site-packages/setuptools/_vendor/typeguard/py.typed", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/typing_extensions.py", "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/LICENSE.txt", "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/entry_points.txt", "Lib/site-packages/setuptools/_vendor/wheel/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/__main__.py", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__main__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_setuptools_logging.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/bdist_wheel.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/macosx_libfile.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/metadata.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/util.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/wheelfile.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/_setuptools_logging.py", "Lib/site-packages/setuptools/_vendor/wheel/bdist_wheel.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/convert.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/pack.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/tags.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/unpack.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/convert.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/pack.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/tags.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/unpack.py", "Lib/site-packages/setuptools/_vendor/wheel/macosx_libfile.py", "Lib/site-packages/setuptools/_vendor/wheel/metadata.py", "Lib/site-packages/setuptools/_vendor/wheel/util.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_elffile.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_manylinux.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_musllinux.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_parser.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_structures.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/markers.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/requirements.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/specifiers.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/tags.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/utils.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/version.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/vendor.txt", "Lib/site-packages/setuptools/_vendor/wheel/wheelfile.py", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/zipp/__init__.py", "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/glob.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/zipp/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/py310.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/zipp/compat/py310.py", "Lib/site-packages/setuptools/_vendor/zipp/glob.py", "Lib/site-packages/setuptools/archive_util.py", "Lib/site-packages/setuptools/build_meta.py", "Lib/site-packages/setuptools/cli-32.exe", "Lib/site-packages/setuptools/cli-64.exe", "Lib/site-packages/setuptools/cli-arm64.exe", "Lib/site-packages/setuptools/cli.exe", "Lib/site-packages/setuptools/command/__init__.py", "Lib/site-packages/setuptools/command/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/_requirestxt.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/alias.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/bdist_egg.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/bdist_rpm.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/bdist_wheel.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/build.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/build_clib.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/build_ext.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/build_py.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/develop.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/dist_info.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/easy_install.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/editable_wheel.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/egg_info.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/install.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/install_lib.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/install_scripts.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/rotate.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/saveopts.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/sdist.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/setopt.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/test.cpython-311.pyc", "Lib/site-packages/setuptools/command/_requirestxt.py", "Lib/site-packages/setuptools/command/alias.py", "Lib/site-packages/setuptools/command/bdist_egg.py", "Lib/site-packages/setuptools/command/bdist_rpm.py", "Lib/site-packages/setuptools/command/bdist_wheel.py", "Lib/site-packages/setuptools/command/build.py", "Lib/site-packages/setuptools/command/build_clib.py", "Lib/site-packages/setuptools/command/build_ext.py", "Lib/site-packages/setuptools/command/build_py.py", "Lib/site-packages/setuptools/command/develop.py", "Lib/site-packages/setuptools/command/dist_info.py", "Lib/site-packages/setuptools/command/easy_install.py", "Lib/site-packages/setuptools/command/editable_wheel.py", "Lib/site-packages/setuptools/command/egg_info.py", "Lib/site-packages/setuptools/command/install.py", "Lib/site-packages/setuptools/command/install_egg_info.py", "Lib/site-packages/setuptools/command/install_lib.py", "Lib/site-packages/setuptools/command/install_scripts.py", "Lib/site-packages/setuptools/command/launcher manifest.xml", "Lib/site-packages/setuptools/command/rotate.py", "Lib/site-packages/setuptools/command/saveopts.py", "Lib/site-packages/setuptools/command/sdist.py", "Lib/site-packages/setuptools/command/setopt.py", "Lib/site-packages/setuptools/command/test.py", "Lib/site-packages/setuptools/compat/__init__.py", "Lib/site-packages/setuptools/compat/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py310.cpython-311.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py311.cpython-311.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py312.cpython-311.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py39.cpython-311.pyc", "Lib/site-packages/setuptools/compat/py310.py", "Lib/site-packages/setuptools/compat/py311.py", "Lib/site-packages/setuptools/compat/py312.py", "Lib/site-packages/setuptools/compat/py39.py", "Lib/site-packages/setuptools/config/NOTICE", "Lib/site-packages/setuptools/config/__init__.py", "Lib/site-packages/setuptools/config/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/config/__pycache__/_apply_pyprojecttoml.cpython-311.pyc", "Lib/site-packages/setuptools/config/__pycache__/expand.cpython-311.pyc", "Lib/site-packages/setuptools/config/__pycache__/pyprojecttoml.cpython-311.pyc", "Lib/site-packages/setuptools/config/__pycache__/setupcfg.cpython-311.pyc", "Lib/site-packages/setuptools/config/_apply_pyprojecttoml.py", "Lib/site-packages/setuptools/config/_validate_pyproject/NOTICE", "Lib/site-packages/setuptools/config/_validate_pyproject/__init__.py", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/error_reporting.cpython-311.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/extra_validations.cpython-311.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_exceptions.cpython-311.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_validations.cpython-311.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/formats.cpython-311.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/error_reporting.py", "Lib/site-packages/setuptools/config/_validate_pyproject/extra_validations.py", "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py", "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py", "Lib/site-packages/setuptools/config/_validate_pyproject/formats.py", "Lib/site-packages/setuptools/config/distutils.schema.json", "Lib/site-packages/setuptools/config/expand.py", "Lib/site-packages/setuptools/config/pyprojecttoml.py", "Lib/site-packages/setuptools/config/setupcfg.py", "Lib/site-packages/setuptools/config/setuptools.schema.json", "Lib/site-packages/setuptools/depends.py", "Lib/site-packages/setuptools/discovery.py", "Lib/site-packages/setuptools/dist.py", "Lib/site-packages/setuptools/errors.py", "Lib/site-packages/setuptools/extension.py", "Lib/site-packages/setuptools/glob.py", "Lib/site-packages/setuptools/gui-32.exe", "Lib/site-packages/setuptools/gui-64.exe", "Lib/site-packages/setuptools/gui-arm64.exe", "Lib/site-packages/setuptools/gui.exe", "Lib/site-packages/setuptools/installer.py", "Lib/site-packages/setuptools/launch.py", "Lib/site-packages/setuptools/logging.py", "Lib/site-packages/setuptools/modified.py", "Lib/site-packages/setuptools/monkey.py", "Lib/site-packages/setuptools/msvc.py", "Lib/site-packages/setuptools/namespaces.py", "Lib/site-packages/setuptools/package_index.py", "Lib/site-packages/setuptools/sandbox.py", "Lib/site-packages/setuptools/script (dev).tmpl", "Lib/site-packages/setuptools/script.tmpl", "Lib/site-packages/setuptools/tests/__init__.py", "Lib/site-packages/setuptools/tests/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/contexts.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/environment.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/fixtures.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/mod_with_constant.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/namespaces.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/script-with-bom.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/server.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_archive_util.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_deprecations.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_egg.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_wheel.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_clib.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_ext.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_meta.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_py.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_config_discovery.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_core_metadata.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_depends.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_develop.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_dist.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_dist_info.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_distutils_adoption.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_easy_install.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_editable_install.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_egg_info.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_extern.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_find_packages.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_find_py_modules.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_glob.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_install_scripts.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_logging.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_manifest.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_namespaces.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_packageindex.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_sandbox.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_sdist.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_setopt.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_setuptools.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_unicode_utils.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_virtualenv.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_warnings.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_wheel.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_windows_wrappers.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/text.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/textwrap.cpython-311.pyc", "Lib/site-packages/setuptools/tests/compat/__init__.py", "Lib/site-packages/setuptools/tests/compat/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/tests/compat/__pycache__/py39.cpython-311.pyc", "Lib/site-packages/setuptools/tests/compat/py39.py", "Lib/site-packages/setuptools/tests/config/__init__.py", "Lib/site-packages/setuptools/tests/config/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_apply_pyprojecttoml.cpython-311.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_expand.cpython-311.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml.cpython-311.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml_dynamic_deps.cpython-311.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_setupcfg.cpython-311.pyc", "Lib/site-packages/setuptools/tests/config/downloads/__init__.py", "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/preload.cpython-311.pyc", "Lib/site-packages/setuptools/tests/config/downloads/preload.py", "Lib/site-packages/setuptools/tests/config/setupcfg_examples.txt", "Lib/site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py", "Lib/site-packages/setuptools/tests/config/test_expand.py", "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml.py", "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py", "Lib/site-packages/setuptools/tests/config/test_setupcfg.py", "Lib/site-packages/setuptools/tests/contexts.py", "Lib/site-packages/setuptools/tests/environment.py", "Lib/site-packages/setuptools/tests/fixtures.py", "Lib/site-packages/setuptools/tests/indexes/test_links_priority/external.html", "Lib/site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/index.html", "Lib/site-packages/setuptools/tests/integration/__init__.py", "Lib/site-packages/setuptools/tests/integration/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/tests/integration/__pycache__/helpers.cpython-311.pyc", "Lib/site-packages/setuptools/tests/integration/__pycache__/test_pip_install_sdist.cpython-311.pyc", "Lib/site-packages/setuptools/tests/integration/helpers.py", "Lib/site-packages/setuptools/tests/integration/test_pip_install_sdist.py", "Lib/site-packages/setuptools/tests/mod_with_constant.py", "Lib/site-packages/setuptools/tests/namespaces.py", "Lib/site-packages/setuptools/tests/script-with-bom.py", "Lib/site-packages/setuptools/tests/server.py", "Lib/site-packages/setuptools/tests/test_archive_util.py", "Lib/site-packages/setuptools/tests/test_bdist_deprecations.py", "Lib/site-packages/setuptools/tests/test_bdist_egg.py", "Lib/site-packages/setuptools/tests/test_bdist_wheel.py", "Lib/site-packages/setuptools/tests/test_build.py", "Lib/site-packages/setuptools/tests/test_build_clib.py", "Lib/site-packages/setuptools/tests/test_build_ext.py", "Lib/site-packages/setuptools/tests/test_build_meta.py", "Lib/site-packages/setuptools/tests/test_build_py.py", "Lib/site-packages/setuptools/tests/test_config_discovery.py", "Lib/site-packages/setuptools/tests/test_core_metadata.py", "Lib/site-packages/setuptools/tests/test_depends.py", "Lib/site-packages/setuptools/tests/test_develop.py", "Lib/site-packages/setuptools/tests/test_dist.py", "Lib/site-packages/setuptools/tests/test_dist_info.py", "Lib/site-packages/setuptools/tests/test_distutils_adoption.py", "Lib/site-packages/setuptools/tests/test_easy_install.py", "Lib/site-packages/setuptools/tests/test_editable_install.py", "Lib/site-packages/setuptools/tests/test_egg_info.py", "Lib/site-packages/setuptools/tests/test_extern.py", "Lib/site-packages/setuptools/tests/test_find_packages.py", "Lib/site-packages/setuptools/tests/test_find_py_modules.py", "Lib/site-packages/setuptools/tests/test_glob.py", "Lib/site-packages/setuptools/tests/test_install_scripts.py", "Lib/site-packages/setuptools/tests/test_logging.py", "Lib/site-packages/setuptools/tests/test_manifest.py", "Lib/site-packages/setuptools/tests/test_namespaces.py", "Lib/site-packages/setuptools/tests/test_packageindex.py", "Lib/site-packages/setuptools/tests/test_sandbox.py", "Lib/site-packages/setuptools/tests/test_sdist.py", "Lib/site-packages/setuptools/tests/test_setopt.py", "Lib/site-packages/setuptools/tests/test_setuptools.py", "Lib/site-packages/setuptools/tests/test_unicode_utils.py", "Lib/site-packages/setuptools/tests/test_virtualenv.py", "Lib/site-packages/setuptools/tests/test_warnings.py", "Lib/site-packages/setuptools/tests/test_wheel.py", "Lib/site-packages/setuptools/tests/test_windows_wrappers.py", "Lib/site-packages/setuptools/tests/text.py", "Lib/site-packages/setuptools/tests/textwrap.py", "Lib/site-packages/setuptools/unicode_utils.py", "Lib/site-packages/setuptools/version.py", "Lib/site-packages/setuptools/warnings.py", "Lib/site-packages/setuptools/wheel.py", "Lib/site-packages/setuptools/windows_support.py"], "fn": "setuptools-75.1.0-py311haa95532_0.conda", "license": "MIT", "link": {"source": "D:\\anaconda3\\pkgs\\setuptools-75.1.0-py311haa95532_0", "type": 1}, "md5": "13c0db2b4ba43e99c14d8fc9beafcfc2", "name": "setuptools", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\setuptools-75.1.0-py311haa95532_0.conda", "paths_data": {"paths": [{"_path": "Lib/site-packages/_distutils_hack/__init__.py", "path_type": "hardlink", "sha256": "a325c4f62f92a19daaa558088e4339afd78e14df40c20a7d51c9dc3a714dc237", "sha256_in_prefix": "a325c4f62f92a19daaa558088e4339afd78e14df40c20a7d51c9dc3a714dc237", "size_in_bytes": 6754}, {"_path": "Lib/site-packages/_distutils_hack/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "d68a5b3a1196414cff47a145df25f9c4dd2f2b1c0806aa63bc751dabe5dfb4fa", "sha256_in_prefix": "d68a5b3a1196414cff47a145df25f9c4dd2f2b1c0806aa63bc751dabe5dfb4fa", "size_in_bytes": 11898}, {"_path": "Lib/site-packages/_distutils_hack/__pycache__/override.cpython-311.pyc", "path_type": "hardlink", "sha256": "037855fc93d1f80792803bbdae5f2db8f061f6f6b4d9a199191ff83f383ba101", "sha256_in_prefix": "037855fc93d1f80792803bbdae5f2db8f061f6f6b4d9a199191ff83f383ba101", "size_in_bytes": 276}, {"_path": "Lib/site-packages/_distutils_hack/override.py", "path_type": "hardlink", "sha256": "12efecf8d17a5486780aa774b5b6c0e70b56932d8864f35df1eb7a18bb759b3a", "sha256_in_prefix": "12efecf8d17a5486780aa774b5b6c0e70b56932d8864f35df1eb7a18bb759b3a", "size_in_bytes": 44}, {"_path": "Lib/site-packages/distutils-precedence.pth", "path_type": "hardlink", "sha256": "ab406aa05439fe87070cde36180433193568432f11d04f0f762f374b8a9302f5", "sha256_in_prefix": "ab406aa05439fe87070cde36180433193568432f11d04f0f762f374b8a9302f5", "size_in_bytes": 152}, {"_path": "Lib/site-packages/pkg_resources/__init__.py", "path_type": "hardlink", "sha256": "f9debd04eb4cc7e904db6fbc252287243e21c7e75dfbc73cbfc1bf9d3d4772e4", "sha256_in_prefix": "f9debd04eb4cc7e904db6fbc252287243e21c7e75dfbc73cbfc1bf9d3d4772e4", "size_in_bytes": 126236}, {"_path": "Lib/site-packages/pkg_resources/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "457e2b37ebcbe2c85e9a1aa87401ff57908e401c32f313142f7bba572d278f05", "sha256_in_prefix": "457e2b37ebcbe2c85e9a1aa87401ff57908e401c32f313142f7bba572d278f05", "size_in_bytes": 179111}, {"_path": "Lib/site-packages/pkg_resources/api_tests.txt", "path_type": "hardlink", "sha256": "5c476fcb88a01c7aeadaa34734c1e795f3ba5d240a36a3b22c76e5e907297c02", "sha256_in_prefix": "5c476fcb88a01c7aeadaa34734c1e795f3ba5d240a36a3b22c76e5e907297c02", "size_in_bytes": 12595}, {"_path": "Lib/site-packages/pkg_resources/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pkg_resources/tests/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "e1fa137085fafdfa0dc954bb94078440d7b45bdb627ad5363a17bef14889331c", "sha256_in_prefix": "e1fa137085fafdfa0dc954bb94078440d7b45bdb627ad5363a17bef14889331c", "size_in_bytes": 158}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_find_distributions.cpython-311.pyc", "path_type": "hardlink", "sha256": "23ac553099f6bc302c09960256a3a8f49aca62ea21abd954dd7335b987a78b4f", "sha256_in_prefix": "23ac553099f6bc302c09960256a3a8f49aca62ea21abd954dd7335b987a78b4f", "size_in_bytes": 3995}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_integration_zope_interface.cpython-311.pyc", "path_type": "hardlink", "sha256": "4518394e4fca9adc411dde53e068289b05fadb34976f5bc30b861cd996e9cf0b", "sha256_in_prefix": "4518394e4fca9adc411dde53e068289b05fadb34976f5bc30b861cd996e9cf0b", "size_in_bytes": 2072}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_markers.cpython-311.pyc", "path_type": "hardlink", "sha256": "4241aac53fabf3d3701c44c1cec53282eebd0752671434ba31c242ce663f05a6", "sha256_in_prefix": "4241aac53fabf3d3701c44c1cec53282eebd0752671434ba31c242ce663f05a6", "size_in_bytes": 664}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_pkg_resources.cpython-311.pyc", "path_type": "hardlink", "sha256": "644840931971c8b8f72b846d9a519b64f13f478cb70a5e27a6be44e498ee71e9", "sha256_in_prefix": "644840931971c8b8f72b846d9a519b64f13f478cb70a5e27a6be44e498ee71e9", "size_in_bytes": 25246}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_resources.cpython-311.pyc", "path_type": "hardlink", "sha256": "f37403fdafaccf7d15770eed6b53041ebaa1962b64ae0a708a99cb97e4951adc", "sha256_in_prefix": "f37403fdafaccf7d15770eed6b53041ebaa1962b64ae0a708a99cb97e4951adc", "size_in_bytes": 54757}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_working_set.cpython-311.pyc", "path_type": "hardlink", "sha256": "80083058fa753471ac3490c3ac151bd22787532f2fa2651bf86f2b66be116a67", "sha256_in_prefix": "80083058fa753471ac3490c3ac151bd22787532f2fa2651bf86f2b66be116a67", "size_in_bytes": 11419}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/__pycache__/setup.cpython-311.pyc", "path_type": "hardlink", "sha256": "dee1e4d268f4cac6f41fca82b7887273788994b5e88ffb453cfbd1fcd4936938", "sha256_in_prefix": "dee1e4d268f4cac6f41fca82b7887273788994b5e88ffb453cfbd1fcd4936938", "size_in_bytes": 349}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.cfg", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py", "path_type": "hardlink", "sha256": "d55a1b84065b31beccf667e16ff344f0fc03b2fba4a162ecf5a5004b4a5885ef", "sha256_in_prefix": "d55a1b84065b31beccf667e16ff344f0fc03b2fba4a162ecf5a5004b4a5885ef", "size_in_bytes": 105}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip", "path_type": "hardlink", "sha256": "01845c437f4655e3cf9cc4fc4e49cfd607431f22675e1b611129a90239f34822", "sha256_in_prefix": "01845c437f4655e3cf9cc4fc4e49cfd607431f22675e1b611129a90239f34822", "size_in_bytes": 1809}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO", "path_type": "hardlink", "sha256": "26f5aff48a363c0b98c04130d9f056e1073962f75b92c729297d6498bceca079", "sha256_in_prefix": "26f5aff48a363c0b98c04130d9f056e1073962f75b92c729297d6498bceca079", "size_in_bytes": 187}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt", "path_type": "hardlink", "sha256": "e029641fc793a2f66b755ac916c56ec5d6cc105fbe941552b8aa270c03c4e497", "sha256_in_prefix": "e029641fc793a2f66b755ac916c56ec5d6cc105fbe941552b8aa270c03c4e497", "size_in_bytes": 208}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg", "path_type": "hardlink", "sha256": "65394c1b18d11a2283364880d9cef98db407d93588b5e3f4d22ac5f60bdccdba", "sha256_in_prefix": "65394c1b18d11a2283364880d9cef98db407d93588b5e3f4d22ac5f60bdccdba", "size_in_bytes": 843}, {"_path": "Lib/site-packages/pkg_resources/tests/test_find_distributions.py", "path_type": "hardlink", "sha256": "53dd5ca2fe4bd423802162cdab75f2e29954eff327384d56b5732eea2576c1a3", "sha256_in_prefix": "53dd5ca2fe4bd423802162cdab75f2e29954eff327384d56b5732eea2576c1a3", "size_in_bytes": 1972}, {"_path": "Lib/site-packages/pkg_resources/tests/test_integration_zope_interface.py", "path_type": "hardlink", "sha256": "9f35682b9e7b29940dd15dc3210d6c55e6823a0b782a997e08e0c05ac3bba667", "sha256_in_prefix": "9f35682b9e7b29940dd15dc3210d6c55e6823a0b782a997e08e0c05ac3bba667", "size_in_bytes": 1652}, {"_path": "Lib/site-packages/pkg_resources/tests/test_markers.py", "path_type": "hardlink", "sha256": "d28aca83b50c0dfedf9ee350bd130e73e105f4400ffc94d09e4e26b4681b5b9d", "sha256_in_prefix": "d28aca83b50c0dfedf9ee350bd130e73e105f4400ffc94d09e4e26b4681b5b9d", "size_in_bytes": 241}, {"_path": "Lib/site-packages/pkg_resources/tests/test_pkg_resources.py", "path_type": "hardlink", "sha256": "9703980093ae8b47bcea2b184fb72af4e24baac07c7971c1a9396b40e6a83c88", "sha256_in_prefix": "9703980093ae8b47bcea2b184fb72af4e24baac07c7971c1a9396b40e6a83c88", "size_in_bytes": 15221}, {"_path": "Lib/site-packages/pkg_resources/tests/test_resources.py", "path_type": "hardlink", "sha256": "be3bdb5fd4a40d12dcc483b9fe0dd9b97f998965255a869734776b114cb115db", "sha256_in_prefix": "be3bdb5fd4a40d12dcc483b9fe0dd9b97f998965255a869734776b114cb115db", "size_in_bytes": 31252}, {"_path": "Lib/site-packages/pkg_resources/tests/test_working_set.py", "path_type": "hardlink", "sha256": "bf795fdcd4ac18637028915963bcb50b9fdca7e7675eca3106e4b17b547093bf", "sha256_in_prefix": "bf795fdcd4ac18637028915963bcb50b9fdca7e7675eca3106e4b17b547093bf", "size_in_bytes": 8531}, {"_path": "Lib/site-packages/setuptools-75.1.0-py3.11.egg-info/PKG-INFO", "path_type": "hardlink", "sha256": "8116a9c6ab119424e37afcc1afa1ee71950e7f3f2eecdbdad948a8bbeb769e2d", "sha256_in_prefix": "8116a9c6ab119424e37afcc1afa1ee71950e7f3f2eecdbdad948a8bbeb769e2d", "size_in_bytes": 6890}, {"_path": "Lib/site-packages/setuptools-75.1.0-py3.11.egg-info/SOURCES.txt", "path_type": "hardlink", "sha256": "d6c6d4b49f9d02054f3ca153339edb65d8202e2e4190121731beda9eff9a3b69", "sha256_in_prefix": "d6c6d4b49f9d02054f3ca153339edb65d8202e2e4190121731beda9eff9a3b69", "size_in_bytes": 26967}, {"_path": "Lib/site-packages/setuptools-75.1.0-py3.11.egg-info/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/setuptools-75.1.0-py3.11.egg-info/entry_points.txt", "path_type": "hardlink", "sha256": "ce482d8697ff15af4d544f69e85293dd793d0d1d5f680711538728820b15ee30", "sha256_in_prefix": "ce482d8697ff15af4d544f69e85293dd793d0d1d5f680711538728820b15ee30", "size_in_bytes": 2449}, {"_path": "Lib/site-packages/setuptools-75.1.0-py3.11.egg-info/requires.txt", "path_type": "hardlink", "sha256": "8a17de53e3a00d8c689f2da11392108c59bf7c3671a01e176e07e1ee21374d3f", "sha256_in_prefix": "8a17de53e3a00d8c689f2da11392108c59bf7c3671a01e176e07e1ee21374d3f", "size_in_bytes": 1309}, {"_path": "Lib/site-packages/setuptools-75.1.0-py3.11.egg-info/top_level.txt", "path_type": "hardlink", "sha256": "77dc8bdfdbff5bbaa62830d21fab13e1b1348ff2ecd4cdcfd7ad4e1a076c9b88", "sha256_in_prefix": "77dc8bdfdbff5bbaa62830d21fab13e1b1348ff2ecd4cdcfd7ad4e1a076c9b88", "size_in_bytes": 41}, {"_path": "Lib/site-packages/setuptools/__init__.py", "path_type": "hardlink", "sha256": "590952d9d042497dd2ce81fae1dce3735b0923e3aed86646cef6d888b08258c0", "sha256_in_prefix": "590952d9d042497dd2ce81fae1dce3735b0923e3aed86646cef6d888b08258c0", "size_in_bytes": 10389}, {"_path": "Lib/site-packages/setuptools/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "31c6826e4c9f9ca4ef2f2d2306b5e371c02f9b78d13ef20dab3686aea1d7bd80", "sha256_in_prefix": "31c6826e4c9f9ca4ef2f2d2306b5e371c02f9b78d13ef20dab3686aea1d7bd80", "size_in_bytes": 15352}, {"_path": "Lib/site-packages/setuptools/__pycache__/_core_metadata.cpython-311.pyc", "path_type": "hardlink", "sha256": "9015509c4c0e7bc64fdd190a63cf42264530911e33830a58c0661b2905f9f998", "sha256_in_prefix": "9015509c4c0e7bc64fdd190a63cf42264530911e33830a58c0661b2905f9f998", "size_in_bytes": 15122}, {"_path": "Lib/site-packages/setuptools/__pycache__/_entry_points.cpython-311.pyc", "path_type": "hardlink", "sha256": "f17347a0ad43746c6b148235cc2bf75b9aaa079efe71c588fca65707944a3907", "sha256_in_prefix": "f17347a0ad43746c6b148235cc2bf75b9aaa079efe71c588fca65707944a3907", "size_in_bytes": 5109}, {"_path": "Lib/site-packages/setuptools/__pycache__/_imp.cpython-311.pyc", "path_type": "hardlink", "sha256": "8d46ecf5195c86727a6abe019ed0ae285f8ad17d0a462c19373cd601cf7070f0", "sha256_in_prefix": "8d46ecf5195c86727a6abe019ed0ae285f8ad17d0a462c19373cd601cf7070f0", "size_in_bytes": 3650}, {"_path": "Lib/site-packages/setuptools/__pycache__/_importlib.cpython-311.pyc", "path_type": "hardlink", "sha256": "92236e339ee25284cd1d18b254ab823c1aa2d713f7e414f2b7a0adb14deefe99", "sha256_in_prefix": "92236e339ee25284cd1d18b254ab823c1aa2d713f7e414f2b7a0adb14deefe99", "size_in_bytes": 501}, {"_path": "Lib/site-packages/setuptools/__pycache__/_itertools.cpython-311.pyc", "path_type": "hardlink", "sha256": "534b8d356b49a699914b9c2bdf5073e92c623cc1609411ca180e13402cdac421", "sha256_in_prefix": "534b8d356b49a699914b9c2bdf5073e92c623cc1609411ca180e13402cdac421", "size_in_bytes": 1098}, {"_path": "Lib/site-packages/setuptools/__pycache__/_normalization.cpython-311.pyc", "path_type": "hardlink", "sha256": "b87920ef654a61c55fcf39b60972ec792de71013ff67c52319e1d38f70a846da", "sha256_in_prefix": "b87920ef654a61c55fcf39b60972ec792de71013ff67c52319e1d38f70a846da", "size_in_bytes": 6683}, {"_path": "Lib/site-packages/setuptools/__pycache__/_path.cpython-311.pyc", "path_type": "hardlink", "sha256": "c37210fb140142e4aeb677d0b47523d24e2f8e11d3dd72436bf58a50eed916d2", "sha256_in_prefix": "c37210fb140142e4aeb677d0b47523d24e2f8e11d3dd72436bf58a50eed916d2", "size_in_bytes": 4423}, {"_path": "Lib/site-packages/setuptools/__pycache__/_reqs.cpython-311.pyc", "path_type": "hardlink", "sha256": "3af254d94cf192cd6460d3da6d7cf10b1bfc3a91ba00b8797485b46d57bcea37", "sha256_in_prefix": "3af254d94cf192cd6460d3da6d7cf10b1bfc3a91ba00b8797485b46d57bcea37", "size_in_bytes": 2214}, {"_path": "Lib/site-packages/setuptools/__pycache__/archive_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "5dec9017649edad6b3b888ddbe591aa0adebc0922dc187a3ebcc8b8075a317c8", "sha256_in_prefix": "5dec9017649edad6b3b888ddbe591aa0adebc0922dc187a3ebcc8b8075a317c8", "size_in_bytes": 10132}, {"_path": "Lib/site-packages/setuptools/__pycache__/build_meta.cpython-311.pyc", "path_type": "hardlink", "sha256": "5e0d70a9df1de05633a68df556282d5a5fab03a0f2ccbc97ed3b5b193cadd9fb", "sha256_in_prefix": "5e0d70a9df1de05633a68df556282d5a5fab03a0f2ccbc97ed3b5b193cadd9fb", "size_in_bytes": 26648}, {"_path": "Lib/site-packages/setuptools/__pycache__/depends.cpython-311.pyc", "path_type": "hardlink", "sha256": "90a391fa00ac69eb03e2a98ece5842c2f870e62fc091b304ec3c2c3cbac371fe", "sha256_in_prefix": "90a391fa00ac69eb03e2a98ece5842c2f870e62fc091b304ec3c2c3cbac371fe", "size_in_bytes": 7570}, {"_path": "Lib/site-packages/setuptools/__pycache__/discovery.cpython-311.pyc", "path_type": "hardlink", "sha256": "8e58ed70e33a6b93f86079948c8d711a06136926cc2686a9967d410b6e64999d", "sha256_in_prefix": "8e58ed70e33a6b93f86079948c8d711a06136926cc2686a9967d410b6e64999d", "size_in_bytes": 30941}, {"_path": "Lib/site-packages/setuptools/__pycache__/dist.cpython-311.pyc", "path_type": "hardlink", "sha256": "bad8eb986e054533e5c689ed2b45fcdd701d35a39d39c28649a188790e2fa0b9", "sha256_in_prefix": "bad8eb986e054533e5c689ed2b45fcdd701d35a39d39c28649a188790e2fa0b9", "size_in_bytes": 49913}, {"_path": "Lib/site-packages/setuptools/__pycache__/errors.cpython-311.pyc", "path_type": "hardlink", "sha256": "ceb04a251b50111b7df43ec2209a87e1a6e5caa700ba0e1877d5371643ee6aee", "sha256_in_prefix": "ceb04a251b50111b7df43ec2209a87e1a6e5caa700ba0e1877d5371643ee6aee", "size_in_bytes": 4141}, {"_path": "Lib/site-packages/setuptools/__pycache__/extension.cpython-311.pyc", "path_type": "hardlink", "sha256": "3dbc1397bafa6d00dabeabab89fa9300359762da680b0d557070e7e845d1feeb", "sha256_in_prefix": "3dbc1397bafa6d00dabeabab89fa9300359762da680b0d557070e7e845d1feeb", "size_in_bytes": 7233}, {"_path": "Lib/site-packages/setuptools/__pycache__/glob.cpython-311.pyc", "path_type": "hardlink", "sha256": "2197a16579b93a6a5d66559f6a36209f02adb8028e9e80b4c04f2fad209a5aed", "sha256_in_prefix": "2197a16579b93a6a5d66559f6a36209f02adb8028e9e80b4c04f2fad209a5aed", "size_in_bytes": 6493}, {"_path": "Lib/site-packages/setuptools/__pycache__/installer.cpython-311.pyc", "path_type": "hardlink", "sha256": "9062521685bf8a81defe5fcfa577da0a45d33643c4ee50674ff7b10a7ac3389c", "sha256_in_prefix": "9062521685bf8a81defe5fcfa577da0a45d33643c4ee50674ff7b10a7ac3389c", "size_in_bytes": 7179}, {"_path": "Lib/site-packages/setuptools/__pycache__/launch.cpython-311.pyc", "path_type": "hardlink", "sha256": "042119ba51405ed148b8d8394e52376b9ba54bf1fe385320b7ced2986c5384c1", "sha256_in_prefix": "042119ba51405ed148b8d8394e52376b9ba54bf1fe385320b7ced2986c5384c1", "size_in_bytes": 1492}, {"_path": "Lib/site-packages/setuptools/__pycache__/logging.cpython-311.pyc", "path_type": "hardlink", "sha256": "58888ba0fbcd259d1aaa79076d9a944452a020ba17b26bcb872509d0b8f25298", "sha256_in_prefix": "58888ba0fbcd259d1aaa79076d9a944452a020ba17b26bcb872509d0b8f25298", "size_in_bytes": 2057}, {"_path": "Lib/site-packages/setuptools/__pycache__/modified.cpython-311.pyc", "path_type": "hardlink", "sha256": "431dfe5a9252917a2fe2536504cc8269485243bd18325b5cd68e55b90ca6abaf", "sha256_in_prefix": "431dfe5a9252917a2fe2536504cc8269485243bd18325b5cd68e55b90ca6abaf", "size_in_bytes": 384}, {"_path": "Lib/site-packages/setuptools/__pycache__/monkey.cpython-311.pyc", "path_type": "hardlink", "sha256": "69334a4b56c7fa6ae3e3068172d407f143cb8b0189e3a3216ec0d0173d2868a3", "sha256_in_prefix": "69334a4b56c7fa6ae3e3068172d407f143cb8b0189e3a3216ec0d0173d2868a3", "size_in_bytes": 5470}, {"_path": "Lib/site-packages/setuptools/__pycache__/msvc.cpython-311.pyc", "path_type": "hardlink", "sha256": "eb7ff61c110fe2fd1be1e4d72243d67f114a5d573d94a91cd6d323b22e11ee8e", "sha256_in_prefix": "eb7ff61c110fe2fd1be1e4d72243d67f114a5d573d94a91cd6d323b22e11ee8e", "size_in_bytes": 58333}, {"_path": "Lib/site-packages/setuptools/__pycache__/namespaces.cpython-311.pyc", "path_type": "hardlink", "sha256": "3dd65d00427c05a0d497abd6784a10787d7108416f199b4ccd1279d4110a6ce9", "sha256_in_prefix": "3dd65d00427c05a0d497abd6784a10787d7108416f199b4ccd1279d4110a6ce9", "size_in_bytes": 5783}, {"_path": "Lib/site-packages/setuptools/__pycache__/package_index.cpython-311.pyc", "path_type": "hardlink", "sha256": "d8396f3ae63223cf55e08ab5a2469fbad77110b40d87b56a235dc99e54e0c449", "sha256_in_prefix": "d8396f3ae63223cf55e08ab5a2469fbad77110b40d87b56a235dc99e54e0c449", "size_in_bytes": 59855}, {"_path": "Lib/site-packages/setuptools/__pycache__/sandbox.cpython-311.pyc", "path_type": "hardlink", "sha256": "29ef8e6aec4b174957a5b39fb3fc93fb14cc68f005c84a86ca633e9116a94dc4", "sha256_in_prefix": "29ef8e6aec4b174957a5b39fb3fc93fb14cc68f005c84a86ca633e9116a94dc4", "size_in_bytes": 27285}, {"_path": "Lib/site-packages/setuptools/__pycache__/unicode_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "732952b73739785ed30b426aa9ccd24e48d5c0efe35841ac3175c885b5b769da", "sha256_in_prefix": "732952b73739785ed30b426aa9ccd24e48d5c0efe35841ac3175c885b5b769da", "size_in_bytes": 4940}, {"_path": "Lib/site-packages/setuptools/__pycache__/version.cpython-311.pyc", "path_type": "hardlink", "sha256": "dd90c169f99aab3cb2e9048fad7d5d77fb2efeb3f12040f52804d99d52a1cd5a", "sha256_in_prefix": "dd90c169f99aab3cb2e9048fad7d5d77fb2efeb3f12040f52804d99d52a1cd5a", "size_in_bytes": 409}, {"_path": "Lib/site-packages/setuptools/__pycache__/warnings.cpython-311.pyc", "path_type": "hardlink", "sha256": "837c52e959ef49d12347352df01967c2fd674ff4575da846e46ebe2b5a908828", "sha256_in_prefix": "837c52e959ef49d12347352df01967c2fd674ff4575da846e46ebe2b5a908828", "size_in_bytes": 5596}, {"_path": "Lib/site-packages/setuptools/__pycache__/wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "0be7f9f30c581fbc6fae0e98eaad894e6823917e8b66139e571c2fab5a8bf234", "sha256_in_prefix": "0be7f9f30c581fbc6fae0e98eaad894e6823917e8b66139e571c2fab5a8bf234", "size_in_bytes": 15415}, {"_path": "Lib/site-packages/setuptools/__pycache__/windows_support.cpython-311.pyc", "path_type": "hardlink", "sha256": "76823f8778c3335278db954dc57936ab58ec496bcc2f9330576798641162deaa", "sha256_in_prefix": "76823f8778c3335278db954dc57936ab58ec496bcc2f9330576798641162deaa", "size_in_bytes": 1407}, {"_path": "Lib/site-packages/setuptools/_core_metadata.py", "path_type": "hardlink", "sha256": "ef1794b0ba1c692650960450f35f87551316516157332149a55ca30832605d2d", "sha256_in_prefix": "ef1794b0ba1c692650960450f35f87551316516157332149a55ca30832605d2d", "size_in_bytes": 9795}, {"_path": "Lib/site-packages/setuptools/_distutils/__init__.py", "path_type": "hardlink", "sha256": "c4662e856c0b1b4ec9d10e3d0559c48cfcbac320dc77abde24c0c95fb9639723", "sha256_in_prefix": "c4662e856c0b1b4ec9d10e3d0559c48cfcbac320dc77abde24c0c95fb9639723", "size_in_bytes": 359}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "86e825a222a39bfc2051fcb6e6bc0f99aadce54f5aba84163c8b72d1527f6215", "sha256_in_prefix": "86e825a222a39bfc2051fcb6e6bc0f99aadce54f5aba84163c8b72d1527f6215", "size_in_bytes": 523}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_log.cpython-311.pyc", "path_type": "hardlink", "sha256": "a049f2b5590edf0e57e5a57605edaf41077950d5b6b6e2539e0aa425c59a22fc", "sha256_in_prefix": "a049f2b5590edf0e57e5a57605edaf41077950d5b6b6e2539e0aa425c59a22fc", "size_in_bytes": 240}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_macos_compat.cpython-311.pyc", "path_type": "hardlink", "sha256": "128ff777a5f80a8ecd4208a38d41048093021e0ae31eb3af8ae19b9548e1a7b7", "sha256_in_prefix": "128ff777a5f80a8ecd4208a38d41048093021e0ae31eb3af8ae19b9548e1a7b7", "size_in_bytes": 534}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_modified.cpython-311.pyc", "path_type": "hardlink", "sha256": "141202d7ba48a91f9f22493f0052f87b39d52314b706ad67022a65b5c9cf7b6a", "sha256_in_prefix": "141202d7ba48a91f9f22493f0052f87b39d52314b706ad67022a65b5c9cf7b6a", "size_in_bytes": 4259}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_msvccompiler.cpython-311.pyc", "path_type": "hardlink", "sha256": "c8938a9f648772189d8e42f7104071b50a57af55d2a9fbb16a853c22d3bf6952", "sha256_in_prefix": "c8938a9f648772189d8e42f7104071b50a57af55d2a9fbb16a853c22d3bf6952", "size_in_bytes": 26859}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/archive_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "0430241fcae81612d90415f5c58a64eed9c246d7ea2e8ef87519ab4cc1843d46", "sha256_in_prefix": "0430241fcae81612d90415f5c58a64eed9c246d7ea2e8ef87519ab4cc1843d46", "size_in_bytes": 10001}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/ccompiler.cpython-311.pyc", "path_type": "hardlink", "sha256": "890c8fb00f2112513b7c589e7ebd25ec8e01723af93e8777fdd076f48921f837", "sha256_in_prefix": "890c8fb00f2112513b7c589e7ebd25ec8e01723af93e8777fdd076f48921f837", "size_in_bytes": 48361}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/cmd.cpython-311.pyc", "path_type": "hardlink", "sha256": "6170bcabfec51e227ce992cbc0d29fab6df335d60ccd3e30b23022b35aec99f2", "sha256_in_prefix": "6170bcabfec51e227ce992cbc0d29fab6df335d60ccd3e30b23022b35aec99f2", "size_in_bytes": 18739}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/core.cpython-311.pyc", "path_type": "hardlink", "sha256": "321d4f6f7fa75c56afa7d71c0d16e07f207c47229ec64b6b43d0521dab644e13", "sha256_in_prefix": "321d4f6f7fa75c56afa7d71c0d16e07f207c47229ec64b6b43d0521dab644e13", "size_in_bytes": 9752}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/cygwinccompiler.cpython-311.pyc", "path_type": "hardlink", "sha256": "cb7b1e5d4beb4015b338a6476d78b84034cf82cfd2c281081a08dc418c3764fa", "sha256_in_prefix": "cb7b1e5d4beb4015b338a6476d78b84034cf82cfd2c281081a08dc418c3764fa", "size_in_bytes": 13048}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/debug.cpython-311.pyc", "path_type": "hardlink", "sha256": "926078079f9db9c6f6b1c903886d1cee5cbf769edcabc855042fb3a0614fac7d", "sha256_in_prefix": "926078079f9db9c6f6b1c903886d1cee5cbf769edcabc855042fb3a0614fac7d", "size_in_bytes": 286}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/dep_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "d7d143196e9360d3a0cc3fec4d03a1d4f23b6d42394180530289f15eeebf6b01", "sha256_in_prefix": "d7d143196e9360d3a0cc3fec4d03a1d4f23b6d42394180530289f15eeebf6b01", "size_in_bytes": 752}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/dir_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "b9f7003cb6be32d66b1729d7f812b09abcb50a861fb42f239da3e470c839538b", "sha256_in_prefix": "b9f7003cb6be32d66b1729d7f812b09abcb50a861fb42f239da3e470c839538b", "size_in_bytes": 11811}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/dist.cpython-311.pyc", "path_type": "hardlink", "sha256": "2f79d87aa97ac378d67ab7721d08d44433cbb972c11941d0912cbc77bf7dd353", "sha256_in_prefix": "2f79d87aa97ac378d67ab7721d08d44433cbb972c11941d0912cbc77bf7dd353", "size_in_bytes": 56872}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/errors.cpython-311.pyc", "path_type": "hardlink", "sha256": "932937cef80a95b8fe5ac67e76751b39a5fd1e2fcb9bdb8fc68bd3588b17ee78", "sha256_in_prefix": "932937cef80a95b8fe5ac67e76751b39a5fd1e2fcb9bdb8fc68bd3588b17ee78", "size_in_bytes": 6490}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/extension.cpython-311.pyc", "path_type": "hardlink", "sha256": "30ebd9cc303164fae86dc8b4d4babef5d09ecaabe7b746b1d50d81a3f571affc", "sha256_in_prefix": "30ebd9cc303164fae86dc8b4d4babef5d09ecaabe7b746b1d50d81a3f571affc", "size_in_bytes": 10356}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/fancy_getopt.cpython-311.pyc", "path_type": "hardlink", "sha256": "df9b0d71cc362edcb7efc4d3a35246b6d827bd07b6e89efca098dc91cf53b7dd", "sha256_in_prefix": "df9b0d71cc362edcb7efc4d3a35246b6d827bd07b6e89efca098dc91cf53b7dd", "size_in_bytes": 17295}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/file_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "5d045aaf93c28c3f1c47b66c2fdbb8fa9ff3bc7af31a5e19acc84ad4ed84f7ea", "sha256_in_prefix": "5d045aaf93c28c3f1c47b66c2fdbb8fa9ff3bc7af31a5e19acc84ad4ed84f7ea", "size_in_bytes": 10551}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/filelist.cpython-311.pyc", "path_type": "hardlink", "sha256": "1b5824fe16f98460fea6d5f27130b74a964267a12e68e6a9b97b87eeb925da22", "sha256_in_prefix": "1b5824fe16f98460fea6d5f27130b74a964267a12e68e6a9b97b87eeb925da22", "size_in_bytes": 17538}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/log.cpython-311.pyc", "path_type": "hardlink", "sha256": "e766b00307a3280bc537a05c01eadc92e8faade1c0ee381b706acec6a45b40d4", "sha256_in_prefix": "e766b00307a3280bc537a05c01eadc92e8faade1c0ee381b706acec6a45b40d4", "size_in_bytes": 2655}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/spawn.cpython-311.pyc", "path_type": "hardlink", "sha256": "0512ff390353f97982ea0d0f042aac74f82f529366fd6e811b7bbb5989ab86fc", "sha256_in_prefix": "0512ff390353f97982ea0d0f042aac74f82f529366fd6e811b7bbb5989ab86fc", "size_in_bytes": 5219}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/sysconfig.cpython-311.pyc", "path_type": "hardlink", "sha256": "7109d961a07147d0a829e9b79af155167cd5ad61d53282e7d434d66f0bea6160", "sha256_in_prefix": "7109d961a07147d0a829e9b79af155167cd5ad61d53282e7d434d66f0bea6160", "size_in_bytes": 23784}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/text_file.cpython-311.pyc", "path_type": "hardlink", "sha256": "ce2f9b89f20bda720b426a8cd220e53bf05c77b62bf26ab6bf00a1e5ca34d55d", "sha256_in_prefix": "ce2f9b89f20bda720b426a8cd220e53bf05c77b62bf26ab6bf00a1e5ca34d55d", "size_in_bytes": 11255}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/unixccompiler.cpython-311.pyc", "path_type": "hardlink", "sha256": "c1d2df1ac9ad4477fa856edba5baf5e81c428951e0a91bff3a8b8e61deb3bc33", "sha256_in_prefix": "c1d2df1ac9ad4477fa856edba5baf5e81c428951e0a91bff3a8b8e61deb3bc33", "size_in_bytes": 16092}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/util.cpython-311.pyc", "path_type": "hardlink", "sha256": "907792d61775406faa8fdcbe2a1a6b05d0c34cac20a58481496a38a85a9f7e9c", "sha256_in_prefix": "907792d61775406faa8fdcbe2a1a6b05d0c34cac20a58481496a38a85a9f7e9c", "size_in_bytes": 20457}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/version.cpython-311.pyc", "path_type": "hardlink", "sha256": "eaeb9bacf3abc49a5ae3152cd2ccc3d4f4bcdcd12de9d7f08828a13ae6bb4aec", "sha256_in_prefix": "eaeb9bacf3abc49a5ae3152cd2ccc3d4f4bcdcd12de9d7f08828a13ae6bb4aec", "size_in_bytes": 11490}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/versionpredicate.cpython-311.pyc", "path_type": "hardlink", "sha256": "2ef6a202428f458832661703a1d32f5e68523097d81a6202a22d517d8abc78c2", "sha256_in_prefix": "2ef6a202428f458832661703a1d32f5e68523097d81a6202a22d517d8abc78c2", "size_in_bytes": 7586}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/zosccompiler.cpython-311.pyc", "path_type": "hardlink", "sha256": "9fb6a7cd4703791213df37ed6308b72ff8aee454aba66d85270b093b6d2eb122", "sha256_in_prefix": "9fb6a7cd4703791213df37ed6308b72ff8aee454aba66d85270b093b6d2eb122", "size_in_bytes": 7032}, {"_path": "Lib/site-packages/setuptools/_distutils/_log.py", "path_type": "hardlink", "sha256": "8be94d4d37174bc4e65884c9e833831afb56e73e6d31ab6d250efa87cad9c505", "sha256_in_prefix": "8be94d4d37174bc4e65884c9e833831afb56e73e6d31ab6d250efa87cad9c505", "size_in_bytes": 42}, {"_path": "Lib/site-packages/setuptools/_distutils/_macos_compat.py", "path_type": "hardlink", "sha256": "273506845e04e722084c76d468fa1b6445a318776badc355eb7cfce92e118c17", "sha256_in_prefix": "273506845e04e722084c76d468fa1b6445a318776badc355eb7cfce92e118c17", "size_in_bytes": 239}, {"_path": "Lib/site-packages/setuptools/_distutils/_modified.py", "path_type": "hardlink", "sha256": "259bc850a1e27673bfc9d74e692f68697752ad69f240c89f6ad68092fa6c9c85", "sha256_in_prefix": "259bc850a1e27673bfc9d74e692f68697752ad69f240c89f6ad68092fa6c9c85", "size_in_bytes": 2446}, {"_path": "Lib/site-packages/setuptools/_distutils/_msvccompiler.py", "path_type": "hardlink", "sha256": "e045f2facc92015955ba273207a0b6dacf030b4f57e89d9dd677f729f275e391", "sha256_in_prefix": "e045f2facc92015955ba273207a0b6dacf030b4f57e89d9dd677f729f275e391", "size_in_bytes": 21195}, {"_path": "Lib/site-packages/setuptools/_distutils/archive_util.py", "path_type": "hardlink", "sha256": "d798c76cb9820dc9d9ef9276b451720a608feb2176696133573fa5bac69ecabe", "sha256_in_prefix": "d798c76cb9820dc9d9ef9276b451720a608feb2176696133573fa5bac69ecabe", "size_in_bytes": 7844}, {"_path": "Lib/site-packages/setuptools/_distutils/ccompiler.py", "path_type": "hardlink", "sha256": "c6f05081cb16be3b7581ddc61f7471ac1c428484eaf8d2a114929455840f5b3d", "sha256_in_prefix": "c6f05081cb16be3b7581ddc61f7471ac1c428484eaf8d2a114929455840f5b3d", "size_in_bytes": 48873}, {"_path": "Lib/site-packages/setuptools/_distutils/cmd.py", "path_type": "hardlink", "sha256": "b197839d81ee66aca434c3a55f8bacb25c49e23c0f8c05ca26f5d8bb9a3bb67b", "sha256_in_prefix": "b197839d81ee66aca434c3a55f8bacb25c49e23c0f8c05ca26f5d8bb9a3bb67b", "size_in_bytes": 17877}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__init__.py", "path_type": "hardlink", "sha256": "19f140cdb06a935ab1487e0175a2a2a0a4b88514670f8e01026c0437ce42e2ef", "sha256_in_prefix": "19f140cdb06a935ab1487e0175a2a2a0a4b88514670f8e01026c0437ce42e2ef", "size_in_bytes": 386}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "48c01fb8e4d28edda1c2afb0508ca798d48fc359826d4932d6e5ee837b5eeff5", "sha256_in_prefix": "48c01fb8e4d28edda1c2afb0508ca798d48fc359826d4932d6e5ee837b5eeff5", "size_in_bytes": 488}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/_framework_compat.cpython-311.pyc", "path_type": "hardlink", "sha256": "aa1767aa6d1a9333d18ff4a6e267f7becad0702219128c63c73af8e4aae83cc8", "sha256_in_prefix": "aa1767aa6d1a9333d18ff4a6e267f7becad0702219128c63c73af8e4aae83cc8", "size_in_bytes": 2699}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist.cpython-311.pyc", "path_type": "hardlink", "sha256": "97066b1cbbfdaae865f3021b1926cdc194521c508f4a436f8ed5ff37ca41c98e", "sha256_in_prefix": "97066b1cbbfdaae865f3021b1926cdc194521c508f4a436f8ed5ff37ca41c98e", "size_in_bytes": 6112}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_dumb.cpython-311.pyc", "path_type": "hardlink", "sha256": "b9601ecaf05693c2c3ef15ec7e579ddad6ca47e533b7cefe83cc7f8ed44419fb", "sha256_in_prefix": "b9601ecaf05693c2c3ef15ec7e579ddad6ca47e533b7cefe83cc7f8ed44419fb", "size_in_bytes": 5587}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_rpm.cpython-311.pyc", "path_type": "hardlink", "sha256": "983ce1a7c7712844635ba496afe1bb1fd026ae357a75461cf3011120cc1b733f", "sha256_in_prefix": "983ce1a7c7712844635ba496afe1bb1fd026ae357a75461cf3011120cc1b733f", "size_in_bytes": 23138}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build.cpython-311.pyc", "path_type": "hardlink", "sha256": "3bcb4a9bd4d39e3a5c815ede188f4922174af287bacbb494a334a6f55cd9074b", "sha256_in_prefix": "3bcb4a9bd4d39e3a5c815ede188f4922174af287bacbb494a334a6f55cd9074b", "size_in_bytes": 6140}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_clib.cpython-311.pyc", "path_type": "hardlink", "sha256": "952add1bacf5d37563d252b1905a8ebd12d9cb7ab09a090d205b2578c24c4c92", "sha256_in_prefix": "952add1bacf5d37563d252b1905a8ebd12d9cb7ab09a090d205b2578c24c4c92", "size_in_bytes": 7769}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_ext.cpython-311.pyc", "path_type": "hardlink", "sha256": "096bf5f71543261e00f557b3271c589c30754cf4f5744aa6cbb34614904de087", "sha256_in_prefix": "096bf5f71543261e00f557b3271c589c30754cf4f5744aa6cbb34614904de087", "size_in_bytes": 31075}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_py.cpython-311.pyc", "path_type": "hardlink", "sha256": "3f50a01a37ea2d24df0cd4922371f4ebc8424f0188c7ededd32c592609b49cb6", "sha256_in_prefix": "3f50a01a37ea2d24df0cd4922371f4ebc8424f0188c7ededd32c592609b49cb6", "size_in_bytes": 17605}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_scripts.cpython-311.pyc", "path_type": "hardlink", "sha256": "567b7245bc524c5228d1cc35382567f9f82cbe91927d3c406e81e5c936b1c438", "sha256_in_prefix": "567b7245bc524c5228d1cc35382567f9f82cbe91927d3c406e81e5c936b1c438", "size_in_bytes": 7791}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/check.cpython-311.pyc", "path_type": "hardlink", "sha256": "b454e579c920be71be2931d7af83d3035f25cdb806869faeb602cbacf2b284be", "sha256_in_prefix": "b454e579c920be71be2931d7af83d3035f25cdb806869faeb602cbacf2b284be", "size_in_bytes": 7647}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/clean.cpython-311.pyc", "path_type": "hardlink", "sha256": "36619d8cc51dde3b6857e94c615ab6bcaa92a5cf73d3db21c265e77c709c74e7", "sha256_in_prefix": "36619d8cc51dde3b6857e94c615ab6bcaa92a5cf73d3db21c265e77c709c74e7", "size_in_bytes": 3150}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/config.cpython-311.pyc", "path_type": "hardlink", "sha256": "b12934cbbbc9381b6d8f89200a8da2a9cba8b803df7b0884203758899d3c513e", "sha256_in_prefix": "b12934cbbbc9381b6d8f89200a8da2a9cba8b803df7b0884203758899d3c513e", "size_in_bytes": 16514}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install.cpython-311.pyc", "path_type": "hardlink", "sha256": "c00af5e868ac91c30e8e32d40fc7e80dda6b5ad3cb63a1736374a1abd62ad0b2", "sha256_in_prefix": "c00af5e868ac91c30e8e32d40fc7e80dda6b5ad3cb63a1736374a1abd62ad0b2", "size_in_bytes": 29320}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_data.cpython-311.pyc", "path_type": "hardlink", "sha256": "8f6a249c498f7a846839120e951f341b738c7cbaaea3aac6f16550d9727b79dd", "sha256_in_prefix": "8f6a249c498f7a846839120e951f341b738c7cbaaea3aac6f16550d9727b79dd", "size_in_bytes": 4566}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_egg_info.cpython-311.pyc", "path_type": "hardlink", "sha256": "e195886d3558f0c73b66371d27b66de41d32f768b179e2e393f229a948b15e85", "sha256_in_prefix": "e195886d3558f0c73b66371d27b66de41d32f768b179e2e393f229a948b15e85", "size_in_bytes": 5182}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_headers.cpython-311.pyc", "path_type": "hardlink", "sha256": "3429784c2a86aad04adeab5079019dfb4149a276141dfd061e103c0e20ac11fc", "sha256_in_prefix": "3429784c2a86aad04adeab5079019dfb4149a276141dfd061e103c0e20ac11fc", "size_in_bytes": 2311}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_lib.cpython-311.pyc", "path_type": "hardlink", "sha256": "37300acf8e3598d408060e23670eddf48c9918ec82c3b5a75917cbb9dfb3e26e", "sha256_in_prefix": "37300acf8e3598d408060e23670eddf48c9918ec82c3b5a75917cbb9dfb3e26e", "size_in_bytes": 8744}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_scripts.cpython-311.pyc", "path_type": "hardlink", "sha256": "9843d5ebdaf30014d9028d1a9f425aa19c24693fd7483f1e36329ca6e034aee4", "sha256_in_prefix": "9843d5ebdaf30014d9028d1a9f425aa19c24693fd7483f1e36329ca6e034aee4", "size_in_bytes": 3118}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/sdist.cpython-311.pyc", "path_type": "hardlink", "sha256": "0fb19969c5de6e7c331fcf2fa2015917f50e37832fc45203c8b2664c3c942c55", "sha256_in_prefix": "0fb19969c5de6e7c331fcf2fa2015917f50e37832fc45203c8b2664c3c942c55", "size_in_bytes": 23595}, {"_path": "Lib/site-packages/setuptools/_distutils/command/_framework_compat.py", "path_type": "hardlink", "sha256": "d2265d4896331915820afcd10ca13e474fbfc9a018bc531dd729576f67985ee8", "sha256_in_prefix": "d2265d4896331915820afcd10ca13e474fbfc9a018bc531dd729576f67985ee8", "size_in_bytes": 1609}, {"_path": "Lib/site-packages/setuptools/_distutils/command/bdist.py", "path_type": "hardlink", "sha256": "9002e6ae1113d2944a952a98dc476f17e634cf3fa2254c78fe3a2b1fd31d69d9", "sha256_in_prefix": "9002e6ae1113d2944a952a98dc476f17e634cf3fa2254c78fe3a2b1fd31d69d9", "size_in_bytes": 5346}, {"_path": "Lib/site-packages/setuptools/_distutils/command/bdist_dumb.py", "path_type": "hardlink", "sha256": "1a2fddd4dcf897a1b3ff15382d17d1551281ae2db65a834f33bb98c97da4b1d9", "sha256_in_prefix": "1a2fddd4dcf897a1b3ff15382d17d1551281ae2db65a834f33bb98c97da4b1d9", "size_in_bytes": 4582}, {"_path": "Lib/site-packages/setuptools/_distutils/command/bdist_rpm.py", "path_type": "hardlink", "sha256": "0df660bcf9a6dcf4c0777f58ccb790f1f99bc9119a5e8fa79a7533604b5c720d", "sha256_in_prefix": "0df660bcf9a6dcf4c0777f58ccb790f1f99bc9119a5e8fa79a7533604b5c720d", "size_in_bytes": 21686}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build.py", "path_type": "hardlink", "sha256": "fe352a1b719628875d39e31592d3d4b55bf4e61481b7eff4ccbad3072bb7fea0", "sha256_in_prefix": "fe352a1b719628875d39e31592d3d4b55bf4e61481b7eff4ccbad3072bb7fea0", "size_in_bytes": 5729}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_clib.py", "path_type": "hardlink", "sha256": "a93a6fcfe0dbdb01232a45ff90fc184331540a83f91d9adc6cbbb81c6293274a", "sha256_in_prefix": "a93a6fcfe0dbdb01232a45ff90fc184331540a83f91d9adc6cbbb81c6293274a", "size_in_bytes": 7684}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_ext.py", "path_type": "hardlink", "sha256": "82ced3577300686e21cac3e4db88546bb33a8d99c9cc5862fe43086f03a760bb", "sha256_in_prefix": "82ced3577300686e21cac3e4db88546bb33a8d99c9cc5862fe43086f03a760bb", "size_in_bytes": 31758}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_py.py", "path_type": "hardlink", "sha256": "70d7bcbf0321c8f27680dea3a2df5c398e4eb943d4bd3ea3f5f205702f857229", "sha256_in_prefix": "70d7bcbf0321c8f27680dea3a2df5c398e4eb943d4bd3ea3f5f205702f857229", "size_in_bytes": 16552}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_scripts.py", "path_type": "hardlink", "sha256": "107095c8288ff403c4f57ef84a43968f801d4b98810ae7bb3eaa4b40ff3a7b56", "sha256_in_prefix": "107095c8288ff403c4f57ef84a43968f801d4b98810ae7bb3eaa4b40ff3a7b56", "size_in_bytes": 5534}, {"_path": "Lib/site-packages/setuptools/_distutils/command/check.py", "path_type": "hardlink", "sha256": "38cf7fa584b3eb699a2339772edcebb5343ae7c39943ddec3a5b3ce884e085a2", "sha256_in_prefix": "38cf7fa584b3eb699a2339772edcebb5343ae7c39943ddec3a5b3ce884e085a2", "size_in_bytes": 4897}, {"_path": "Lib/site-packages/setuptools/_distutils/command/clean.py", "path_type": "hardlink", "sha256": "aa52ad87be2358b66329ada7c4e6b2ff616e6ba315353ae80296903af6b67707", "sha256_in_prefix": "aa52ad87be2358b66329ada7c4e6b2ff616e6ba315353ae80296903af6b67707", "size_in_bytes": 2595}, {"_path": "Lib/site-packages/setuptools/_distutils/command/config.py", "path_type": "hardlink", "sha256": "14a776bd44953a9d2ba5551eaf86e3e83f78f9fcb1c85f072718ad46564573d7", "sha256_in_prefix": "14a776bd44953a9d2ba5551eaf86e3e83f78f9fcb1c85f072718ad46564573d7", "size_in_bytes": 13008}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install.py", "path_type": "hardlink", "sha256": "4c6fe56d36c58a6da662dd6532030d4c3f6b6ad6a0d0e275182b72b87a5eee8a", "sha256_in_prefix": "4c6fe56d36c58a6da662dd6532030d4c3f6b6ad6a0d0e275182b72b87a5eee8a", "size_in_bytes": 30078}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_data.py", "path_type": "hardlink", "sha256": "4eacdaa10f0f223eed4dcdb958a3d0f35699bcffdd4d8638a7daffb6ab5d9a0f", "sha256_in_prefix": "4eacdaa10f0f223eed4dcdb958a3d0f35699bcffdd4d8638a7daffb6ab5d9a0f", "size_in_bytes": 2816}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_egg_info.py", "path_type": "hardlink", "sha256": "4b2d9fb81aaf08695056125c52d7866dd1b4282c7604137b8c0cd610c03dae9f", "sha256_in_prefix": "4b2d9fb81aaf08695056125c52d7866dd1b4282c7604137b8c0cd610c03dae9f", "size_in_bytes": 2788}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_headers.py", "path_type": "hardlink", "sha256": "5a6a70da7a513541983d1d4101b46945a15b0c515ff8a282115cd41b212ecaf6", "sha256_in_prefix": "5a6a70da7a513541983d1d4101b46945a15b0c515ff8a282115cd41b212ecaf6", "size_in_bytes": 1184}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_lib.py", "path_type": "hardlink", "sha256": "a4a2cd135ae7a9da12a3c6eaf5e7d06d0b90a6b8394c6b30169bca91ad45dc58", "sha256_in_prefix": "a4a2cd135ae7a9da12a3c6eaf5e7d06d0b90a6b8394c6b30169bca91ad45dc58", "size_in_bytes": 8330}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_scripts.py", "path_type": "hardlink", "sha256": "430f0aac2db899c21e244bdc04d28848ca62ef99e94a6ea3cd6e813b303d1bb8", "sha256_in_prefix": "430f0aac2db899c21e244bdc04d28848ca62ef99e94a6ea3cd6e813b303d1bb8", "size_in_bytes": 1937}, {"_path": "Lib/site-packages/setuptools/_distutils/command/sdist.py", "path_type": "hardlink", "sha256": "e96973cf6ad101cba0d84bbbb8d384f443b76fa23642312572d0a5823c19e63f", "sha256_in_prefix": "e96973cf6ad101cba0d84bbbb8d384f443b76fa23642312572d0a5823c19e63f", "size_in_bytes": 18809}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__init__.py", "path_type": "hardlink", "sha256": "02131d8b70335fad8a62735d382704b8d5cbb813b186f392407b5d8e2e20f33f", "sha256_in_prefix": "02131d8b70335fad8a62735d382704b8d5cbb813b186f392407b5d8e2e20f33f", "size_in_bytes": 429}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "0b5dd2ed39fc855e570733a486e1f6228ca09600b20f0ebd3f311d66464e774f", "sha256_in_prefix": "0b5dd2ed39fc855e570733a486e1f6228ca09600b20f0ebd3f311d66464e774f", "size_in_bytes": 1311}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__pycache__/py38.cpython-311.pyc", "path_type": "hardlink", "sha256": "dccf78c7513bf7651dead9e6fc346cd6205738c78a988f2d74e01bcd1cee1ebd", "sha256_in_prefix": "dccf78c7513bf7651dead9e6fc346cd6205738c78a988f2d74e01bcd1cee1ebd", "size_in_bytes": 1575}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__pycache__/py39.cpython-311.pyc", "path_type": "hardlink", "sha256": "28d3a18659877d19cc7d6e0948b12fdab03a885fb995eb2fc74594bfa12442cb", "sha256_in_prefix": "28d3a18659877d19cc7d6e0948b12fdab03a885fb995eb2fc74594bfa12442cb", "size_in_bytes": 2997}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/py38.py", "path_type": "hardlink", "sha256": "427211152bc32a240d1a941e6d35ca982ff664bba61f4f23e73f32f3e274e153", "sha256_in_prefix": "427211152bc32a240d1a941e6d35ca982ff664bba61f4f23e73f32f3e274e153", "size_in_bytes": 791}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/py39.py", "path_type": "hardlink", "sha256": "84eb03ea5c192ea66832769c349dcfea7500f8b250844a55b584f3547d28f7a3", "sha256_in_prefix": "84eb03ea5c192ea66832769c349dcfea7500f8b250844a55b584f3547d28f7a3", "size_in_bytes": 1964}, {"_path": "Lib/site-packages/setuptools/_distutils/core.py", "path_type": "hardlink", "sha256": "fcc99978d21b928a56d0b747b47ef0dc748e23f5d3cd5853895f2701edd45b9c", "sha256_in_prefix": "fcc99978d21b928a56d0b747b47ef0dc748e23f5d3cd5853895f2701edd45b9c", "size_in_bytes": 9267}, {"_path": "Lib/site-packages/setuptools/_distutils/cygwinccompiler.py", "path_type": "hardlink", "sha256": "d4b40ed29f80c0348dccb264fca3c82a9eb67a20e99066787cc32cd8dde8f78c", "sha256_in_prefix": "d4b40ed29f80c0348dccb264fca3c82a9eb67a20e99066787cc32cd8dde8f78c", "size_in_bytes": 11891}, {"_path": "Lib/site-packages/setuptools/_distutils/debug.py", "path_type": "hardlink", "sha256": "37a32b4c0a8aea5f52564ead5b0791d74f0f33c3a5eea3657f257e9c770b86c6", "sha256_in_prefix": "37a32b4c0a8aea5f52564ead5b0791d74f0f33c3a5eea3657f257e9c770b86c6", "size_in_bytes": 139}, {"_path": "Lib/site-packages/setuptools/_distutils/dep_util.py", "path_type": "hardlink", "sha256": "c4def9a7a6691e13221c473eae92f65e29494329c79c336269f1ed79a678b635", "sha256_in_prefix": "c4def9a7a6691e13221c473eae92f65e29494329c79c336269f1ed79a678b635", "size_in_bytes": 349}, {"_path": "Lib/site-packages/setuptools/_distutils/dir_util.py", "path_type": "hardlink", "sha256": "cf0c34b77d667dd5d5ec5d6801916575d4b6e622a581959a34d30b28bf33125c", "sha256_in_prefix": "cf0c34b77d667dd5d5ec5d6801916575d4b6e622a581959a34d30b28bf33125c", "size_in_bytes": 7407}, {"_path": "Lib/site-packages/setuptools/_distutils/dist.py", "path_type": "hardlink", "sha256": "679d7e17289ba2e2765e2d8a555f4619bd4eb9f43221d496811f1060b2872ee6", "sha256_in_prefix": "679d7e17289ba2e2765e2d8a555f4619bd4eb9f43221d496811f1060b2872ee6", "size_in_bytes": 50553}, {"_path": "Lib/site-packages/setuptools/_distutils/errors.py", "path_type": "hardlink", "sha256": "6d9ddc2f5629998547258120c3c50cf2f96c2cc2297805ea8ba203495f58aa1c", "sha256_in_prefix": "6d9ddc2f5629998547258120c3c50cf2f96c2cc2297805ea8ba203495f58aa1c", "size_in_bytes": 3325}, {"_path": "Lib/site-packages/setuptools/_distutils/extension.py", "path_type": "hardlink", "sha256": "d922c9f2fcd8667fc73c400852a13121d276db24b1958a0411e8dbacca010527", "sha256_in_prefix": "d922c9f2fcd8667fc73c400852a13121d276db24b1958a0411e8dbacca010527", "size_in_bytes": 10358}, {"_path": "Lib/site-packages/setuptools/_distutils/fancy_getopt.py", "path_type": "hardlink", "sha256": "15f04a8dfcc05cec20d70248543048a1381a96cf7e5cba50762f986d94bcd89c", "sha256_in_prefix": "15f04a8dfcc05cec20d70248543048a1381a96cf7e5cba50762f986d94bcd89c", "size_in_bytes": 17822}, {"_path": "Lib/site-packages/setuptools/_distutils/file_util.py", "path_type": "hardlink", "sha256": "8e93c2760a4437cefa4c549610a3f311b8f8859ac04e964a3d00ce4f81bec874", "sha256_in_prefix": "8e93c2760a4437cefa4c549610a3f311b8f8859ac04e964a3d00ce4f81bec874", "size_in_bytes": 7962}, {"_path": "Lib/site-packages/setuptools/_distutils/filelist.py", "path_type": "hardlink", "sha256": "3e37957e9bef8d8d34b0ec287dde1defd1148e623f73bb9d78f08be9111b6333", "sha256_in_prefix": "3e37957e9bef8d8d34b0ec287dde1defd1148e623f73bb9d78f08be9111b6333", "size_in_bytes": 13654}, {"_path": "Lib/site-packages/setuptools/_distutils/log.py", "path_type": "hardlink", "sha256": "57206ce63ef3e3e2ba5d310405385473d1f2329a0f2c6b50a4446a6f3e72970c", "sha256_in_prefix": "57206ce63ef3e3e2ba5d310405385473d1f2329a0f2c6b50a4446a6f3e72970c", "size_in_bytes": 1200}, {"_path": "Lib/site-packages/setuptools/_distutils/spawn.py", "path_type": "hardlink", "sha256": "bb9b2b15c5680713b0785956b594633bd2fffed45c390bcb1fc0c07a5e646528", "sha256_in_prefix": "bb9b2b15c5680713b0785956b594633bd2fffed45c390bcb1fc0c07a5e646528", "size_in_bytes": 3625}, {"_path": "Lib/site-packages/setuptools/_distutils/sysconfig.py", "path_type": "hardlink", "sha256": "d2b067ae420c25ce1e93fe6c7da34e3800a947d6d7394f46c4300adca2c7908b", "sha256_in_prefix": "d2b067ae420c25ce1e93fe6c7da34e3800a947d6d7394f46c4300adca2c7908b", "size_in_bytes": 19235}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__init__.py", "path_type": "hardlink", "sha256": "6c4f6a4622602c99df3cb1c8536d6e63bb9760270763d771f95d31d6f353ffe3", "sha256_in_prefix": "6c4f6a4622602c99df3cb1c8536d6e63bb9760270763d771f95d31d6f353ffe3", "size_in_bytes": 1476}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "5bf42da7c0c59a68c9394c528b2c37a5d948ab8008da319d18071ceb5c9732e5", "sha256_in_prefix": "5bf42da7c0c59a68c9394c528b2c37a5d948ab8008da319d18071ceb5c9732e5", "size_in_bytes": 2041}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/support.cpython-311.pyc", "path_type": "hardlink", "sha256": "5b3b9c7216f517672ab3adfd219e1d33f75ed00d7f95810b4def27320551af85", "sha256_in_prefix": "5b3b9c7216f517672ab3adfd219e1d33f75ed00d7f95810b4def27320551af85", "size_in_bytes": 7100}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_archive_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "72af114704b211a6c482dc95d2b0c074449fd0c657b3c2fe488735747ed2c31a", "sha256_in_prefix": "72af114704b211a6c482dc95d2b0c074449fd0c657b3c2fe488735747ed2c31a", "size_in_bytes": 23356}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist.cpython-311.pyc", "path_type": "hardlink", "sha256": "3b46f904fa0c5df9060d8709ef5db96a305ffcde1fa903cf4ee64b3c8f1e3266", "sha256_in_prefix": "3b46f904fa0c5df9060d8709ef5db96a305ffcde1fa903cf4ee64b3c8f1e3266", "size_in_bytes": 2096}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_dumb.cpython-311.pyc", "path_type": "hardlink", "sha256": "2abca0ed58506f3cc3f20d1bea30aa022a625b4db041000d0808b5ac5744b305", "sha256_in_prefix": "2abca0ed58506f3cc3f20d1bea30aa022a625b4db041000d0808b5ac5744b305", "size_in_bytes": 4139}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_rpm.cpython-311.pyc", "path_type": "hardlink", "sha256": "655ce1f4abc105ea5527b1f69b938a6a55e6a47acc6b95bfb6903c06d7848385", "sha256_in_prefix": "655ce1f4abc105ea5527b1f69b938a6a55e6a47acc6b95bfb6903c06d7848385", "size_in_bytes": 6143}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build.cpython-311.pyc", "path_type": "hardlink", "sha256": "574cbd1cf3cfbd0c5eb04f0262320158ccd90c41c6fa607dd89f5b1c6da9e344", "sha256_in_prefix": "574cbd1cf3cfbd0c5eb04f0262320158ccd90c41c6fa607dd89f5b1c6da9e344", "size_in_bytes": 2724}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_clib.cpython-311.pyc", "path_type": "hardlink", "sha256": "96cbc445e9098b93dc78bca73059971d65ebd76986f8cc1149478912fe741bc9", "sha256_in_prefix": "96cbc445e9098b93dc78bca73059971d65ebd76986f8cc1149478912fe741bc9", "size_in_bytes": 8049}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_ext.cpython-311.pyc", "path_type": "hardlink", "sha256": "2be3dab614a4b57913ddd3474cd8222ccad23686880631f08c32bfedd70db8ce", "sha256_in_prefix": "2be3dab614a4b57913ddd3474cd8222ccad23686880631f08c32bfedd70db8ce", "size_in_bytes": 30030}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_py.cpython-311.pyc", "path_type": "hardlink", "sha256": "e4ef57946b854c66f6e17b72a5b5a5004bc14c8bcd1a165e3b0113c23337b404", "sha256_in_prefix": "e4ef57946b854c66f6e17b72a5b5a5004bc14c8bcd1a165e3b0113c23337b404", "size_in_bytes": 10652}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_scripts.cpython-311.pyc", "path_type": "hardlink", "sha256": "c8586a6ff263975fa5b1c0153c593bbb60704a165755b337b17ad4a3eca0a3c7", "sha256_in_prefix": "c8586a6ff263975fa5b1c0153c593bbb60704a165755b337b17ad4a3eca0a3c7", "size_in_bytes": 5322}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_ccompiler.cpython-311.pyc", "path_type": "hardlink", "sha256": "adcf65d43fb7fc15cd9534f382855e1a2ce449e0dcfef59d94a888f5a08c3287", "sha256_in_prefix": "adcf65d43fb7fc15cd9534f382855e1a2ce449e0dcfef59d94a888f5a08c3287", "size_in_bytes": 5162}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_check.cpython-311.pyc", "path_type": "hardlink", "sha256": "18e3e2a6fb9299544a589346600901469b00968de456520bc5344b5c75730e86", "sha256_in_prefix": "18e3e2a6fb9299544a589346600901469b00968de456520bc5344b5c75730e86", "size_in_bytes": 7979}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_clean.cpython-311.pyc", "path_type": "hardlink", "sha256": "4095b34930544a1b0453f9ba9b16aece6c8c4036ef69f97a5777c6e200bf448c", "sha256_in_prefix": "4095b34930544a1b0453f9ba9b16aece6c8c4036ef69f97a5777c6e200bf448c", "size_in_bytes": 2304}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_cmd.cpython-311.pyc", "path_type": "hardlink", "sha256": "62c42a2293e3a5d23cb27b2ce1d6b0c981baa8f091fdce180f968463aa03a64c", "sha256_in_prefix": "62c42a2293e3a5d23cb27b2ce1d6b0c981baa8f091fdce180f968463aa03a64c", "size_in_bytes": 7666}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_config_cmd.cpython-311.pyc", "path_type": "hardlink", "sha256": "c49239d4ddf0652a27db407299ae7513caf36562a286bfd10e11b3bec994b7c3", "sha256_in_prefix": "c49239d4ddf0652a27db407299ae7513caf36562a286bfd10e11b3bec994b7c3", "size_in_bytes": 5655}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_core.cpython-311.pyc", "path_type": "hardlink", "sha256": "813e280fa11a0553ffe01540be41cbc182f3de3f4e8a07146a2a086f55fb35af", "sha256_in_prefix": "813e280fa11a0553ffe01540be41cbc182f3de3f4e8a07146a2a086f55fb35af", "size_in_bytes": 6606}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_cygwinccompiler.cpython-311.pyc", "path_type": "hardlink", "sha256": "3b62b13c527ade83b4ed1c9f263a4f3f825f808d0ce815d4fee60433cb72f658", "sha256_in_prefix": "3b62b13c527ade83b4ed1c9f263a4f3f825f808d0ce815d4fee60433cb72f658", "size_in_bytes": 4854}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dir_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "b277807fff726f5451a0a54dd97e68d8511f6c92ecb235143b131eda1f7b2f1f", "sha256_in_prefix": "b277807fff726f5451a0a54dd97e68d8511f6c92ecb235143b131eda1f7b2f1f", "size_in_bytes": 7629}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dist.cpython-311.pyc", "path_type": "hardlink", "sha256": "788d973b53373c7586ca810eca5f9449b38847022705e3de010d974edba591bc", "sha256_in_prefix": "788d973b53373c7586ca810eca5f9449b38847022705e3de010d974edba591bc", "size_in_bytes": 29692}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_extension.cpython-311.pyc", "path_type": "hardlink", "sha256": "7d09cd2992d5bfbe9521bd2497bf3689e932ccce9922f79ab3fc3cf7c856859b", "sha256_in_prefix": "7d09cd2992d5bfbe9521bd2497bf3689e932ccce9922f79ab3fc3cf7c856859b", "size_in_bytes": 4344}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_file_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "10058b5e2aa2b7fbc6249dbfd8d874459e35172ee12098dc8fc77928c41d33c5", "sha256_in_prefix": "10058b5e2aa2b7fbc6249dbfd8d874459e35172ee12098dc8fc77928c41d33c5", "size_in_bytes": 7556}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_filelist.cpython-311.pyc", "path_type": "hardlink", "sha256": "fb7c210b6c4662349e94eb1582b1844318aeb44afb1b4b5ec986d081683dde6b", "sha256_in_prefix": "fb7c210b6c4662349e94eb1582b1844318aeb44afb1b4b5ec986d081683dde6b", "size_in_bytes": 16543}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install.cpython-311.pyc", "path_type": "hardlink", "sha256": "634043f03772965feff9e2774ba731a231ce11bedf456efd1991cb6c0f78ce0d", "sha256_in_prefix": "634043f03772965feff9e2774ba731a231ce11bedf456efd1991cb6c0f78ce0d", "size_in_bytes": 15461}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_data.cpython-311.pyc", "path_type": "hardlink", "sha256": "9a05cd9eb677648e647a5de9e26c6f890b55cb57cf2dfa99c0ee8ddc5041ef9e", "sha256_in_prefix": "9a05cd9eb677648e647a5de9e26c6f890b55cb57cf2dfa99c0ee8ddc5041ef9e", "size_in_bytes": 5191}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_headers.cpython-311.pyc", "path_type": "hardlink", "sha256": "6afccd3ba138f792049fe48ee4873e380b3d62c93704a63afa2a64a73a64769a", "sha256_in_prefix": "6afccd3ba138f792049fe48ee4873e380b3d62c93704a63afa2a64a73a64769a", "size_in_bytes": 2093}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_lib.cpython-311.pyc", "path_type": "hardlink", "sha256": "41664908785d5596c6d61a1d855f6128759b879187383df716312fff50672808", "sha256_in_prefix": "41664908785d5596c6d61a1d855f6128759b879187383df716312fff50672808", "size_in_bytes": 6770}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_scripts.cpython-311.pyc", "path_type": "hardlink", "sha256": "7dc7b1e3b74b204c97f4c1f79a82f21e30236a61032d62f81613584f44c5c2f3", "sha256_in_prefix": "7dc7b1e3b74b204c97f4c1f79a82f21e30236a61032d62f81613584f44c5c2f3", "size_in_bytes": 2813}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_log.cpython-311.pyc", "path_type": "hardlink", "sha256": "5eb67e5a729b4a5938119ff9b9189396216d1a3c667842c6c77c0a74daf03c80", "sha256_in_prefix": "5eb67e5a729b4a5938119ff9b9189396216d1a3c667842c6c77c0a74daf03c80", "size_in_bytes": 1035}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_mingwccompiler.cpython-311.pyc", "path_type": "hardlink", "sha256": "51ad8787ba3aebe6a6970d0ca59cfc54499fd5e076288b28deee16454b6c53aa", "sha256_in_prefix": "51ad8787ba3aebe6a6970d0ca59cfc54499fd5e076288b28deee16454b6c53aa", "size_in_bytes": 4510}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_modified.cpython-311.pyc", "path_type": "hardlink", "sha256": "363d70496f9bed6fa084e08336e53a0522d222f7027e46a0383588a904651ccf", "sha256_in_prefix": "363d70496f9bed6fa084e08336e53a0522d222f7027e46a0383588a904651ccf", "size_in_bytes": 8389}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_msvccompiler.cpython-311.pyc", "path_type": "hardlink", "sha256": "d82c1c7caa8aecd8206c31cb89b90b26c46e2790472ddb47a3f727451f0f6841", "sha256_in_prefix": "d82c1c7caa8aecd8206c31cb89b90b26c46e2790472ddb47a3f727451f0f6841", "size_in_bytes": 8667}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sdist.cpython-311.pyc", "path_type": "hardlink", "sha256": "570bfcf7d593e7b6d00e2dd9bd6efd94ebac5182613dd0bd31dcd348ffff7cf4", "sha256_in_prefix": "570bfcf7d593e7b6d00e2dd9bd6efd94ebac5182613dd0bd31dcd348ffff7cf4", "size_in_bytes": 23471}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_spawn.cpython-311.pyc", "path_type": "hardlink", "sha256": "06a4bc17c68a4775e938efe84189db6aa588e44e5a87f7c3fbfce5810f4cadc5", "sha256_in_prefix": "06a4bc17c68a4775e938efe84189db6aa588e44e5a87f7c3fbfce5810f4cadc5", "size_in_bytes": 8624}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sysconfig.cpython-311.pyc", "path_type": "hardlink", "sha256": "d6fc0fd91e48fd3ddec91f61cd5ab2656c3db91ba85e724ff0ed95c731b9fa98", "sha256_in_prefix": "d6fc0fd91e48fd3ddec91f61cd5ab2656c3db91ba85e724ff0ed95c731b9fa98", "size_in_bytes": 19447}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_text_file.cpython-311.pyc", "path_type": "hardlink", "sha256": "97f305eee59c5b2bdb5260a349262d1d7dea3326c0111e3a514bc7bf755b51a5", "sha256_in_prefix": "97f305eee59c5b2bdb5260a349262d1d7dea3326c0111e3a514bc7bf755b51a5", "size_in_bytes": 3844}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_unixccompiler.cpython-311.pyc", "path_type": "hardlink", "sha256": "0a56fdf0ab907a83abcb412c5a84aed6d8f3521a7279a4772a94056115d3928d", "sha256_in_prefix": "0a56fdf0ab907a83abcb412c5a84aed6d8f3521a7279a4772a94056115d3928d", "size_in_bytes": 17716}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "f25ed142b02aa3bdc00d2c7bc1a4c1b3e510e281999111d1701eb25f0bdda9e0", "sha256_in_prefix": "f25ed142b02aa3bdc00d2c7bc1a4c1b3e510e281999111d1701eb25f0bdda9e0", "size_in_bytes": 15797}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_version.cpython-311.pyc", "path_type": "hardlink", "sha256": "851a5e2ad20de72f53a9982d386e80090f760e338ce44df9bea38c79ff706381", "sha256_in_prefix": "851a5e2ad20de72f53a9982d386e80090f760e338ce44df9bea38c79ff706381", "size_in_bytes": 4477}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_versionpredicate.cpython-311.pyc", "path_type": "hardlink", "sha256": "3ca15f4d988f33b44ddca8102c115802e0072d76f976d11a79626f225f53b0e3", "sha256_in_prefix": "3ca15f4d988f33b44ddca8102c115802e0072d76f976d11a79626f225f53b0e3", "size_in_bytes": 179}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/unix_compat.cpython-311.pyc", "path_type": "hardlink", "sha256": "261802ff5940956a37ab2beb2209033babe879c63e1a018fd0e6754a1daa7d58", "sha256_in_prefix": "261802ff5940956a37ab2beb2209033babe879c63e1a018fd0e6754a1daa7d58", "size_in_bytes": 770}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "ced0555d785c73cb160efa7a76e45b7f6f732651170ad92e5b6f0de3b58190ba", "sha256_in_prefix": "ced0555d785c73cb160efa7a76e45b7f6f732651170ad92e5b6f0de3b58190ba", "size_in_bytes": 173}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/py38.cpython-311.pyc", "path_type": "hardlink", "sha256": "3756ebd6abb860523c0a1ff43beb8aca9a75946bd7a66ecc94df4500bb5f0dfd", "sha256_in_prefix": "3756ebd6abb860523c0a1ff43beb8aca9a75946bd7a66ecc94df4500bb5f0dfd", "size_in_bytes": 1568}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/py38.py", "path_type": "hardlink", "sha256": "9b24a14132e5f839f202344326b3b8e0774b684a0590a21e45e1e2c9d0a21e0e", "sha256_in_prefix": "9b24a14132e5f839f202344326b3b8e0774b684a0590a21e45e1e2c9d0a21e0e", "size_in_bytes": 1015}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/support.py", "path_type": "hardlink", "sha256": "b63b18b32c6fa532b836b902b1e876ba3bc320657431ffdbe522397cfd93d323", "sha256_in_prefix": "b63b18b32c6fa532b836b902b1e876ba3bc320657431ffdbe522397cfd93d323", "size_in_bytes": 4099}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_archive_util.py", "path_type": "hardlink", "sha256": "8e8ce2992c0f045f89a097cdfef0da895199a7ae8135c5991a1df81655b9ec34", "sha256_in_prefix": "8e8ce2992c0f045f89a097cdfef0da895199a7ae8135c5991a1df81655b9ec34", "size_in_bytes": 11787}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_bdist.py", "path_type": "hardlink", "sha256": "c4d1f152c2e51ec6504709332dbfe2483db8b3ef4c93e357d9f7c15b03b23f27", "sha256_in_prefix": "c4d1f152c2e51ec6504709332dbfe2483db8b3ef4c93e357d9f7c15b03b23f27", "size_in_bytes": 1396}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_bdist_dumb.py", "path_type": "hardlink", "sha256": "405d393073613ce759ca1f3c5e9c3c2ac3bae2cee9445925f0a2fe4685785cad", "sha256_in_prefix": "405d393073613ce759ca1f3c5e9c3c2ac3bae2cee9445925f0a2fe4685785cad", "size_in_bytes": 2247}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_bdist_rpm.py", "path_type": "hardlink", "sha256": "606bfde38d890b82b7321fdfd7163ac71dd71597b174890d763342842ebf15ee", "sha256_in_prefix": "606bfde38d890b82b7321fdfd7163ac71dd71597b174890d763342842ebf15ee", "size_in_bytes": 3933}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build.py", "path_type": "hardlink", "sha256": "2dfeba48ef568bf7b1cca82c104e56a553e074d60716bd62bce84a4368310b5a", "sha256_in_prefix": "2dfeba48ef568bf7b1cca82c104e56a553e074d60716bd62bce84a4368310b5a", "size_in_bytes": 1698}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_clib.py", "path_type": "hardlink", "sha256": "328d5915be02d555c160e1af9da965c0ded80a74edaf6e1a90b0cef198b80ac6", "sha256_in_prefix": "328d5915be02d555c160e1af9da965c0ded80a74edaf6e1a90b0cef198b80ac6", "size_in_bytes": 4331}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_ext.py", "path_type": "hardlink", "sha256": "872c4e7875e9eac0dbf021f1686911e3efbe9cfe67c1b3edafd268009713f585", "sha256_in_prefix": "872c4e7875e9eac0dbf021f1686911e3efbe9cfe67c1b3edafd268009713f585", "size_in_bytes": 19961}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_py.py", "path_type": "hardlink", "sha256": "36c7e646ba2338705734ca9647f9a9e60e0f2d3823843ee264551f7c664521dc", "sha256_in_prefix": "36c7e646ba2338705734ca9647f9a9e60e0f2d3823843ee264551f7c664521dc", "size_in_bytes": 6882}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_scripts.py", "path_type": "hardlink", "sha256": "703f85472fa85f9e6c5d15f9133e7140269e1eb59a8f229ce17bb0bf67dee3cc", "sha256_in_prefix": "703f85472fa85f9e6c5d15f9133e7140269e1eb59a8f229ce17bb0bf67dee3cc", "size_in_bytes": 2880}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_ccompiler.py", "path_type": "hardlink", "sha256": "795cd9644f09308725e0e8f08ae85d48da4d74a92700f3a79487b60e7157fb7f", "sha256_in_prefix": "795cd9644f09308725e0e8f08ae85d48da4d74a92700f3a79487b60e7157fb7f", "size_in_bytes": 2964}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_check.py", "path_type": "hardlink", "sha256": "847495d3ba9fed8a12c46b136dbb1443db6cb19cf945135d6eb635b364b06852", "sha256_in_prefix": "847495d3ba9fed8a12c46b136dbb1443db6cb19cf945135d6eb635b364b06852", "size_in_bytes": 6226}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_clean.py", "path_type": "hardlink", "sha256": "84f1fa8df22918552bbd66c5d6dc6488d55235a031b76c2ae578d5e3df733b81", "sha256_in_prefix": "84f1fa8df22918552bbd66c5d6dc6488d55235a031b76c2ae578d5e3df733b81", "size_in_bytes": 1240}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_cmd.py", "path_type": "hardlink", "sha256": "6e0441efd9a2b6838a4753a2c991e70a882f1b1b77a56931793a880b4e254164", "sha256_in_prefix": "6e0441efd9a2b6838a4753a2c991e70a882f1b1b77a56931793a880b4e254164", "size_in_bytes": 3254}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_config_cmd.py", "path_type": "hardlink", "sha256": "66ce965f421fc43be6b82d7d5f3b953676029d3afd63e865ef74c09834813786", "sha256_in_prefix": "66ce965f421fc43be6b82d7d5f3b953676029d3afd63e865ef74c09834813786", "size_in_bytes": 2664}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_core.py", "path_type": "hardlink", "sha256": "2fb5ca540c5af8c1a8019780368a67b8af5f44a9de621912429830f1742f705f", "sha256_in_prefix": "2fb5ca540c5af8c1a8019780368a67b8af5f44a9de621912429830f1742f705f", "size_in_bytes": 3829}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_cygwinccompiler.py", "path_type": "hardlink", "sha256": "8aac6c0f2d19e594d183133c011ccf5da922b50a1dd95f1a1b9a9eb7f279b538", "sha256_in_prefix": "8aac6c0f2d19e594d183133c011ccf5da922b50a1dd95f1a1b9a9eb7f279b538", "size_in_bytes": 2753}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_dir_util.py", "path_type": "hardlink", "sha256": "21d75753bf04a093f11e9979c10d936e5491d1ea2801855c211aaa80003c4cb6", "sha256_in_prefix": "21d75753bf04a093f11e9979c10d936e5491d1ea2801855c211aaa80003c4cb6", "size_in_bytes": 3822}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_dist.py", "path_type": "hardlink", "sha256": "085941a5b337c091f65a3b20086b135789f96770697cb4a59fa762c41c5eaa93", "sha256_in_prefix": "085941a5b337c091f65a3b20086b135789f96770697cb4a59fa762c41c5eaa93", "size_in_bytes": 18459}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_extension.py", "path_type": "hardlink", "sha256": "48b2589cb86c85f8f8bbbd90f7513fe639f35696cf963bfaff1a95ef2281d43b", "sha256_in_prefix": "48b2589cb86c85f8f8bbbd90f7513fe639f35696cf963bfaff1a95ef2281d43b", "size_in_bytes": 3094}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_file_util.py", "path_type": "hardlink", "sha256": "af730d0be8370593ca45974718ca926ad33ddc3fdaa284c7067f959b82221035", "sha256_in_prefix": "af730d0be8370593ca45974718ca926ad33ddc3fdaa284c7067f959b82221035", "size_in_bytes": 3502}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_filelist.py", "path_type": "hardlink", "sha256": "0a8f1d0d6082e4afc3530e53e93432801b21fcf4150a83a561039cb25d9a8077", "sha256_in_prefix": "0a8f1d0d6082e4afc3530e53e93432801b21fcf4150a83a561039cb25d9a8077", "size_in_bytes": 10766}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install.py", "path_type": "hardlink", "sha256": "4df081d32921231c9d202d90e12b93019cd21efb5e30782b04bf708684a02bd4", "sha256_in_prefix": "4df081d32921231c9d202d90e12b93019cd21efb5e30782b04bf708684a02bd4", "size_in_bytes": 8618}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_data.py", "path_type": "hardlink", "sha256": "bcaab72bdee4d210409ce837f279b011d7fb7040d5afdad357209e2689606f80", "sha256_in_prefix": "bcaab72bdee4d210409ce837f279b011d7fb7040d5afdad357209e2689606f80", "size_in_bytes": 2464}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_headers.py", "path_type": "hardlink", "sha256": "3d5018a68fed625f7cd107fae033ce9a64afc9e7c81dd534e9fed5b09799ca41", "sha256_in_prefix": "3d5018a68fed625f7cd107fae033ce9a64afc9e7c81dd534e9fed5b09799ca41", "size_in_bytes": 936}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_lib.py", "path_type": "hardlink", "sha256": "aab8ba465fa668d4d0acd0d5f036de5cd974863b1f4482a2238adf64bae65812", "sha256_in_prefix": "aab8ba465fa668d4d0acd0d5f036de5cd974863b1f4482a2238adf64bae65812", "size_in_bytes": 3612}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_scripts.py", "path_type": "hardlink", "sha256": "284defd1c0e4156fbdd083880fe3a665918cda6872f99904bae5bb5174b6487c", "sha256_in_prefix": "284defd1c0e4156fbdd083880fe3a665918cda6872f99904bae5bb5174b6487c", "size_in_bytes": 1600}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_log.py", "path_type": "hardlink", "sha256": "8ac16d3ae7e5a02c84759690395edc554af8e86c2d755323e37986041e571fb9", "sha256_in_prefix": "8ac16d3ae7e5a02c84759690395edc554af8e86c2d755323e37986041e571fb9", "size_in_bytes": 323}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_mingwccompiler.py", "path_type": "hardlink", "sha256": "98197c5bc4083b6c72e1e3a3e9a0045689b89686f0a4733e1ef154217bbaab47", "sha256_in_prefix": "98197c5bc4083b6c72e1e3a3e9a0045689b89686f0a4733e1ef154217bbaab47", "size_in_bytes": 2202}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_modified.py", "path_type": "hardlink", "sha256": "875fbe6ce5a6b49a356e9555eae4617674bd6ebef508188d0ccd4c0f0486a6e8", "sha256_in_prefix": "875fbe6ce5a6b49a356e9555eae4617674bd6ebef508188d0ccd4c0f0486a6e8", "size_in_bytes": 4221}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_msvccompiler.py", "path_type": "hardlink", "sha256": "c54adfc82c023b9ec312cc5ca0beacf981b760865196562c2ae6a065b04f149d", "sha256_in_prefix": "c54adfc82c023b9ec312cc5ca0beacf981b760865196562c2ae6a065b04f149d", "size_in_bytes": 4301}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_sdist.py", "path_type": "hardlink", "sha256": "227b1b534f5a795749b63f10cb04449d466e577d9bbe2e3b791987de2590c249", "sha256_in_prefix": "227b1b534f5a795749b63f10cb04449d466e577d9bbe2e3b791987de2590c249", "size_in_bytes": 15058}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_spawn.py", "path_type": "hardlink", "sha256": "6f19384663561660a7c98a4096ab46f157d77e4e53773723579de53b1172ca1e", "sha256_in_prefix": "6f19384663561660a7c98a4096ab46f157d77e4e53773723579de53b1172ca1e", "size_in_bytes": 4613}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_sysconfig.py", "path_type": "hardlink", "sha256": "887e18f44f141eb7e5dcff954add256e024c947ba842c20ea2bc6bb154509c4f", "sha256_in_prefix": "887e18f44f141eb7e5dcff954add256e024c947ba842c20ea2bc6bb154509c4f", "size_in_bytes": 12010}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_text_file.py", "path_type": "hardlink", "sha256": "59059207901f7410d968c03c045822a493e7b096ffd9228c7cbf747d291156dc", "sha256_in_prefix": "59059207901f7410d968c03c045822a493e7b096ffd9228c7cbf747d291156dc", "size_in_bytes": 3460}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_unixccompiler.py", "path_type": "hardlink", "sha256": "c1c2502615ed914504dc8eb84f20ef337628ec6f5ad2e83f329ec36d92f04f84", "sha256_in_prefix": "c1c2502615ed914504dc8eb84f20ef337628ec6f5ad2e83f329ec36d92f04f84", "size_in_bytes": 11840}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_util.py", "path_type": "hardlink", "sha256": "1fdce5678cf8561e137e33580c1b313fbc20b902e9c427c963239c9b5c995377", "sha256_in_prefix": "1fdce5678cf8561e137e33580c1b313fbc20b902e9c427c963239c9b5c995377", "size_in_bytes": 7988}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_version.py", "path_type": "hardlink", "sha256": "6f450c74c441a1fcb2480854627ce2621226741dc2cec53bebde9baca0cfd173", "sha256_in_prefix": "6f450c74c441a1fcb2480854627ce2621226741dc2cec53bebde9baca0cfd173", "size_in_bytes": 2750}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_versionpredicate.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/unix_compat.py", "path_type": "hardlink", "sha256": "cfea29e82da255d5f56aae4120147b72a3b18a3284f7b6a537026a1cd74ba682", "sha256_in_prefix": "cfea29e82da255d5f56aae4120147b72a3b18a3284f7b6a537026a1cd74ba682", "size_in_bytes": 386}, {"_path": "Lib/site-packages/setuptools/_distutils/text_file.py", "path_type": "hardlink", "sha256": "115f210c27ad61e2aae9cfee3dfc06824530f21ba0784a1225c5e9cbf124406a", "sha256_in_prefix": "115f210c27ad61e2aae9cfee3dfc06824530f21ba0784a1225c5e9cbf124406a", "size_in_bytes": 12098}, {"_path": "Lib/site-packages/setuptools/_distutils/unixccompiler.py", "path_type": "hardlink", "sha256": "4091cd71088cb5670e4385b3ba9cc8bf59d0c0110da3e6cd91e542495993e099", "sha256_in_prefix": "4091cd71088cb5670e4385b3ba9cc8bf59d0c0110da3e6cd91e542495993e099", "size_in_bytes": 15437}, {"_path": "Lib/site-packages/setuptools/_distutils/util.py", "path_type": "hardlink", "sha256": "c855c29d8a09acbba6f82a2e63c43e6b154c9da952142d50bec48aa51a5801c5", "sha256_in_prefix": "c855c29d8a09acbba6f82a2e63c43e6b154c9da952142d50bec48aa51a5801c5", "size_in_bytes": 17654}, {"_path": "Lib/site-packages/setuptools/_distutils/version.py", "path_type": "hardlink", "sha256": "2a56c38c0bc60726d526a443c4d2cd32f64b9795cbd853f47695638337e6d336", "sha256_in_prefix": "2a56c38c0bc60726d526a443c4d2cd32f64b9795cbd853f47695638337e6d336", "size_in_bytes": 12634}, {"_path": "Lib/site-packages/setuptools/_distutils/versionpredicate.py", "path_type": "hardlink", "sha256": "a81590eb04e3d76383cada13988c9d79f218da36f8b98d6c75b81bb8b9fe2093", "sha256_in_prefix": "a81590eb04e3d76383cada13988c9d79f218da36f8b98d6c75b81bb8b9fe2093", "size_in_bytes": 5205}, {"_path": "Lib/site-packages/setuptools/_distutils/zosccompiler.py", "path_type": "hardlink", "sha256": "6dbd9d4281a7b2fe0b9a84017e3843b1a3a9b7fa7947bcbfdbc975725b661bde", "sha256_in_prefix": "6dbd9d4281a7b2fe0b9a84017e3843b1a3a9b7fa7947bcbfdbc975725b661bde", "size_in_bytes": 6589}, {"_path": "Lib/site-packages/setuptools/_entry_points.py", "path_type": "hardlink", "sha256": "63741413d24a156fd8caab839e97df3564ace9fde3284b757be767c7efbdf8ac", "sha256_in_prefix": "63741413d24a156fd8caab839e97df3564ace9fde3284b757be767c7efbdf8ac", "size_in_bytes": 2310}, {"_path": "Lib/site-packages/setuptools/_imp.py", "path_type": "hardlink", "sha256": "6f9b04cfe10e24a02932c99bfb224f3e0906a9905e64578bb685a1cac2eb7bed", "sha256_in_prefix": "6f9b04cfe10e24a02932c99bfb224f3e0906a9905e64578bb685a1cac2eb7bed", "size_in_bytes": 2441}, {"_path": "Lib/site-packages/setuptools/_importlib.py", "path_type": "hardlink", "sha256": "aeb79b8ff62ebd379533e03780524ca7c9518120260f67c0f8d3d756cc73b79c", "sha256_in_prefix": "aeb79b8ff62ebd379533e03780524ca7c9518120260f67c0f8d3d756cc73b79c", "size_in_bytes": 327}, {"_path": "Lib/site-packages/setuptools/_itertools.py", "path_type": "hardlink", "sha256": "8d645fb08ae90bb9b2a28cf78435118fd1adbe9b3065e2978361da926121363a", "sha256_in_prefix": "8d645fb08ae90bb9b2a28cf78435118fd1adbe9b3065e2978361da926121363a", "size_in_bytes": 657}, {"_path": "Lib/site-packages/setuptools/_normalization.py", "path_type": "hardlink", "sha256": "b7e49e5dcd23536c1e418f41037a869514e1cc1343d6860ae47a73835ff9df78", "sha256_in_prefix": "b7e49e5dcd23536c1e418f41037a869514e1cc1343d6860ae47a73835ff9df78", "size_in_bytes": 4536}, {"_path": "Lib/site-packages/setuptools/_path.py", "path_type": "hardlink", "sha256": "723ae776cf9609f0200583c787616c9d9176f78a5c2909c98956bb567a80e3f2", "sha256_in_prefix": "723ae776cf9609f0200583c787616c9d9176f78a5c2909c98956bb567a80e3f2", "size_in_bytes": 2700}, {"_path": "Lib/site-packages/setuptools/_reqs.py", "path_type": "hardlink", "sha256": "629d699b4ec21eed5052ca1ae827dade6f01e8b459ca89ee8edd421dbfaeced4", "sha256_in_prefix": "629d699b4ec21eed5052ca1ae827dade6f01e8b459ca89ee8edd421dbfaeced4", "size_in_bytes": 1411}, {"_path": "Lib/site-packages/setuptools/_vendor/__pycache__/typing_extensions.cpython-311.pyc", "path_type": "hardlink", "sha256": "27c406ce32eb19e9fb790fd0b235a762a07e2799231db86b9cf50c80716b8b1b", "sha256_in_prefix": "27c406ce32eb19e9fb790fd0b235a762a07e2799231db86b9cf50c80716b8b1b", "size_in_bytes": 151452}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "ade78d04982d69972d444a8e14a94f87a2334dd3855cc80348ea8e240aa0df2d", "sha256_in_prefix": "ade78d04982d69972d444a8e14a94f87a2334dd3855cc80348ea8e240aa0df2d", "size_in_bytes": 7634}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "3800d9b91dceea2065a6ed6279383362e97ac38b8e56b9343f404ee531860099", "sha256_in_prefix": "3800d9b91dceea2065a6ed6279383362e97ac38b8e56b9343f404ee531860099", "size_in_bytes": 15006}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "822bba66b41526fa547186b80221f85da50d652bee5493dbfe5d14085112f0c3", "sha256_in_prefix": "822bba66b41526fa547186b80221f85da50d652bee5493dbfe5d14085112f0c3", "size_in_bytes": 1308}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "db07a93359e4e034b8785a58ad6d534ea3dca0635f1e184efe2e66e1c3a299ba", "sha256_in_prefix": "db07a93359e4e034b8785a58ad6d534ea3dca0635f1e184efe2e66e1c3a299ba", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "0337e180a292f04740c16513485f2681e5506d7398f64a241c1ea44aac30aaed", "sha256_in_prefix": "0337e180a292f04740c16513485f2681e5506d7398f64a241c1ea44aac30aaed", "size_in_bytes": 12}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__init__.py", "path_type": "hardlink", "sha256": "ce4a39467be896f6fe5178c2c7fd80acf4c6056c142b9418e0b21020a611ec0b", "sha256_in_prefix": "ce4a39467be896f6fe5178c2c7fd80acf4c6056c142b9418e0b21020a611ec0b", "size_in_bytes": 1037}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "a5127d6f0f407fadbd2f3e62f9bf15ae2d2c3a110c8e8f0b82da17d99c919f50", "sha256_in_prefix": "a5127d6f0f407fadbd2f3e62f9bf15ae2d2c3a110c8e8f0b82da17d99c919f50", "size_in_bytes": 479}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoasync.cpython-311.pyc", "path_type": "hardlink", "sha256": "cfa38bb7f791a8c05f90635537fa5bfa054104f67530e2c38cdd16c049e9d863", "sha256_in_prefix": "cfa38bb7f791a8c05f90635537fa5bfa054104f67530e2c38cdd16c049e9d863", "size_in_bytes": 5305}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autocommand.cpython-311.pyc", "path_type": "hardlink", "sha256": "ab66db50129370ebcbb0e0689149e562162cf7b8df7db06cecddd35b984ba42e", "sha256_in_prefix": "ab66db50129370ebcbb0e0689149e562162cf7b8df7db06cecddd35b984ba42e", "size_in_bytes": 1494}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/automain.cpython-311.pyc", "path_type": "hardlink", "sha256": "c1eee19d346c6306233a4e348f30f0874688401307b2495bc78a4ab061424d93", "sha256_in_prefix": "c1eee19d346c6306233a4e348f30f0874688401307b2495bc78a4ab061424d93", "size_in_bytes": 2001}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoparse.cpython-311.pyc", "path_type": "hardlink", "sha256": "d47eb7fe4c3faa68d389b511bad9c28dbc8ef335d14e770200c9d4de4411d454", "sha256_in_prefix": "d47eb7fe4c3faa68d389b511bad9c28dbc8ef335d14e770200c9d4de4411d454", "size_in_bytes": 12265}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/errors.cpython-311.pyc", "path_type": "hardlink", "sha256": "e2545b74a585a075578d30d0308da58afecc54204d71c54f7336b7f87cdc6dcd", "sha256_in_prefix": "e2545b74a585a075578d30d0308da58afecc54204d71c54f7336b7f87cdc6dcd", "size_in_bytes": 466}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/autoasync.py", "path_type": "hardlink", "sha256": "00c772af1352e29a9625f3ffc6ea0e70898e1d60fea93ef1d3ac2628dd55a7e5", "sha256_in_prefix": "00c772af1352e29a9625f3ffc6ea0e70898e1d60fea93ef1d3ac2628dd55a7e5", "size_in_bytes": 5680}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/autocommand.py", "path_type": "hardlink", "sha256": "866904990ef61ed2f9e609d44558c33a7b1f62519de652d76ef4f8286e3de90c", "sha256_in_prefix": "866904990ef61ed2f9e609d44558c33a7b1f62519de652d76ef4f8286e3de90c", "size_in_bytes": 2505}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/automain.py", "path_type": "hardlink", "sha256": "0366fc8bbe7833173f0e353d585afabea6035a5873d1c9fc9a2bbc77c12cc55f", "sha256_in_prefix": "0366fc8bbe7833173f0e353d585afabea6035a5873d1c9fc9a2bbc77c12cc55f", "size_in_bytes": 2076}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/autoparse.py", "path_type": "hardlink", "sha256": "5955a66493dc6f350a5cfe34ada430ff41c3f2a3c9d95f551b57851669a7171c", "sha256_in_prefix": "5955a66493dc6f350a5cfe34ada430ff41c3f2a3c9d95f551b57851669a7171c", "size_in_bytes": 11642}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/errors.py", "path_type": "hardlink", "sha256": "eda6b7ae887d1deaddea720aa501cd584b25584f28abb1a21d8554b91a8e4670", "sha256_in_prefix": "eda6b7ae887d1deaddea720aa501cd584b25584f28abb1a21d8554b91a8e4670", "size_in_bytes": 886}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "8215c54ead77d9dc5a108a25c6bdc72b5999aa6f62c9499a440359412afa5a51", "sha256_in_prefix": "8215c54ead77d9dc5a108a25c6bdc72b5999aa6f62c9499a440359412afa5a51", "size_in_bytes": 2020}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "258a1f1c849e1175069a55a5d6ce357afdd04e34cd5de27093e4acec7a9d2ce1", "sha256_in_prefix": "258a1f1c849e1175069a55a5d6ce357afdd04e34cd5de27093e4acec7a9d2ce1", "size_in_bytes": 1360}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "7068da2cc3a8051d452b4029a23b73595995893b49ec91882bf1f05e212cbed5", "sha256_in_prefix": "7068da2cc3a8051d452b4029a23b73595995893b49ec91882bf1f05e212cbed5", "size_in_bytes": 10}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/__init__.py", "path_type": "hardlink", "sha256": "88e10cc2794e4567b374ef3edafc4120f491dfb0fb2468e5b99f1fe79bf3c65b", "sha256_in_prefix": "88e10cc2794e4567b374ef3edafc4120f491dfb0fb2468e5b99f1fe79bf3c65b", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "6199ac9986ecf9aca0481ee60c03bd97091d75ce17ffa2bfc24028bbc4609009", "sha256_in_prefix": "6199ac9986ecf9aca0481ee60c03bd97091d75ce17ffa2bfc24028bbc4609009", "size_in_bytes": 305}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__init__.py", "path_type": "hardlink", "sha256": "3f07f6a9421f0744a89493c229cc77bf3dd412efda89db38838b007f1cbde2a8", "sha256_in_prefix": "3f07f6a9421f0744a89493c229cc77bf3dd412efda89db38838b007f1cbde2a8", "size_in_bytes": 108491}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__main__.py", "path_type": "hardlink", "sha256": "630da8193d5a7ebcf6781b24cdd3d82fc45e07fde5880a6684590dd846c399ce", "sha256_in_prefix": "630da8193d5a7ebcf6781b24cdd3d82fc45e07fde5880a6684590dd846c399ce", "size_in_bytes": 59}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "0d1662df9013010a5e138018028eed53a597448155f5d7e1425eae4acea9564f", "sha256_in_prefix": "0d1662df9013010a5e138018028eed53a597448155f5d7e1425eae4acea9564f", "size_in_bytes": 132677}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "03b344ca6a49640ac77273e06cd407d89b4a21f8032fff4bddd864f9922fbacf", "sha256_in_prefix": "03b344ca6a49640ac77273e06cd407d89b4a21f8032fff4bddd864f9922fbacf", "size_in_bytes": 304}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "ef4c71c8356420819570e8b1f5617df087efcf5c89b32582f3ba81f264c453b1", "sha256_in_prefix": "ef4c71c8356420819570e8b1f5617df087efcf5c89b32582f3ba81f264c453b1", "size_in_bytes": 182}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/py38.cpython-311.pyc", "path_type": "hardlink", "sha256": "5db533f7d4ff2ecf49d5aeb3d1f2eb7b94ee0b33d8a9b37a6f8fa6c11f6d1d32", "sha256_in_prefix": "5db533f7d4ff2ecf49d5aeb3d1f2eb7b94ee0b33d8a9b37a6f8fa6c11f6d1d32", "size_in_bytes": 1206}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py", "path_type": "hardlink", "sha256": "898932b7f82f5a32f31944c90fd4ee4df30c8ce93e7abb17666465bd060ddaa1", "sha256_in_prefix": "898932b7f82f5a32f31944c90fd4ee4df30c8ce93e7abb17666465bd060ddaa1", "size_in_bytes": 568}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "sha256_in_prefix": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "size_in_bytes": 11358}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "6a7b90effee1e09d5b484cdf7232016a43e2d9cc9543bcbb8e494b1ec05e1f59", "sha256_in_prefix": "6a7b90effee1e09d5b484cdf7232016a43e2d9cc9543bcbb8e494b1ec05e1f59", "size_in_bytes": 4648}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "0d8d3c6eeb9ebbe86cac7d60861552433c329da9ea51248b61d02be2e5e64030", "sha256_in_prefix": "0d8d3c6eeb9ebbe86cac7d60861552433c329da9ea51248b61d02be2e5e64030", "size_in_bytes": 2518}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "9a0b8c95618c5fe5479cca4a3a38d089d228d6cb1194216ee1ae26069cf5b363", "sha256_in_prefix": "9a0b8c95618c5fe5479cca4a3a38d089d228d6cb1194216ee1ae26069cf5b363", "size_in_bytes": 91}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "08eddf0fdcb29403625e4acca38a872d5fe6a972f6b02e4914a82dd725804fe0", "sha256_in_prefix": "08eddf0fdcb29403625e4acca38a872d5fe6a972f6b02e4914a82dd725804fe0", "size_in_bytes": 19}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__init__.py", "path_type": "hardlink", "sha256": "b59341fb6de1f018b18bdb82ad0aa3f587f469e0bef89a2c772dc8651210781d", "sha256_in_prefix": "b59341fb6de1f018b18bdb82ad0aa3f587f469e0bef89a2c772dc8651210781d", "size_in_bytes": 33798}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "4e18bfa307caaabf154f585212e8b3bf1872d4c31c65f85b87b740f5dbef1abe", "sha256_in_prefix": "4e18bfa307caaabf154f585212e8b3bf1872d4c31c65f85b87b740f5dbef1abe", "size_in_bytes": 58813}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_adapters.cpython-311.pyc", "path_type": "hardlink", "sha256": "2f05db3a9d1063a2ea2faf8f4fe9bff7f550108faaf504ae5014f7831b2254a8", "sha256_in_prefix": "2f05db3a9d1063a2ea2faf8f4fe9bff7f550108faaf504ae5014f7831b2254a8", "size_in_bytes": 4447}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_collections.cpython-311.pyc", "path_type": "hardlink", "sha256": "df8049bf38fcff4b2ad06e3032e1a6a723eb4d3372732b035d1e51d6be362256", "sha256_in_prefix": "df8049bf38fcff4b2ad06e3032e1a6a723eb4d3372732b035d1e51d6be362256", "size_in_bytes": 2156}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_compat.cpython-311.pyc", "path_type": "hardlink", "sha256": "d9717bfd2d6b5dfd96a99b0403d5e4cc774d2dec150f39214c6f11bb9cf98c5b", "sha256_in_prefix": "d9717bfd2d6b5dfd96a99b0403d5e4cc774d2dec150f39214c6f11bb9cf98c5b", "size_in_bytes": 2445}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_functools.cpython-311.pyc", "path_type": "hardlink", "sha256": "0e2dc7ffc55a22e110031bbec7b7b47ba62970ad9fb47a4ecfa8cf273d71712c", "sha256_in_prefix": "0e2dc7ffc55a22e110031bbec7b7b47ba62970ad9fb47a4ecfa8cf273d71712c", "size_in_bytes": 3596}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_itertools.cpython-311.pyc", "path_type": "hardlink", "sha256": "c20ac596ea13cb968618077233cda33bd76b6514e9e0dc31d6ab4d534de60c76", "sha256_in_prefix": "c20ac596ea13cb968618077233cda33bd76b6514e9e0dc31d6ab4d534de60c76", "size_in_bytes": 2559}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_meta.cpython-311.pyc", "path_type": "hardlink", "sha256": "a67a0f0e932576fc3cb575892c9f2ed6dc5984b6190b4f874919b1dfb4c33faa", "sha256_in_prefix": "a67a0f0e932576fc3cb575892c9f2ed6dc5984b6190b4f874919b1dfb4c33faa", "size_in_bytes": 4072}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_text.cpython-311.pyc", "path_type": "hardlink", "sha256": "1ca5f8c0de70d9c8a51feb236d0a4131d779f33074574d93699e90166ffa0b5e", "sha256_in_prefix": "1ca5f8c0de70d9c8a51feb236d0a4131d779f33074574d93699e90166ffa0b5e", "size_in_bytes": 4354}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/diagnose.cpython-311.pyc", "path_type": "hardlink", "sha256": "fda6efafac48064c3425489046fd00f8e8c1a01eb988cfb44833a264cc5f6b7e", "sha256_in_prefix": "fda6efafac48064c3425489046fd00f8e8c1a01eb988cfb44833a264cc5f6b7e", "size_in_bytes": 1354}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py", "path_type": "hardlink", "sha256": "ac88564f006f600d5b57b8bee457d55f7f2a1170d35c5792e5c6f9c49b4fde4b", "sha256_in_prefix": "ac88564f006f600d5b57b8bee457d55f7f2a1170d35c5792e5c6f9c49b4fde4b", "size_in_bytes": 2317}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_collections.py", "path_type": "hardlink", "sha256": "089d0e4c21c88d6034648552e2fa0e440b27d91e11d9c40112d3ec6442690126", "sha256_in_prefix": "089d0e4c21c88d6034648552e2fa0e440b27d91e11d9c40112d3ec6442690126", "size_in_bytes": 743}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_compat.py", "path_type": "hardlink", "sha256": "ef740aacdf4a368699ce16d7e723c20996be15a00bc177bc4cf94347bd898015", "sha256_in_prefix": "ef740aacdf4a368699ce16d7e723c20996be15a00bc177bc4cf94347bd898015", "size_in_bytes": 1314}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_functools.py", "path_type": "hardlink", "sha256": "3ec636fb8aeb297e1155e442d681a9d65075a660bd78a37cf3f7fe6c3f6e3a80", "sha256_in_prefix": "3ec636fb8aeb297e1155e442d681a9d65075a660bd78a37cf3f7fe6c3f6e3a80", "size_in_bytes": 2895}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py", "path_type": "hardlink", "sha256": "72faffdaff0145bc5c225e71e6575fa9d1e3848f188bcb3cca4e741bf9e6ea34", "sha256_in_prefix": "72faffdaff0145bc5c225e71e6575fa9d1e3848f188bcb3cca4e741bf9e6ea34", "size_in_bytes": 2068}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_meta.py", "path_type": "hardlink", "sha256": "9f167b0bc19595c04500a5b254e9ff767ee8b7fb7005c6e6d4d9af8c87ad0472", "sha256_in_prefix": "9f167b0bc19595c04500a5b254e9ff767ee8b7fb7005c6e6d4d9af8c87ad0472", "size_in_bytes": 1801}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_text.py", "path_type": "hardlink", "sha256": "1c2b0592c66924b7933f734493f9e0ac079755146d4ebb7287d78e001a113f80", "sha256_in_prefix": "1c2b0592c66924b7933f734493f9e0ac079755146d4ebb7287d78e001a113f80", "size_in_bytes": 2166}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "444f0f0c5f90849ab0155d60e166e540ffaf237a4f3c75e89e1c277c02fb4e15", "sha256_in_prefix": "444f0f0c5f90849ab0155d60e166e540ffaf237a4f3c75e89e1c277c02fb4e15", "size_in_bytes": 183}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py311.cpython-311.pyc", "path_type": "hardlink", "sha256": "f651a4151e2139336df711e8a96e1d4a6e03d52f71796e8e7f62f230e9f75723", "sha256_in_prefix": "f651a4151e2139336df711e8a96e1d4a6e03d52f71796e8e7f62f230e9f75723", "size_in_bytes": 1303}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py39.cpython-311.pyc", "path_type": "hardlink", "sha256": "a29b62caf3523ad4a5372a9b4faf7f922cad293f6527320c9114d72c54872ead", "sha256_in_prefix": "a29b62caf3523ad4a5372a9b4faf7f922cad293f6527320c9114d72c54872ead", "size_in_bytes": 1761}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py", "path_type": "hardlink", "sha256": "baa9be2beba88728f5d38d931f86bd12bfc8e68efaebb0efba5703fa00bf7d20", "sha256_in_prefix": "baa9be2beba88728f5d38d931f86bd12bfc8e68efaebb0efba5703fa00bf7d20", "size_in_bytes": 608}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py", "path_type": "hardlink", "sha256": "70f90cbfafb48a52bed09c3f4e49f4c586ce28965ce1624a407a30d1cd93e38c", "sha256_in_prefix": "70f90cbfafb48a52bed09c3f4e49f4c586ce28965ce1624a407a30d1cd93e38c", "size_in_bytes": 1102}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/diagnose.py", "path_type": "hardlink", "sha256": "9e4491322a309669212d884a86f0a0f60966b7fd750a8c7e1262f311ba984daf", "sha256_in_prefix": "9e4491322a309669212d884a86f0a0f60966b7fd750a8c7e1262f311ba984daf", "size_in_bytes": 379}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "sha256_in_prefix": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "size_in_bytes": 11358}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "83878cd8bb8bd0e89971454d0f4ab00c9529136f603afb4edc148f5d36cef459", "sha256_in_prefix": "83878cd8bb8bd0e89971454d0f4ab00c9529136f603afb4edc148f5d36cef459", "size_in_bytes": 3944}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "967dd56feea143f1d2c4e98ac1f937c055e61c9aa0425146d55f7ad7c82510fa", "sha256_in_prefix": "967dd56feea143f1d2c4e98ac1f937c055e61c9aa0425146d55f7ad7c82510fa", "size_in_bytes": 7620}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "7c72231d4d46670023bdcc9da6652752b4e76ef7625a31b83845592bc6f2d134", "sha256_in_prefix": "7c72231d4d46670023bdcc9da6652752b4e76ef7625a31b83845592bc6f2d134", "size_in_bytes": 20}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/__init__.py", "path_type": "hardlink", "sha256": "bb2a75933611e926b0401b2a9726975df231271e19da633c3239999fcaaad869", "sha256_in_prefix": "bb2a75933611e926b0401b2a9726975df231271e19da633c3239999fcaaad869", "size_in_bytes": 505}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "65b87b5b70c1bbff4a7dedd92f4dd40054d451ceca5b16ba2ebd9823aede0670", "sha256_in_prefix": "65b87b5b70c1bbff4a7dedd92f4dd40054d451ceca5b16ba2ebd9823aede0670", "size_in_bytes": 792}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/__pycache__/_adapters.cpython-311.pyc", "path_type": "hardlink", "sha256": "7b6a0345cb466a29684ff889168f6254db6049e3a792a834c5d9878d76380665", "sha256_in_prefix": "7b6a0345cb466a29684ff889168f6254db6049e3a792a834c5d9878d76380665", "size_in_bytes": 10665}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/__pycache__/_common.cpython-311.pyc", "path_type": "hardlink", "sha256": "7b3c85d9f6887a0050f807333fb10b2fcfdad826f8a6241738de75f84eb38536", "sha256_in_prefix": "7b3c85d9f6887a0050f807333fb10b2fcfdad826f8a6241738de75f84eb38536", "size_in_bytes": 9604}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/__pycache__/_itertools.cpython-311.pyc", "path_type": "hardlink", "sha256": "b177fbd75cb62c71aedaa24b873cb3ceb09e0d467d5144dfc3e7871c5983d821", "sha256_in_prefix": "b177fbd75cb62c71aedaa24b873cb3ceb09e0d467d5144dfc3e7871c5983d821", "size_in_bytes": 1649}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/__pycache__/abc.cpython-311.pyc", "path_type": "hardlink", "sha256": "245b20d4f116af534eab5b2413dda014087a485d3fd78d248a848e890e5e50a8", "sha256_in_prefix": "245b20d4f116af534eab5b2413dda014087a485d3fd78d248a848e890e5e50a8", "size_in_bytes": 9917}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/__pycache__/functional.cpython-311.pyc", "path_type": "hardlink", "sha256": "fd812f41f7eadc85d692db4976e959c4cb6d4670860071320929cee4f24c4e61", "sha256_in_prefix": "fd812f41f7eadc85d692db4976e959c4cb6d4670860071320929cee4f24c4e61", "size_in_bytes": 3943}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/__pycache__/readers.cpython-311.pyc", "path_type": "hardlink", "sha256": "8827d2c22df758acad7b50d6dfb708b88386910b877750090d2395add07b5eb5", "sha256_in_prefix": "8827d2c22df758acad7b50d6dfb708b88386910b877750090d2395add07b5eb5", "size_in_bytes": 12908}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/__pycache__/simple.cpython-311.pyc", "path_type": "hardlink", "sha256": "d10791c9446ce9bc6c58853a26f3a3327080d3a10c9581592974c0a5b1cbcdce", "sha256_in_prefix": "d10791c9446ce9bc6c58853a26f3a3327080d3a10c9581592974c0a5b1cbcdce", "size_in_bytes": 6054}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/_adapters.py", "path_type": "hardlink", "sha256": "be9ac919b51e1db6a35fa5c2b8c3fa27794caea0a2f8ffcc4e5ce225447b8df9", "sha256_in_prefix": "be9ac919b51e1db6a35fa5c2b8c3fa27794caea0a2f8ffcc4e5ce225447b8df9", "size_in_bytes": 4482}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/_common.py", "path_type": "hardlink", "sha256": "6e5b78f99b479db50fcd04323cfee32c6825ffce9bb485b966122c1217258680", "sha256_in_prefix": "6e5b78f99b479db50fcd04323cfee32c6825ffce9bb485b966122c1217258680", "size_in_bytes": 5571}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/_itertools.py", "path_type": "hardlink", "sha256": "7838ac57a46a88d64ea202d25dfe8b3861ce61cefd14680faca34bcc52e60ab5", "sha256_in_prefix": "7838ac57a46a88d64ea202d25dfe8b3861ce61cefd14680faca34bcc52e60ab5", "size_in_bytes": 1277}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/abc.py", "path_type": "hardlink", "sha256": "50a354f677040e4651077b717066f758bc6c2f68a3bbd25b68b4c8f9d7cb13fe", "sha256_in_prefix": "50a354f677040e4651077b717066f758bc6c2f68a3bbd25b68b4c8f9d7cb13fe", "size_in_bytes": 5162}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "86c493a41e0296b771a1c9361411f52cbd5344e535abbbf743021131f1583d1f", "sha256_in_prefix": "86c493a41e0296b771a1c9361411f52cbd5344e535abbbf743021131f1583d1f", "size_in_bytes": 184}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/compat/__pycache__/py38.cpython-311.pyc", "path_type": "hardlink", "sha256": "25dabd7dad6b0ca4f00da8ece6ff309b49455ccbc25d521aeebc2952505e73c2", "sha256_in_prefix": "25dabd7dad6b0ca4f00da8ece6ff309b49455ccbc25d521aeebc2952505e73c2", "size_in_bytes": 487}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/compat/__pycache__/py39.cpython-311.pyc", "path_type": "hardlink", "sha256": "959ef0190db13b9bf00106a2d41f3d960d69f715bc6bcd68a6ffa907dbe7eeb5", "sha256_in_prefix": "959ef0190db13b9bf00106a2d41f3d960d69f715bc6bcd68a6ffa907dbe7eeb5", "size_in_bytes": 400}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/compat/py38.py", "path_type": "hardlink", "sha256": "31686eb775ec009c0161469ae506f60280ab64da9c42355384ff8fd6e06813fe", "sha256_in_prefix": "31686eb775ec009c0161469ae506f60280ab64da9c42355384ff8fd6e06813fe", "size_in_bytes": 230}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/compat/py39.py", "path_type": "hardlink", "sha256": "59f967e2e4144a1373d577421beb681bafd8d16ae55263bd273a6fb5c7d0f82c", "sha256_in_prefix": "59f967e2e4144a1373d577421beb681bafd8d16ae55263bd273a6fb5c7d0f82c", "size_in_bytes": 184}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/functional.py", "path_type": "hardlink", "sha256": "98b5380f04a587cff62175aac0a39f3d5c7246a004a41dc1e174df471af75f73", "sha256_in_prefix": "98b5380f04a587cff62175aac0a39f3d5c7246a004a41dc1e174df471af75f73", "size_in_bytes": 2651}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/future/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/future/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "3f07f63ec911b14dcca98168367b1708800341c760bc86fd6d73bb809070bb4d", "sha256_in_prefix": "3f07f63ec911b14dcca98168367b1708800341c760bc86fd6d73bb809070bb4d", "size_in_bytes": 184}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/future/__pycache__/adapters.cpython-311.pyc", "path_type": "hardlink", "sha256": "8bbb5f185b0be20e30826a1dcdf092cc5e612e81f5b05e69cac935a62730d702", "sha256_in_prefix": "8bbb5f185b0be20e30826a1dcdf092cc5e612e81f5b05e69cac935a62730d702", "size_in_bytes": 5745}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/future/adapters.py", "path_type": "hardlink", "sha256": "d7e305d9545c081bad85c0b538c7d920b53da379306789d707696ead7a5a200c", "sha256_in_prefix": "d7e305d9545c081bad85c0b538c7d920b53da379306789d707696ead7a5a200c", "size_in_bytes": 2940}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/readers.py", "path_type": "hardlink", "sha256": "58d2aeac11c756ef4456d5215a43a3d9fc47e741cfeee6a7345baea40a87d92f", "sha256_in_prefix": "58d2aeac11c756ef4456d5215a43a3d9fc47e741cfeee6a7345baea40a87d92f", "size_in_bytes": 5863}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/simple.py", "path_type": "hardlink", "sha256": "090dd3888305889b3ff34a3eef124bd44a5b5145676b8f8d183ad24d0dc75b66", "sha256_in_prefix": "090dd3888305889b3ff34a3eef124bd44a5b5145676b8f8d183ad24d0dc75b66", "size_in_bytes": 2584}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "51a6e103fd162fb5a9bb35a67c545b4be81ae6853793e434c912c8aefa700c56", "sha256_in_prefix": "51a6e103fd162fb5a9bb35a67c545b4be81ae6853793e434c912c8aefa700c56", "size_in_bytes": 183}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/_path.cpython-311.pyc", "path_type": "hardlink", "sha256": "3ac0b54600339932c0c154e569a53b02277225ec4ab6b235d4434c240a1c4a1d", "sha256_in_prefix": "3ac0b54600339932c0c154e569a53b02277225ec4ab6b235d4434c240a1c4a1d", "size_in_bytes": 2351}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_compatibilty_files.cpython-311.pyc", "path_type": "hardlink", "sha256": "90e4f259f44010f8919b50c5b3c7929c9094ab4528dfcaa535986aa12e548a2a", "sha256_in_prefix": "90e4f259f44010f8919b50c5b3c7929c9094ab4528dfcaa535986aa12e548a2a", "size_in_bytes": 10021}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_contents.cpython-311.pyc", "path_type": "hardlink", "sha256": "1407146130a9a679dc8db8b9181d70c0cfa1fa15fe2dd0ad655147dd714e9380", "sha256_in_prefix": "1407146130a9a679dc8db8b9181d70c0cfa1fa15fe2dd0ad655147dd714e9380", "size_in_bytes": 2476}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_custom.cpython-311.pyc", "path_type": "hardlink", "sha256": "fd242c3bad59daa0b1ffb985b9bd007911b2484ad2f712186a2dac32870f0461", "sha256_in_prefix": "fd242c3bad59daa0b1ffb985b9bd007911b2484ad2f712186a2dac32870f0461", "size_in_bytes": 3410}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_files.cpython-311.pyc", "path_type": "hardlink", "sha256": "f2cbff2a4ced808435c6b001a1939939237e1a95c6a046632b5a6eaee005943f", "sha256_in_prefix": "f2cbff2a4ced808435c6b001a1939939237e1a95c6a046632b5a6eaee005943f", "size_in_bytes": 8249}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_functional.cpython-311.pyc", "path_type": "hardlink", "sha256": "e8fda8bae9d373a88f3bf35572ac865e9a3c2ef1c0385b7444708f0e438ac9bf", "sha256_in_prefix": "e8fda8bae9d373a88f3bf35572ac865e9a3c2ef1c0385b7444708f0e438ac9bf", "size_in_bytes": 16831}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_open.cpython-311.pyc", "path_type": "hardlink", "sha256": "5fd3c223e7dc2be01cf199746e521d2f41d4937573f54a90fc1ed68f1e3495d4", "sha256_in_prefix": "5fd3c223e7dc2be01cf199746e521d2f41d4937573f54a90fc1ed68f1e3495d4", "size_in_bytes": 7919}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_path.cpython-311.pyc", "path_type": "hardlink", "sha256": "84e907e5bd254b5db483f2b53d99733ea3ef0c1905a52bb64b93ec9589c1b232", "sha256_in_prefix": "84e907e5bd254b5db483f2b53d99733ea3ef0c1905a52bb64b93ec9589c1b232", "size_in_bytes": 5375}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_read.cpython-311.pyc", "path_type": "hardlink", "sha256": "e55488b1acd8537ec7f68b8812f24d6499309cb605ee96bcb2a0cd39a1be2602", "sha256_in_prefix": "e55488b1acd8537ec7f68b8812f24d6499309cb605ee96bcb2a0cd39a1be2602", "size_in_bytes": 7543}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_reader.cpython-311.pyc", "path_type": "hardlink", "sha256": "ae685d231e7706f9e11ff774db8cd42dc2acd02a7c5f7b106c820b4472ad44a5", "sha256_in_prefix": "ae685d231e7706f9e11ff774db8cd42dc2acd02a7c5f7b106c820b4472ad44a5", "size_in_bytes": 12907}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_resource.cpython-311.pyc", "path_type": "hardlink", "sha256": "5e374e9fe0401a76058200bf13b4cfabd5569a69b0e43c50700cd873e1e51f0a", "sha256_in_prefix": "5e374e9fe0401a76058200bf13b4cfabd5569a69b0e43c50700cd873e1e51f0a", "size_in_bytes": 18774}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/util.cpython-311.pyc", "path_type": "hardlink", "sha256": "de57ad24f803acaf93c84efd4e0b3fd858f032c358e85344abc6fc5e958a68aa", "sha256_in_prefix": "de57ad24f803acaf93c84efd4e0b3fd858f032c358e85344abc6fc5e958a68aa", "size_in_bytes": 9944}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/zip.cpython-311.pyc", "path_type": "hardlink", "sha256": "af88ed2e42becd1d182b9d739508e3de632bb5fc40f57fe410e3b9ed8246be66", "sha256_in_prefix": "af88ed2e42becd1d182b9d739508e3de632bb5fc40f57fe410e3b9ed8246be66", "size_in_bytes": 2046}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/_path.py", "path_type": "hardlink", "sha256": "9e4bf77a4ec3d54f3df2ff76d6b61b95d0c2b4aae2da8c983a2dc426a1a31065", "sha256_in_prefix": "9e4bf77a4ec3d54f3df2ff76d6b61b95d0c2b4aae2da8c983a2dc426a1a31065", "size_in_bytes": 1289}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "d7d348e090f7d64c87355fb302da9ce1ddee2680511d8a52767e9bbcdae1c1bb", "sha256_in_prefix": "d7d348e090f7d64c87355fb302da9ce1ddee2680511d8a52767e9bbcdae1c1bb", "size_in_bytes": 190}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/compat/__pycache__/py312.cpython-311.pyc", "path_type": "hardlink", "sha256": "4bb491de49448003c1497942093c21caa97e3ab9f8468a430b16f66446b364da", "sha256_in_prefix": "4bb491de49448003c1497942093c21caa97e3ab9f8468a430b16f66446b364da", "size_in_bytes": 909}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/compat/__pycache__/py39.cpython-311.pyc", "path_type": "hardlink", "sha256": "66322879f5107ca2df51214bc1a12fc4b871057119ad19995ac74b4e57e94e3c", "sha256_in_prefix": "66322879f5107ca2df51214bc1a12fc4b871057119ad19995ac74b4e57e94e3c", "size_in_bytes": 641}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/compat/py312.py", "path_type": "hardlink", "sha256": "a9c5a3a59850a36a04b1dc089514501ebb0c18396d9054e7113786edf2dd512f", "sha256_in_prefix": "a9c5a3a59850a36a04b1dc089514501ebb0c18396d9054e7113786edf2dd512f", "size_in_bytes": 364}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/compat/py39.py", "path_type": "hardlink", "sha256": "9514e4d115803846fd473020bdd467a895060812dcdeaa05430cee2526bfccfe", "sha256_in_prefix": "9514e4d115803846fd473020bdd467a895060812dcdeaa05430cee2526bfccfe", "size_in_bytes": 329}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data01/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data01/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "961db53815ac4047bc807311cf7e19c4d9cf12c4c27bb376bdcf4261b9d70688", "sha256_in_prefix": "961db53815ac4047bc807311cf7e19c4d9cf12c4c27bb376bdcf4261b9d70688", "size_in_bytes": 190}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data01/binary.file", "path_type": "hardlink", "sha256": "054edec1d0211f624fed0cbca9d4f9400b0e491c43742af2c5b0abebf0c990d8", "sha256_in_prefix": "054edec1d0211f624fed0cbca9d4f9400b0e491c43742af2c5b0abebf0c990d8", "size_in_bytes": 4}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data01/subdirectory/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data01/subdirectory/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "2aef06d59633bda0d22ae98d0135ea89cf6cf7edee54647a4c19d69bdc2f4a2b", "sha256_in_prefix": "2aef06d59633bda0d22ae98d0135ea89cf6cf7edee54647a4c19d69bdc2f4a2b", "size_in_bytes": 203}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data01/subdirectory/binary.file", "path_type": "hardlink", "sha256": "c6d44cf418f610e3fe9e1d9294ff43def81c6cdcad6cbb1820cff48d3aa4355d", "sha256_in_prefix": "c6d44cf418f610e3fe9e1d9294ff43def81c6cdcad6cbb1820cff48d3aa4355d", "size_in_bytes": 4}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data01/utf-16.file", "path_type": "hardlink", "sha256": "b79abdaa1c57d2b62a22d04e33c0f7ca5c06f911eb9ce62d7932ed42beac17b8", "sha256_in_prefix": "b79abdaa1c57d2b62a22d04e33c0f7ca5c06f911eb9ce62d7932ed42beac17b8", "size_in_bytes": 44}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data01/utf-8.file", "path_type": "hardlink", "sha256": "9305a0606e3243e645d97fd603ae848d83e6c49467fb0f1a48e892f5ef2d2986", "sha256_in_prefix": "9305a0606e3243e645d97fd603ae848d83e6c49467fb0f1a48e892f5ef2d2986", "size_in_bytes": 20}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data02/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data02/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "c6d59c7f0d4f7371a03f2178132818eda15fde52326a2ea9ec68c91f3030c5c0", "sha256_in_prefix": "c6d59c7f0d4f7371a03f2178132818eda15fde52326a2ea9ec68c91f3030c5c0", "size_in_bytes": 190}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data02/one/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data02/one/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "90fe7eeebb9a592e3ce3da965680939be59a68d45ec080f5e9cd0f8f7a6aa534", "sha256_in_prefix": "90fe7eeebb9a592e3ce3da965680939be59a68d45ec080f5e9cd0f8f7a6aa534", "size_in_bytes": 194}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data02/one/resource1.txt", "path_type": "hardlink", "sha256": "d747e529a73b73e5d7173277b7e001e4c263941cbffdd499bcf13f74e9b6aba5", "sha256_in_prefix": "d747e529a73b73e5d7173277b7e001e4c263941cbffdd499bcf13f74e9b6aba5", "size_in_bytes": 13}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data02/subdirectory/subsubdir/resource.txt", "path_type": "hardlink", "sha256": "8e7ac1073b7162bb50724edc9959dce314153b8f9a8330190c64859005ad945c", "sha256_in_prefix": "8e7ac1073b7162bb50724edc9959dce314153b8f9a8330190c64859005ad945c", "size_in_bytes": 10}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data02/two/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data02/two/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "256c879f86212d6aaff8fc9b558dacd60ede2b9285493c3c8b006b800f572920", "sha256_in_prefix": "256c879f86212d6aaff8fc9b558dacd60ede2b9285493c3c8b006b800f572920", "size_in_bytes": 194}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/data02/two/resource2.txt", "path_type": "hardlink", "sha256": "96dda36cddd3327f5088528cf37d97dfd6d4ffad94a6d0dd524a18ce4bc46e5d", "sha256_in_prefix": "96dda36cddd3327f5088528cf37d97dfd6d4ffad94a6d0dd524a18ce4bc46e5d", "size_in_bytes": 13}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/namespacedata01/binary.file", "path_type": "hardlink", "sha256": "054edec1d0211f624fed0cbca9d4f9400b0e491c43742af2c5b0abebf0c990d8", "sha256_in_prefix": "054edec1d0211f624fed0cbca9d4f9400b0e491c43742af2c5b0abebf0c990d8", "size_in_bytes": 4}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/namespacedata01/subdirectory/binary.file", "path_type": "hardlink", "sha256": "71b92110bf135c85581c8a128f6a19c0f6aca752b0c6c91e3571899cf09b145d", "sha256_in_prefix": "71b92110bf135c85581c8a128f6a19c0f6aca752b0c6c91e3571899cf09b145d", "size_in_bytes": 4}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/namespacedata01/utf-16.file", "path_type": "hardlink", "sha256": "b79abdaa1c57d2b62a22d04e33c0f7ca5c06f911eb9ce62d7932ed42beac17b8", "sha256_in_prefix": "b79abdaa1c57d2b62a22d04e33c0f7ca5c06f911eb9ce62d7932ed42beac17b8", "size_in_bytes": 44}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/namespacedata01/utf-8.file", "path_type": "hardlink", "sha256": "9305a0606e3243e645d97fd603ae848d83e6c49467fb0f1a48e892f5ef2d2986", "sha256_in_prefix": "9305a0606e3243e645d97fd603ae848d83e6c49467fb0f1a48e892f5ef2d2986", "size_in_bytes": 20}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_compatibilty_files.py", "path_type": "hardlink", "sha256": "f7937f47b6a293c72f9c4eac049a6cc663f42b9a6539644824381b6a50fe1e9c", "sha256_in_prefix": "f7937f47b6a293c72f9c4eac049a6cc663f42b9a6539644824381b6a50fe1e9c", "size_in_bytes": 3314}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_contents.py", "path_type": "hardlink", "sha256": "ef41d6de62ff86fd39126bfe3a0766c282e15f18edb95c62595694a6045a4560", "sha256_in_prefix": "ef41d6de62ff86fd39126bfe3a0766c282e15f18edb95c62595694a6045a4560", "size_in_bytes": 930}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_custom.py", "path_type": "hardlink", "sha256": "42b1d9a885a5d1ef9fb1045f9b4c9c87cb2d3a52893ac008537acae9039cc8dd", "sha256_in_prefix": "42b1d9a885a5d1ef9fb1045f9b4c9c87cb2d3a52893ac008537acae9039cc8dd", "size_in_bytes": 1221}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_files.py", "path_type": "hardlink", "sha256": "39c4a162edf79027325e50f265254f909344d3c87e37fe1b40e2d5d90692a97d", "sha256_in_prefix": "39c4a162edf79027325e50f265254f909344d3c87e37fe1b40e2d5d90692a97d", "size_in_bytes": 3472}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_functional.py", "path_type": "hardlink", "sha256": "0720955620306f63c894abc334912a4d9d1a2d91a9165e6a6bb08c097fbb1ca3", "sha256_in_prefix": "0720955620306f63c894abc334912a4d9d1a2d91a9165e6a6bb08c097fbb1ca3", "size_in_bytes": 8591}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_open.py", "path_type": "hardlink", "sha256": "71c9b36ce7846bacd3778ca6659f32212ade71fb985748e1a27f9575dab2b2ee", "sha256_in_prefix": "71c9b36ce7846bacd3778ca6659f32212ade71fb985748e1a27f9575dab2b2ee", "size_in_bytes": 2778}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_path.py", "path_type": "hardlink", "sha256": "c7caf6809c46de114cf7108e14d920987617c6c32574c2d3496fc06587f597e0", "sha256_in_prefix": "c7caf6809c46de114cf7108e14d920987617c6c32574c2d3496fc06587f597e0", "size_in_bytes": 2009}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_read.py", "path_type": "hardlink", "sha256": "eedb082d0d8da2a546150c611eac41c1ce6159c37c6ffde27688c2b2ccd335f4", "sha256_in_prefix": "eedb082d0d8da2a546150c611eac41c1ce6159c37c6ffde27688c2b2ccd335f4", "size_in_bytes": 3112}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_reader.py", "path_type": "hardlink", "sha256": "21c2145da88f02db9a846578fd94f861714b30cb0999c33588ec6a7481f601ae", "sha256_in_prefix": "21c2145da88f02db9a846578fd94f861714b30cb0999c33588ec6a7481f601ae", "size_in_bytes": 5001}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/test_resource.py", "path_type": "hardlink", "sha256": "7dc17c5a067aac30934459f1b4051b76268d7b81b6df21a8bd3d676f675cee5b", "sha256_in_prefix": "7dc17c5a067aac30934459f1b4051b76268d7b81b6df21a8bd3d676f675cee5b", "size_in_bytes": 7823}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/util.py", "path_type": "hardlink", "sha256": "be35731325f45f64644cdfb01a2422a656b2a7db19a26e090e32629d335ec28b", "sha256_in_prefix": "be35731325f45f64644cdfb01a2422a656b2a7db19a26e090e32629d335ec28b", "size_in_bytes": 4745}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_resources/tests/zip.py", "path_type": "hardlink", "sha256": "d8c2a617cfa8b170494a7a9c513b8051e93ffad481de22a6213f6a3e172c3ac3", "sha256_in_prefix": "d8c2a617cfa8b170494a7a9c513b8051e93ffad481de22a6213f6a3e172c3ac3", "size_in_bytes": 783}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "66030d634580651b3e53cc19895d9231f8d22aa06b327817c8332cfc20303308", "sha256_in_prefix": "66030d634580651b3e53cc19895d9231f8d22aa06b327817c8332cfc20303308", "size_in_bytes": 21079}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "5d7834ac1ba2612c6801050fde57a7b98b0f36acf88c3c2d4f376fd8911b3091", "sha256_in_prefix": "5d7834ac1ba2612c6801050fde57a7b98b0f36acf88c3c2d4f376fd8911b3091", "size_in_bytes": 943}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cb8997f92397e1f6089289ec0060393743b2fbcfe0238157c391cd235c6abd68", "sha256_in_prefix": "cb8997f92397e1f6089289ec0060393743b2fbcfe0238157c391cd235c6abd68", "size_in_bytes": 91}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "9b9dae8dda75d02a93ea38755d0c594fa9049ed727bfeed397b52218d4f35990", "sha256_in_prefix": "9b9dae8dda75d02a93ea38755d0c594fa9049ed727bfeed397b52218d4f35990", "size_in_bytes": 8}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/__init__.py", "path_type": "hardlink", "sha256": "271cb51c95d9899f3990778b021926bf3e84313745a817be76ebeddf847a20e7", "sha256_in_prefix": "271cb51c95d9899f3990778b021926bf3e84313745a817be76ebeddf847a20e7", "size_in_bytes": 103796}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "71c5272b205c3fd2f2bb879725cd38039fbdb35d5458d81022fff72310d2607b", "sha256_in_prefix": "71c5272b205c3fd2f2bb879725cd38039fbdb35d5458d81022fff72310d2607b", "size_in_bytes": 125053}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "0a0773ae995ae2870534dbd9b9e9c25ba793ca7faf1c1c225c555edaca8c2475", "sha256_in_prefix": "0a0773ae995ae2870534dbd9b9e9c25ba793ca7faf1c1c225c555edaca8c2475", "size_in_bytes": 172}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/py38.cpython-311.pyc", "path_type": "hardlink", "sha256": "5ff49983212698569795b7041befcfe2c775bf1b8251bf6d3bf8eba2c9dd21eb", "sha256_in_prefix": "5ff49983212698569795b7041befcfe2c775bf1b8251bf6d3bf8eba2c9dd21eb", "size_in_bytes": 367}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/py38.py", "path_type": "hardlink", "sha256": "a0e6d57d59d65fdfcea673ae1099f59856c9c55870c91e5ea5b8933570c36aca", "sha256_in_prefix": "a0e6d57d59d65fdfcea673ae1099f59856c9c55870c91e5ea5b8933570c36aca", "size_in_bytes": 160}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "20c51a96236c0395f53b1f4c5d458e6a0721e51e16c1bff733b7aba76f5d06d8", "sha256_in_prefix": "20c51a96236c0395f53b1f4c5d458e6a0721e51e16c1bff733b7aba76f5d06d8", "size_in_bytes": 3933}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "1e9b62bd70e4a5fa26e9594cbb80860ffeca3debfee8773daefa774cd259ca06", "sha256_in_prefix": "1e9b62bd70e4a5fa26e9594cbb80860ffeca3debfee8773daefa774cd259ca06", "size_in_bytes": 873}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "31d8bd3c3370119a6d3a34e551c02d87b5c90c5b4aac761a40c3ee9597810a24", "sha256_in_prefix": "31d8bd3c3370119a6d3a34e551c02d87b5c90c5b4aac761a40c3ee9597810a24", "size_in_bytes": 91}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "c43b60b897a3d2d37d8845c252fc44261d9aef171e21154111a9012d2afffed6", "sha256_in_prefix": "c43b60b897a3d2d37d8845c252fc44261d9aef171e21154111a9012d2afffed6", "size_in_bytes": 4020}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "55197b88a78443297bb2d827a75baae740b33896251d872835d4b4c75ec2f57e", "sha256_in_prefix": "55197b88a78443297bb2d827a75baae740b33896251d872835d4b4c75ec2f57e", "size_in_bytes": 641}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "8b86946900d7fa38dd1102b9c1ebe17a0cb1f09c8b7e29f61f2bda4a4dc51eca", "sha256_in_prefix": "8b86946900d7fa38dd1102b9c1ebe17a0cb1f09c8b7e29f61f2bda4a4dc51eca", "size_in_bytes": 2891}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "632aa7c04f7c4bcc01c027af5b9bc76fe8958f4a181035b957a3bd3014ba248b", "sha256_in_prefix": "632aa7c04f7c4bcc01c027af5b9bc76fe8958f4a181035b957a3bd3014ba248b", "size_in_bytes": 843}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "03359d9ba56231f0ce3e840c7cb5a7db380141218949ccaa78ddbd4dcb965d52", "sha256_in_prefix": "03359d9ba56231f0ce3e840c7cb5a7db380141218949ccaa78ddbd4dcb965d52", "size_in_bytes": 3658}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "816d945741dca246099388ca3eed74fc0667acbaa36f70b559b2494c3979b1f6", "sha256_in_prefix": "816d945741dca246099388ca3eed74fc0667acbaa36f70b559b2494c3979b1f6", "size_in_bytes": 1500}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/__pycache__/context.cpython-311.pyc", "path_type": "hardlink", "sha256": "29602037d3a58a8f9d65a905df4570ee5bbe0e433607b66bb22af0f2289621df", "sha256_in_prefix": "29602037d3a58a8f9d65a905df4570ee5bbe0e433607b66bb22af0f2289621df", "size_in_bytes": 15654}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/collections/__init__.py", "path_type": "hardlink", "sha256": "3dcd7e4aa8d69bcd5a7753f4f86b6da64c0efcb5a59da63a814abc81c2a1dafd", "sha256_in_prefix": "3dcd7e4aa8d69bcd5a7753f4f86b6da64c0efcb5a59da63a814abc81c2a1dafd", "size_in_bytes": 26640}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/collections/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "51ec4b192e68f92506faaeb07ee3e5bf2c0d49e0e282fce87a35584e8fb2e50c", "sha256_in_prefix": "51ec4b192e68f92506faaeb07ee3e5bf2c0d49e0e282fce87a35584e8fb2e50c", "size_in_bytes": 44292}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/collections/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/context.py", "path_type": "hardlink", "sha256": "444a0b2310e43b931f118a30b7f5a8ba9342468c414b9bfb617d8f6d6f2521cb", "sha256_in_prefix": "444a0b2310e43b931f118a30b7f5a8ba9342468c414b9bfb617d8f6d6f2521cb", "size_in_bytes": 9552}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.py", "path_type": "hardlink", "sha256": "844009692dae49946e17f258e02c421c8621efd669c5a3e9f4e887cabf44275c", "sha256_in_prefix": "844009692dae49946e17f258e02c421c8621efd669c5a3e9f4e887cabf44275c", "size_in_bytes": 16642}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.pyi", "path_type": "hardlink", "sha256": "824dddb201f3a3917f53be07cc0be9362bc500f0a43c9d5bdbec8277ad9d7e7c", "sha256_in_prefix": "824dddb201f3a3917f53be07cc0be9362bc500f0a43c9d5bdbec8277ad9d7e7c", "size_in_bytes": 3878}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "43c8a533af1864f21f0858aac8813a2508bed4b5a5765bf5c79a32aba977e0ea", "sha256_in_prefix": "43c8a533af1864f21f0858aac8813a2508bed4b5a5765bf5c79a32aba977e0ea", "size_in_bytes": 24345}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/Lorem ipsum.txt", "path_type": "hardlink", "sha256": "37fedcffbf73c4eb9f058f47677cb33203a436ff9390e4d38a8e01c9dad28e0b", "sha256_in_prefix": "37fedcffbf73c4eb9f058f47677cb33203a436ff9390e4d38a8e01c9dad28e0b", "size_in_bytes": 1335}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__init__.py", "path_type": "hardlink", "sha256": "636614a9747fa2b5280da6384a43d17ba65985be4750707f021f5108db15ca1a", "sha256_in_prefix": "636614a9747fa2b5280da6384a43d17ba65985be4750707f021f5108db15ca1a", "size_in_bytes": 16250}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "3014f2965f20ae8cc2df6bf2225c7d685211de06c4b42e28cbee053cf006d4aa", "sha256_in_prefix": "3014f2965f20ae8cc2df6bf2225c7d685211de06c4b42e28cbee053cf006d4aa", "size_in_bytes": 27748}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/layouts.cpython-311.pyc", "path_type": "hardlink", "sha256": "cd313907ebba837604823835c8e7d134bd052b0d07a1a603121b16a0a701befd", "sha256_in_prefix": "cd313907ebba837604823835c8e7d134bd052b0d07a1a603121b16a0a701befd", "size_in_bytes": 1183}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/show-newlines.cpython-311.pyc", "path_type": "hardlink", "sha256": "6dadfc0c0c7a72c804c64fffa14726efba117587d9d93d3b6865cbfffbf986b7", "sha256_in_prefix": "6dadfc0c0c7a72c804c64fffa14726efba117587d9d93d3b6865cbfffbf986b7", "size_in_bytes": 1607}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/strip-prefix.cpython-311.pyc", "path_type": "hardlink", "sha256": "01b7a8ed8a719e5d17cb79c763ecc146c85e2676643f4f848106bd9aefa06921", "sha256_in_prefix": "01b7a8ed8a719e5d17cb79c763ecc146c85e2676643f4f848106bd9aefa06921", "size_in_bytes": 916}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-dvorak.cpython-311.pyc", "path_type": "hardlink", "sha256": "df49a5ba94506ebd8b4eac4981ed257675da5253e57c65076d1f5dbf5de9458a", "sha256_in_prefix": "df49a5ba94506ebd8b4eac4981ed257675da5253e57c65076d1f5dbf5de9458a", "size_in_bytes": 420}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-qwerty.cpython-311.pyc", "path_type": "hardlink", "sha256": "b1bc439398c5ed2e1ed41240f9ae8d8049b0f1424befc2b31381aa8be627cda1", "sha256_in_prefix": "b1bc439398c5ed2e1ed41240f9ae8d8049b0f1424befc2b31381aa8be627cda1", "size_in_bytes": 420}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/layouts.py", "path_type": "hardlink", "sha256": "1d30bc6924cb67bb978a9c8e5daa51302d79f23b9e7232ba455c22b5f999f7fc", "sha256_in_prefix": "1d30bc6924cb67bb978a9c8e5daa51302d79f23b9e7232ba455c22b5f999f7fc", "size_in_bytes": 643}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/show-newlines.py", "path_type": "hardlink", "sha256": "58641aeb97bc97285bf762d438ba959fa29a0ada1e560570b3a5ad49198b93ac", "sha256_in_prefix": "58641aeb97bc97285bf762d438ba959fa29a0ada1e560570b3a5ad49198b93ac", "size_in_bytes": 904}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py", "path_type": "hardlink", "sha256": "35f55757c255368ea7a9cb980127cc57bff2e04a3cccc42a942386bc09d1215c", "sha256_in_prefix": "35f55757c255368ea7a9cb980127cc57bff2e04a3cccc42a942386bc09d1215c", "size_in_bytes": 412}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py", "path_type": "hardlink", "sha256": "d5235c6d2b2f212a575e0f8b9f26c81c763e45414e42bdfacdc1e4635a5616ac", "sha256_in_prefix": "d5235c6d2b2f212a575e0f8b9f26c81c763e45414e42bdfacdc1e4635a5616ac", "size_in_bytes": 119}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py", "path_type": "hardlink", "sha256": "b3850c4149cfc059ff741e6e642dbb06eac7390718a277417f322954be69133c", "sha256_in_prefix": "b3850c4149cfc059ff741e6e642dbb06eac7390718a277417f322954be69133c", "size_in_bytes": 119}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "09f1c8c9e941af3e584d59641ea9b87d83c0cb0fd007eb5ef391a7e2643c1a46", "sha256_in_prefix": "09f1c8c9e941af3e584d59641ea9b87d83c0cb0fd007eb5ef391a7e2643c1a46", "size_in_bytes": 1053}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "0453bdd0ef9f2cd89540ca63ee8212e73b73809514419dd3037d8fe471f737e0", "sha256_in_prefix": "0453bdd0ef9f2cd89540ca63ee8212e73b73809514419dd3037d8fe471f737e0", "size_in_bytes": 36293}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "77c8e73e018dc0fd7e9ed6c80b05a4404545f641fb085220ce42b368b59aa3d3", "sha256_in_prefix": "77c8e73e018dc0fd7e9ed6c80b05a4404545f641fb085220ce42b368b59aa3d3", "size_in_bytes": 1259}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "ad282afc9a4717d7c7475971e77ab083fd7ed8bca9644fea99cb976d552af78f", "sha256_in_prefix": "ad282afc9a4717d7c7475971e77ab083fd7ed8bca9644fea99cb976d552af78f", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.py", "path_type": "hardlink", "sha256": "76d01b1a34c39a7fe08625396177e1c83cb4a610255d576de649590397d46be4", "sha256_in_prefix": "76d01b1a34c39a7fe08625396177e1c83cb4a610255d576de649590397d46be4", "size_in_bytes": 149}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.pyi", "path_type": "hardlink", "sha256": "e41dde4f338dd4106e38ba1bd6f09f97211bda549deaeb17410f82bfe85791e0", "sha256_in_prefix": "e41dde4f338dd4106e38ba1bd6f09f97211bda549deaeb17410f82bfe85791e0", "size_in_bytes": 43}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "d527ce3f906cb84ac2a99060d57d5c27de5813e4832c6db570201a89b721dee4", "sha256_in_prefix": "d527ce3f906cb84ac2a99060d57d5c27de5813e4832c6db570201a89b721dee4", "size_in_bytes": 337}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/more.cpython-311.pyc", "path_type": "hardlink", "sha256": "1f57652f9e271c9d787ab57a2560ac699a41645dc7503013f1d6e94c3b8be9b2", "sha256_in_prefix": "1f57652f9e271c9d787ab57a2560ac699a41645dc7503013f1d6e94c3b8be9b2", "size_in_bytes": 189622}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/recipes.cpython-311.pyc", "path_type": "hardlink", "sha256": "84657b6a430adbf1f460fac4b241e771911ff72fbe76433c73ced8d4da0cc737", "sha256_in_prefix": "84657b6a430adbf1f460fac4b241e771911ff72fbe76433c73ced8d4da0cc737", "size_in_bytes": 39749}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/more.py", "path_type": "hardlink", "sha256": "d44e64cc59dc44a4c3c34718bf5c915cc80376e9ecb4b41dd504ad7272fa53dd", "sha256_in_prefix": "d44e64cc59dc44a4c3c34718bf5c915cc80376e9ecb4b41dd504ad7272fa53dd", "size_in_bytes": 148370}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/more.pyi", "path_type": "hardlink", "sha256": "8975deaade3c3717bc5469885a99155ee2a947615836ebb60d4f2740b5820aed", "sha256_in_prefix": "8975deaade3c3717bc5469885a99155ee2a947615836ebb60d4f2740b5820aed", "size_in_bytes": 21484}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.py", "path_type": "hardlink", "sha256": "59e76185f846560aface28bc7c86c62559914f0d1929188b2a76010c626fe276", "sha256_in_prefix": "59e76185f846560aface28bc7c86c62559914f0d1929188b2a76010c626fe276", "size_in_bytes": 28591}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.pyi", "path_type": "hardlink", "sha256": "4ff99d1a970575facfdc94966f0cd83fd272355f86a3eed13dfa717dfb405a50", "sha256_in_prefix": "4ff99d1a970575facfdc94966f0cd83fd272355f86a3eed13dfa717dfb405a50", "size_in_bytes": 4617}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.1.dist-info/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.1.dist-info/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "5f7a283b75a709fccd481aea42379f083d4f3801753365922e6b0732042515d9", "sha256_in_prefix": "5f7a283b75a709fccd481aea42379f083d4f3801753365922e6b0732042515d9", "size_in_bytes": 3204}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "38a6898306293627c81e2b2d8a93e5f6857d5f7edb73f0334e8d9a53dad53b6e", "sha256_in_prefix": "38a6898306293627c81e2b2d8a93e5f6857d5f7edb73f0334e8d9a53dad53b6e", "size_in_bytes": 2565}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__init__.py", "path_type": "hardlink", "sha256": "76dc366cd996090f569cca0addb93f7a52f5b2f4a58a45ed2e9661085201f521", "sha256_in_prefix": "76dc366cd996090f569cca0addb93f7a52f5b2f4a58a45ed2e9661085201f521", "size_in_bytes": 496}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "1eddc7f635b878734bbc0c4ab7db3eaf966dec33679c8f8dacc67b10d97712d2", "sha256_in_prefix": "1eddc7f635b878734bbc0c4ab7db3eaf966dec33679c8f8dacc67b10d97712d2", "size_in_bytes": 526}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_elffile.cpython-311.pyc", "path_type": "hardlink", "sha256": "e5a1a16722f4ad52154afa009387c94a7632a408fcf7e450b90c6a3fb0bd0d54", "sha256_in_prefix": "e5a1a16722f4ad52154afa009387c94a7632a408fcf7e450b90c6a3fb0bd0d54", "size_in_bytes": 5423}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_manylinux.cpython-311.pyc", "path_type": "hardlink", "sha256": "e4537aa40177bf74f91d507b85bf98e68e655e1d4aeae5db838a8f4f558d070b", "sha256_in_prefix": "e4537aa40177bf74f91d507b85bf98e68e655e1d4aeae5db838a8f4f558d070b", "size_in_bytes": 10887}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_musllinux.cpython-311.pyc", "path_type": "hardlink", "sha256": "def62f7a8f7eb938fa0407a6aa4dc5d0317ac5e96e29d0f1460be701edf53547", "sha256_in_prefix": "def62f7a8f7eb938fa0407a6aa4dc5d0317ac5e96e29d0f1460be701edf53547", "size_in_bytes": 5278}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_parser.cpython-311.pyc", "path_type": "hardlink", "sha256": "bb513f7591a7ec1bfe6fdeca20d8ba7d40aebd775f1452d5e4e34967932239c4", "sha256_in_prefix": "bb513f7591a7ec1bfe6fdeca20d8ba7d40aebd775f1452d5e4e34967932239c4", "size_in_bytes": 16250}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_structures.cpython-311.pyc", "path_type": "hardlink", "sha256": "49b7b3a04539f6623ac6621b5e1744a8e451ca5f86b718e492903e16b109121f", "sha256_in_prefix": "49b7b3a04539f6623ac6621b5e1744a8e451ca5f86b718e492903e16b109121f", "size_in_bytes": 3651}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_tokenizer.cpython-311.pyc", "path_type": "hardlink", "sha256": "5aaad227100f45cdea2497c288ff0aafdb698293914ae7b703b54a5296d7b79d", "sha256_in_prefix": "5aaad227100f45cdea2497c288ff0aafdb698293914ae7b703b54a5296d7b79d", "size_in_bytes": 8515}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/markers.cpython-311.pyc", "path_type": "hardlink", "sha256": "a50239e2cffb8992b4e7d90b93f2969670967ec619da5f86f766b79ec2e26508", "sha256_in_prefix": "a50239e2cffb8992b4e7d90b93f2969670967ec619da5f86f766b79ec2e26508", "size_in_bytes": 12739}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/metadata.cpython-311.pyc", "path_type": "hardlink", "sha256": "32542fc9f9ff7270783a17c3760b2c97d20876d74a6a0880235418f34568eb68", "sha256_in_prefix": "32542fc9f9ff7270783a17c3760b2c97d20876d74a6a0880235418f34568eb68", "size_in_bytes": 28506}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/requirements.cpython-311.pyc", "path_type": "hardlink", "sha256": "7f397f4ea3a6ad9aa6ea57ec15dc0e4121f70d2ad9bb5451bf100c9cbccddb9e", "sha256_in_prefix": "7f397f4ea3a6ad9aa6ea57ec15dc0e4121f70d2ad9bb5451bf100c9cbccddb9e", "size_in_bytes": 4692}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/specifiers.cpython-311.pyc", "path_type": "hardlink", "sha256": "8393ffc72f4918d4f9d9ac0e1da42549d6542ec70f64268bb3a06c81043fde30", "sha256_in_prefix": "8393ffc72f4918d4f9d9ac0e1da42549d6542ec70f64268bb3a06c81043fde30", "size_in_bytes": 41203}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/tags.cpython-311.pyc", "path_type": "hardlink", "sha256": "7bf4fdcc93462f6108098b2f1982bd365500d31f342ba1a7c5d90761084b69d8", "sha256_in_prefix": "7bf4fdcc93462f6108098b2f1982bd365500d31f342ba1a7c5d90761084b69d8", "size_in_bytes": 24046}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "a99ce20f492d955f37a13f0107ab299e3b6e39b1041b1feb42882597fc461218", "sha256_in_prefix": "a99ce20f492d955f37a13f0107ab299e3b6e39b1041b1feb42882597fc461218", "size_in_bytes": 8279}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/version.cpython-311.pyc", "path_type": "hardlink", "sha256": "9b4dc65a5c14ffec872d099b5e8aa08734e86dcaa9aec84ccb339a50b91a303e", "sha256_in_prefix": "9b4dc65a5c14ffec872d099b5e8aa08734e86dcaa9aec84ccb339a50b91a303e", "size_in_bytes": 20785}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_elffile.py", "path_type": "hardlink", "sha256": "fcb7095b860d2b2c18b25e35ebd076ba4291ab0c63c6cb7ff07d0545540a973f", "sha256_in_prefix": "fcb7095b860d2b2c18b25e35ebd076ba4291ab0c63c6cb7ff07d0545540a973f", "size_in_bytes": 3282}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "5e8e15d0f673f2c6ee5426d39e2d2dd424740077a2affee26f8953995f2c703e", "sha256_in_prefix": "5e8e15d0f673f2c6ee5426d39e2d2dd424740077a2affee26f8953995f2c703e", "size_in_bytes": 9586}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "sha256_in_prefix": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "size_in_bytes": 2694}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_parser.py", "path_type": "hardlink", "sha256": "b3f4ef4ef0cd2b436b336401dd529385d58533835cd0fe899e439b925dcc8e93", "sha256_in_prefix": "b3f4ef4ef0cd2b436b336401dd529385d58533835cd0fe899e439b925dcc8e93", "size_in_bytes": 10236}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "27abf91fb273bdbfa0f35c69ff640008ac0eecbc47400ea292bc8c53bcd7c0df", "sha256_in_prefix": "27abf91fb273bdbfa0f35c69ff640008ac0eecbc47400ea292bc8c53bcd7c0df", "size_in_bytes": 5273}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/markers.py", "path_type": "hardlink", "sha256": "756292aa7e52a7e8c398e1be5b719f2c72a3c217f522cce76d3ef55650680793", "sha256_in_prefix": "756292aa7e52a7e8c398e1be5b719f2c72a3c217f522cce76d3ef55650680793", "size_in_bytes": 10671}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/metadata.py", "path_type": "hardlink", "sha256": "28836e4a4275daef92ca828d4f2fe91cd1807cc52dc4dbd9e77a80d7300a70a2", "sha256_in_prefix": "28836e4a4275daef92ca828d4f2fe91cd1807cc52dc4dbd9e77a80d7300a70a2", "size_in_bytes": 32349}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/requirements.py", "path_type": "hardlink", "sha256": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "sha256_in_prefix": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "size_in_bytes": 2947}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/specifiers.py", "path_type": "hardlink", "sha256": "ae3a5cde1a09bae9d1213e83747ee02d39d0e63e50292b968e84c2e6c747b472", "sha256_in_prefix": "ae3a5cde1a09bae9d1213e83747ee02d39d0e63e50292b968e84c2e6c747b472", "size_in_bytes": 39714}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/tags.py", "path_type": "hardlink", "sha256": "cbc11b85e3aef564bbb3e31e6da5cc707305fa3cec03f0b52f3e57453892cb8c", "sha256_in_prefix": "cbc11b85e3aef564bbb3e31e6da5cc707305fa3cec03f0b52f3e57453892cb8c", "size_in_bytes": 18883}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/utils.py", "path_type": "hardlink", "sha256": "3407585309e500ea646adfd1b616af5fc6b4ed8b95c6018bfefc2bc7bdc64833", "sha256_in_prefix": "3407585309e500ea646adfd1b616af5fc6b4ed8b95c6018bfefc2bc7bdc64833", "size_in_bytes": 5287}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/version.py", "path_type": "hardlink", "sha256": "5741f748e8fff307abaefa2badbe900cb46195ca9212b3534e40afbdfb338432", "sha256_in_prefix": "5741f748e8fff307abaefa2badbe900cb46195ca9212b3534e40afbdfb338432", "size_in_bytes": 16198}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "ce6b227b4d46d4cb57474c2022fe57a557933bb89daf4596bdf9b12ac296b869", "sha256_in_prefix": "ce6b227b4d46d4cb57474c2022fe57a557933bb89daf4596bdf9b12ac296b869", "size_in_bytes": 11429}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "4c211d76d42ed40efc3acfcc866d8912a718afbca2b7e51849442366d6e99fe8", "sha256_in_prefix": "4c211d76d42ed40efc3acfcc866d8912a718afbca2b7e51849442366d6e99fe8", "size_in_bytes": 1642}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cc431c46bf4aaf4df1d68cc6c20e6ff4d4012a7de49dda7a2d2a1295583e8e15", "sha256_in_prefix": "cc431c46bf4aaf4df1d68cc6c20e6ff4d4012a7de49dda7a2d2a1295583e8e15", "size_in_bytes": 87}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "sha256_in_prefix": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "size_in_bytes": 1089}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__init__.py", "path_type": "hardlink", "sha256": "10c184f2a787451f42cc316bf242f7b40f217596678988d705565dd1419358ad", "sha256_in_prefix": "10c184f2a787451f42cc316bf242f7b40f217596678988d705565dd1419358ad", "size_in_bytes": 22225}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__main__.py", "path_type": "hardlink", "sha256": "1e7b14407a6205a893c70726c15c3e9c568f755359b5021d8b57960ed23e3332", "sha256_in_prefix": "1e7b14407a6205a893c70726c15c3e9c568f755359b5021d8b57960ed23e3332", "size_in_bytes": 1493}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "06d56bb4ddd07a93169b62d97eec557e19e355a68c4fbe4249c4d28ff715ec85", "sha256_in_prefix": "06d56bb4ddd07a93169b62d97eec557e19e355a68c4fbe4249c4d28ff715ec85", "size_in_bytes": 19110}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "6d76a0759040f255754d0ac27a21d95bc0ae2869ea23e01071557190068f60b4", "sha256_in_prefix": "6d76a0759040f255754d0ac27a21d95bc0ae2869ea23e01071557190068f60b4", "size_in_bytes": 2252}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/android.cpython-311.pyc", "path_type": "hardlink", "sha256": "6735b40b4e25b2f58235e4d410647033dfda57a91e26562155dd32cc86a726fa", "sha256_in_prefix": "6735b40b4e25b2f58235e4d410647033dfda57a91e26562155dd32cc86a726fa", "size_in_bytes": 11864}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/api.cpython-311.pyc", "path_type": "hardlink", "sha256": "bbd8e40ac15e967ebc4bff54564302e985f6704a5ae3d976edc8e82d662a2639", "sha256_in_prefix": "bbd8e40ac15e967ebc4bff54564302e985f6704a5ae3d976edc8e82d662a2639", "size_in_bytes": 14076}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/macos.cpython-311.pyc", "path_type": "hardlink", "sha256": "701ad0955f6c990fda00cc12a30d4ede9568ffa864ef83f20493bd98b65926d5", "sha256_in_prefix": "701ad0955f6c990fda00cc12a30d4ede9568ffa864ef83f20493bd98b65926d5", "size_in_bytes": 8485}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/unix.cpython-311.pyc", "path_type": "hardlink", "sha256": "87b8d3784837615c3d44b5440537b7777ee908a053924a99667674f0ac1a0d3a", "sha256_in_prefix": "87b8d3784837615c3d44b5440537b7777ee908a053924a99667674f0ac1a0d3a", "size_in_bytes": 16529}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/version.cpython-311.pyc", "path_type": "hardlink", "sha256": "518089fe46c9a0949ec4f2a42f4f0dc43a608b2cea51998a532160e3a0810cd7", "sha256_in_prefix": "518089fe46c9a0949ec4f2a42f4f0dc43a608b2cea51998a532160e3a0810cd7", "size_in_bytes": 627}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/windows.cpython-311.pyc", "path_type": "hardlink", "sha256": "9d8c8320869546462b408c89c2ec8eab2eb48a872973edaff8e3262246b4bb6c", "sha256_in_prefix": "9d8c8320869546462b408c89c2ec8eab2eb48a872973edaff8e3262246b4bb6c", "size_in_bytes": 14670}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/android.py", "path_type": "hardlink", "sha256": "c595d8f49778e963acc53d94ebee47b0db4367e210ab170452b04b977858938a", "sha256_in_prefix": "c595d8f49778e963acc53d94ebee47b0db4367e210ab170452b04b977858938a", "size_in_bytes": 9016}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/api.py", "path_type": "hardlink", "sha256": "40161d51a736782e76d5e93fcb9dee0f50dcabe9495fc22049155de089c2eae7", "sha256_in_prefix": "40161d51a736782e76d5e93fcb9dee0f50dcabe9495fc22049155de089c2eae7", "size_in_bytes": 8996}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/macos.py", "path_type": "hardlink", "sha256": "c1fb6c6ecbeaea767458e4574a20ab64d9111f3fd62ae92d9746ba982ecc1642", "sha256_in_prefix": "c1fb6c6ecbeaea767458e4574a20ab64d9111f3fd62ae92d9746ba982ecc1642", "size_in_bytes": 5580}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/unix.py", "path_type": "hardlink", "sha256": "09c8bd5aab77e5d00cb20e874fd9d11874815b9a1b6f4a51dc01352499ec0978", "sha256_in_prefix": "09c8bd5aab77e5d00cb20e874fd9d11874815b9a1b6f4a51dc01352499ec0978", "size_in_bytes": 10643}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/version.py", "path_type": "hardlink", "sha256": "afb17bead6518e040aceba71fc8d3f64c40e314f8f4bb7869c70fbcc42b7281d", "sha256_in_prefix": "afb17bead6518e040aceba71fc8d3f64c40e314f8f4bb7869c70fbcc42b7281d", "size_in_bytes": 411}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/windows.py", "path_type": "hardlink", "sha256": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "sha256_in_prefix": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "size_in_bytes": 10125}, {"_path": "Lib/site-packages/setuptools/_vendor/ruff.toml", "path_type": "hardlink", "sha256": "5d695f64b5cbc4ce0efe8b0a4199b8cce2650a69caa65d96d3965bc73d09ba77", "sha256_in_prefix": "5d695f64b5cbc4ce0efe8b0a4199b8cce2650a69caa65d96d3965bc73d09ba77", "size_in_bytes": 16}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "sha256_in_prefix": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "size_in_bytes": 1072}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "ccf0dc78a98fc0918b5ad67292b1e2c4bed65575a6246cd9d63c914f9942a0f2", "sha256_in_prefix": "ccf0dc78a98fc0918b5ad67292b1e2c4bed65575a6246cd9d63c914f9942a0f2", "size_in_bytes": 8875}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "0cb9f9a451a1e365ac54b4c88662e1da0cb54a72d16a5258fb0abff9d3e1c022", "sha256_in_prefix": "0cb9f9a451a1e365ac54b4c88662e1da0cb54a72d16a5258fb0abff9d3e1c022", "size_in_bytes": 999}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "sha256_in_prefix": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__init__.py", "path_type": "hardlink", "sha256": "26153057ae830758381efb7551009531d7c2bbe220015f055e6bc353da27c5de", "sha256_in_prefix": "26153057ae830758381efb7551009531d7c2bbe220015f055e6bc353da27c5de", "size_in_bytes": 396}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "e37f642a5fbc66dca7e9e625d545812a59afbe801734e7130dc3f14eef13495e", "sha256_in_prefix": "e37f642a5fbc66dca7e9e625d545812a59afbe801734e7130dc3f14eef13495e", "size_in_bytes": 379}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-311.pyc", "path_type": "hardlink", "sha256": "1b2678ab1edf4032eae0b5904209f91ad708321b70d8b6ae5dad34d08fff9796", "sha256_in_prefix": "1b2678ab1edf4032eae0b5904209f91ad708321b70d8b6ae5dad34d08fff9796", "size_in_bytes": 30818}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_re.cpython-311.pyc", "path_type": "hardlink", "sha256": "8501187fe202f421b7a94fe5f940bf2b796396c0cf1b726215e3a3e90268d2ae", "sha256_in_prefix": "8501187fe202f421b7a94fe5f940bf2b796396c0cf1b726215e3a3e90268d2ae", "size_in_bytes": 4458}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_types.cpython-311.pyc", "path_type": "hardlink", "sha256": "ca7d9ceae79d0c936a9ed21330f5435b3d366246f4bd09ecf790c80a1de90d04", "sha256_in_prefix": "ca7d9ceae79d0c936a9ed21330f5435b3d366246f4bd09ecf790c80a1de90d04", "size_in_bytes": 371}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/_parser.py", "path_type": "hardlink", "sha256": "83df8435a00b4be07c768918a42bb35056a55a5a20ed3f922183232d9496aed3", "sha256_in_prefix": "83df8435a00b4be07c768918a42bb35056a55a5a20ed3f922183232d9496aed3", "size_in_bytes": 22633}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/_re.py", "path_type": "hardlink", "sha256": "75b8e0e428594f6dca6bdcfd0c73977ddb52a4fc147dd80c5e78fc34ea25cbec", "sha256_in_prefix": "75b8e0e428594f6dca6bdcfd0c73977ddb52a4fc147dd80c5e78fc34ea25cbec", "size_in_bytes": 2943}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/_types.py", "path_type": "hardlink", "sha256": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "sha256_in_prefix": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "size_in_bytes": 254}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/py.typed", "path_type": "hardlink", "sha256": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "sha256_in_prefix": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "size_in_bytes": 26}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "6163f7987dfb38d6bc320ce2b70b2f02b862bc41126516d552ef1cd43247e758", "sha256_in_prefix": "6163f7987dfb38d6bc320ce2b70b2f02b862bc41126516d552ef1cd43247e758", "size_in_bytes": 1130}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "cf675c1c0a744f08580855390de87cc77d676b312582e8d4cfdb5bb8fd298d21", "sha256_in_prefix": "cf675c1c0a744f08580855390de87cc77d676b312582e8d4cfdb5bb8fd298d21", "size_in_bytes": 3717}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "48a51959582478352275428ceecd78ef77d79ac9dae796e39a2eaf2540282552", "sha256_in_prefix": "48a51959582478352275428ceecd78ef77d79ac9dae796e39a2eaf2540282552", "size_in_bytes": 2402}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "aa9ecd43568bb624a0310aa8ea05a57c6a72d08217ce830999e4132e9cea1594", "sha256_in_prefix": "aa9ecd43568bb624a0310aa8ea05a57c6a72d08217ce830999e4132e9cea1594", "size_in_bytes": 48}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "e33dbc021b83a1dc114bf73527f97c1f9d6de50bb07d3b1eb24633971a7a82bb", "sha256_in_prefix": "e33dbc021b83a1dc114bf73527f97c1f9d6de50bb07d3b1eb24633971a7a82bb", "size_in_bytes": 10}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__init__.py", "path_type": "hardlink", "sha256": "3a7878c37f1e94f0a3b65714dc963d93787bd0d8ecc5722401f966427f99d056", "sha256_in_prefix": "3a7878c37f1e94f0a3b65714dc963d93787bd0d8ecc5722401f966427f99d056", "size_in_bytes": 2071}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "9df688827595bccf5e64907f367666b82090a1c1ff563d79a3f7e180a1f270b1", "sha256_in_prefix": "9df688827595bccf5e64907f367666b82090a1c1ff563d79a3f7e180a1f270b1", "size_in_bytes": 2538}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_checkers.cpython-311.pyc", "path_type": "hardlink", "sha256": "50653236adfef5f64527d3b6bf01975674f402d3bf93aa1c733be04af9b85126", "sha256_in_prefix": "50653236adfef5f64527d3b6bf01975674f402d3bf93aa1c733be04af9b85126", "size_in_bytes": 38445}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_config.cpython-311.pyc", "path_type": "hardlink", "sha256": "8b46cdede170d013010913f6a8c683c24d53d11ef9b4bad2d25202000abc0e33", "sha256_in_prefix": "8b46cdede170d013010913f6a8c683c24d53d11ef9b4bad2d25202000abc0e33", "size_in_bytes": 4246}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_decorators.cpython-311.pyc", "path_type": "hardlink", "sha256": "7e61c729ea00f20e9f618c4eee02ecc5288b77d8f5b712501886b124efd6fe7d", "sha256_in_prefix": "7e61c729ea00f20e9f618c4eee02ecc5288b77d8f5b712501886b124efd6fe7d", "size_in_bytes": 11319}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_exceptions.cpython-311.pyc", "path_type": "hardlink", "sha256": "b67131ac84773dca7d765f0b5c5af2947d3cca0f90b766fac29070e6fc7920b2", "sha256_in_prefix": "b67131ac84773dca7d765f0b5c5af2947d3cca0f90b766fac29070e6fc7920b2", "size_in_bytes": 3292}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_functions.cpython-311.pyc", "path_type": "hardlink", "sha256": "6baa6cb1fdcfaacc6093a4163c05b61de4ea24069043bb6571683fb9dd33743d", "sha256_in_prefix": "6baa6cb1fdcfaacc6093a4163c05b61de4ea24069043bb6571683fb9dd33743d", "size_in_bytes": 12975}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_importhook.cpython-311.pyc", "path_type": "hardlink", "sha256": "1914c0b01cc2da0afcd98b264cb12172ec3681a7a798d319411291286f401592", "sha256_in_prefix": "1914c0b01cc2da0afcd98b264cb12172ec3681a7a798d319411291286f401592", "size_in_bytes": 10354}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_memo.cpython-311.pyc", "path_type": "hardlink", "sha256": "bcd703821faf7adfa7822d18660f4e23011c3367c4451fc9402e70076ef7f5aa", "sha256_in_prefix": "bcd703821faf7adfa7822d18660f4e23011c3367c4451fc9402e70076ef7f5aa", "size_in_bytes": 1848}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_pytest_plugin.cpython-311.pyc", "path_type": "hardlink", "sha256": "02a0eb0907efeec935f3771323772d84a015254690637cfcc341c29b211408a5", "sha256_in_prefix": "02a0eb0907efeec935f3771323772d84a015254690637cfcc341c29b211408a5", "size_in_bytes": 6477}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_suppression.cpython-311.pyc", "path_type": "hardlink", "sha256": "84b56fed83f4ba0b1cb651cc3f2df1fdb0d9bff7f0fdef94a4415b11ad579a0c", "sha256_in_prefix": "84b56fed83f4ba0b1cb651cc3f2df1fdb0d9bff7f0fdef94a4415b11ad579a0c", "size_in_bytes": 4267}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_transformer.cpython-311.pyc", "path_type": "hardlink", "sha256": "a1dd910621e1aba801d6d56a2e6b462764dc9579218f85a7eb890759421f77ec", "sha256_in_prefix": "a1dd910621e1aba801d6d56a2e6b462764dc9579218f85a7eb890759421f77ec", "size_in_bytes": 55661}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_union_transformer.cpython-311.pyc", "path_type": "hardlink", "sha256": "8a338fe27f458788fea411aa9d2f605471d63ef3115182bcd8f448a86d1a1f84", "sha256_in_prefix": "8a338fe27f458788fea411aa9d2f605471d63ef3115182bcd8f448a86d1a1f84", "size_in_bytes": 2889}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "77db13d3ab334f0f5af655b4290c2d5fed8e3a16c38f9958e651c8a16afbee80", "sha256_in_prefix": "77db13d3ab334f0f5af655b4290c2d5fed8e3a16c38f9958e651c8a16afbee80", "size_in_bytes": 8564}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_checkers.py", "path_type": "hardlink", "sha256": "251ae02a271d3847c8068344b5e81808422586969df9ad6ed449bb1828f45822", "sha256_in_prefix": "251ae02a271d3847c8068344b5e81808422586969df9ad6ed449bb1828f45822", "size_in_bytes": 31360}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_config.py", "path_type": "hardlink", "sha256": "9c8cfc4300dafa814edcbf4ef3feacaf396677df6949bcb6c0e33859bec5fc1d", "sha256_in_prefix": "9c8cfc4300dafa814edcbf4ef3feacaf396677df6949bcb6c0e33859bec5fc1d", "size_in_bytes": 2846}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_decorators.py", "path_type": "hardlink", "sha256": "bfa76c21e5af3e113118b3ffc1717e4660d4ca365ffc0936f20ee0049fefd3ed", "sha256_in_prefix": "bfa76c21e5af3e113118b3ffc1717e4660d4ca365ffc0936f20ee0049fefd3ed", "size_in_bytes": 9033}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_exceptions.py", "path_type": "hardlink", "sha256": "6483de895f8505de449b0d8469677616f96caf08b8a1cc13d9f54604802d1dc4", "sha256_in_prefix": "6483de895f8505de449b0d8469677616f96caf08b8a1cc13d9f54604802d1dc4", "size_in_bytes": 1121}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_functions.py", "path_type": "hardlink", "sha256": "89b81200a6b9a6d226d5e47d0111b4052a3300524f14d01266a84f57241eaa28", "sha256_in_prefix": "89b81200a6b9a6d226d5e47d0111b4052a3300524f14d01266a84f57241eaa28", "size_in_bytes": 10393}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_importhook.py", "path_type": "hardlink", "sha256": "ba08c20ef15c756314ed4ba0aa5246f7522954da44231b51afef7db3487593b3", "sha256_in_prefix": "ba08c20ef15c756314ed4ba0aa5246f7522954da44231b51afef7db3487593b3", "size_in_bytes": 6389}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_memo.py", "path_type": "hardlink", "sha256": "d63b9057fbf19c3d8960a6d2ade6e242e8f8a0a1f3ea7ebbbfda5803e0822128", "sha256_in_prefix": "d63b9057fbf19c3d8960a6d2ade6e242e8f8a0a1f3ea7ebbbfda5803e0822128", "size_in_bytes": 1303}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py", "path_type": "hardlink", "sha256": "f9f712aa4bf9e2b21f205f290dabd8e5840f923e0e5fc18cb7f94bec24120f82", "sha256_in_prefix": "f9f712aa4bf9e2b21f205f290dabd8e5840f923e0e5fc18cb7f94bec24120f82", "size_in_bytes": 4416}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_suppression.py", "path_type": "hardlink", "sha256": "5507f3c5cc086eede27f47fb54190a33b86460e03bb4d170f5aee3301b26320e", "sha256_in_prefix": "5507f3c5cc086eede27f47fb54190a33b86460e03bb4d170f5aee3301b26320e", "size_in_bytes": 2266}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_transformer.py", "path_type": "hardlink", "sha256": "f476bbfd085dc285278bfea1bdd63e8596ee11eae0a301eb34bdafcc721a9056", "sha256_in_prefix": "f476bbfd085dc285278bfea1bdd63e8596ee11eae0a301eb34bdafcc721a9056", "size_in_bytes": 44937}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_union_transformer.py", "path_type": "hardlink", "sha256": "bffe36afbfba1ee457d92a05c27c89f84e4f9715e955e5093c9475f8753da92a", "sha256_in_prefix": "bffe36afbfba1ee457d92a05c27c89f84e4f9715e955e5093c9475f8753da92a", "size_in_bytes": 1354}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_utils.py", "path_type": "hardlink", "sha256": "e4784ed6b3e7e5fd4ceb29a454012fed79a5cf5717fa3d0e9d3325c87aaaad1f", "sha256_in_prefix": "e4784ed6b3e7e5fd4ceb29a454012fed79a5cf5717fa3d0e9d3325c87aaaad1f", "size_in_bytes": 5270}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "sha256_in_prefix": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "size_in_bytes": 13936}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "05e51021af1c9d86eb8d6c7e37c4cece733d5065b91a6d8389c5690ed440f16d", "sha256_in_prefix": "05e51021af1c9d86eb8d6c7e37c4cece733d5065b91a6d8389c5690ed440f16d", "size_in_bytes": 3018}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "7710002d81971e632aa6a2fc33dc5d74aaf5d7caae22040a65d3e31503b05ee9", "sha256_in_prefix": "7710002d81971e632aa6a2fc33dc5d74aaf5d7caae22040a65d3e31503b05ee9", "size_in_bytes": 571}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions.py", "path_type": "hardlink", "sha256": "8307a4a721bd0d51b797158a5f89e2f2eee793759ee6c946f7c980f45dc3250c", "sha256_in_prefix": "8307a4a721bd0d51b797158a5f89e2f2eee793759ee6c946f7c980f45dc3250c", "size_in_bytes": 134451}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "sha256_in_prefix": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "size_in_bytes": 1107}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "59bac22b00a59d3e5608a56b8cf8efc43831a36b72792ee4389c9cd4669c7841", "sha256_in_prefix": "59bac22b00a59d3e5608a56b8cf8efc43831a36b72792ee4389c9cd4669c7841", "size_in_bytes": 2153}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "783e654742611af88cd9f00bf01a431a219db536556e63ff981c7bd673070ac9", "sha256_in_prefix": "783e654742611af88cd9f00bf01a431a219db536556e63ff981c7bd673070ac9", "size_in_bytes": 4557}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "sha256_in_prefix": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "size_in_bytes": 104}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__init__.py", "path_type": "hardlink", "sha256": "0fa8e11f4d1e3336e0ad718078ec157c3e62fa508030cc9cb86d4bd2eb1e0e5a", "sha256_in_prefix": "0fa8e11f4d1e3336e0ad718078ec157c3e62fa508030cc9cb86d4bd2eb1e0e5a", "size_in_bytes": 59}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__main__.py", "path_type": "hardlink", "sha256": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "sha256_in_prefix": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "size_in_bytes": 455}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "e5cf3afaed7b5fa7cfe12331df7a7dd859dd377ff4f770b995b8dc108dd6ca1b", "sha256_in_prefix": "e5cf3afaed7b5fa7cfe12331df7a7dd859dd377ff4f770b995b8dc108dd6ca1b", "size_in_bytes": 251}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "97740595c106919b0a0d46164c4e11d8e62b62a24758300db35b5922be4c697e", "sha256_in_prefix": "97740595c106919b0a0d46164c4e11d8e62b62a24758300db35b5922be4c697e", "size_in_bytes": 1056}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_setuptools_logging.cpython-311.pyc", "path_type": "hardlink", "sha256": "b9e63dabd2585bbc378dc44a6e7b62e189f1d9c376619d1636cc645d08c3095b", "sha256_in_prefix": "b9e63dabd2585bbc378dc44a6e7b62e189f1d9c376619d1636cc645d08c3095b", "size_in_bytes": 1376}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/bdist_wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "b9faa88928299cd64914ae57aa47bec8e9cea788596ec272e9222e6d602f4fcc", "sha256_in_prefix": "b9faa88928299cd64914ae57aa47bec8e9cea788596ec272e9222e6d602f4fcc", "size_in_bytes": 27127}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/macosx_libfile.cpython-311.pyc", "path_type": "hardlink", "sha256": "2634b24cacee2924b991c7d1351454b57d532fbbe661752de1ada57f7984dccb", "sha256_in_prefix": "2634b24cacee2924b991c7d1351454b57d532fbbe661752de1ada57f7984dccb", "size_in_bytes": 17123}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/metadata.cpython-311.pyc", "path_type": "hardlink", "sha256": "fbe7e2dd4685f8baf89a0c3c3de4e363fe9c283b26f8d4d25d5672faa8fe9cbd", "sha256_in_prefix": "fbe7e2dd4685f8baf89a0c3c3de4e363fe9c283b26f8d4d25d5672faa8fe9cbd", "size_in_bytes": 9247}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/util.cpython-311.pyc", "path_type": "hardlink", "sha256": "e43a8caed0aad2737b24deed26b9de7701cc0bb01a7ba0dbde7efa14d00c8787", "sha256_in_prefix": "e43a8caed0aad2737b24deed26b9de7701cc0bb01a7ba0dbde7efa14d00c8787", "size_in_bytes": 1305}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/wheelfile.cpython-311.pyc", "path_type": "hardlink", "sha256": "53a13a23314e9073606118fd1baa44df0268c202d7f660a0362d2b5eaa22562d", "sha256_in_prefix": "53a13a23314e9073606118fd1baa44df0268c202d7f660a0362d2b5eaa22562d", "size_in_bytes": 11453}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/_setuptools_logging.py", "path_type": "hardlink", "sha256": "3680a78c9e03144678e44a3ed817572ec5890b01a46a2b75b69ff5ee96a5795c", "sha256_in_prefix": "3680a78c9e03144678e44a3ed817572ec5890b01a46a2b75b69ff5ee96a5795c", "size_in_bytes": 746}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/bdist_wheel.py", "path_type": "hardlink", "sha256": "38a272a7d13cdf5cc9af1a117e633d0203a30721b5081fa9cc5e645d016668a9", "sha256_in_prefix": "38a272a7d13cdf5cc9af1a117e633d0203a30721b5081fa9cc5e645d016668a9", "size_in_bytes": 20938}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__init__.py", "path_type": "hardlink", "sha256": "7813619cfc164ed74a0091f2efb96fcfb80e43912edc66af1ae817c614ac9fe5", "sha256_in_prefix": "7813619cfc164ed74a0091f2efb96fcfb80e43912edc66af1ae817c614ac9fe5", "size_in_bytes": 4264}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "9b696d6eb7d1f894fef2e8b814b24ceb955ff497bca3a5495acbbdc886c73bea", "sha256_in_prefix": "9b696d6eb7d1f894fef2e8b814b24ceb955ff497bca3a5495acbbdc886c73bea", "size_in_bytes": 7684}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/convert.cpython-311.pyc", "path_type": "hardlink", "sha256": "097e23e70a1a018dfc50c2f776785a48e0220024fa75880b8f05a0473fd566f9", "sha256_in_prefix": "097e23e70a1a018dfc50c2f776785a48e0220024fa75880b8f05a0473fd566f9", "size_in_bytes": 12139}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/pack.cpython-311.pyc", "path_type": "hardlink", "sha256": "4bf19c24dcd00df1864049e28044141b61ff222d72eace6886fc5800700eac15", "sha256_in_prefix": "4bf19c24dcd00df1864049e28044141b61ff222d72eace6886fc5800700eac15", "size_in_bytes": 5832}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/tags.cpython-311.pyc", "path_type": "hardlink", "sha256": "f1dad7bf359e3352cd61a018e20947ef2d83dfe4b220dca2c84605d5be447ac1", "sha256_in_prefix": "f1dad7bf359e3352cd61a018e20947ef2d83dfe4b220dca2c84605d5be447ac1", "size_in_bytes": 7928}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/unpack.cpython-311.pyc", "path_type": "hardlink", "sha256": "937a777aa5ececb64da7a88731ac69f6a41fff2687b9de09ec378feed6605c93", "sha256_in_prefix": "937a777aa5ececb64da7a88731ac69f6a41fff2687b9de09ec378feed6605c93", "size_in_bytes": 1756}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/convert.py", "path_type": "hardlink", "sha256": "a897296062aa75fc353fa05e9603751e7fecb8d80ce9bbf211616565eb925b1d", "sha256_in_prefix": "a897296062aa75fc353fa05e9603751e7fecb8d80ce9bbf211616565eb925b1d", "size_in_bytes": 9439}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/pack.py", "path_type": "hardlink", "sha256": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "sha256_in_prefix": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "size_in_bytes": 3103}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/tags.py", "path_type": "hardlink", "sha256": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "sha256_in_prefix": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "size_in_bytes": 4760}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/unpack.py", "path_type": "hardlink", "sha256": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "sha256_in_prefix": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "size_in_bytes": 1021}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/macosx_libfile.py", "path_type": "hardlink", "sha256": "1e75ba38f74df7dde9b12b6fc25e3dc6dc76930ee1a156deea7bf099ff16b0a2", "sha256_in_prefix": "1e75ba38f74df7dde9b12b6fc25e3dc6dc76930ee1a156deea7bf099ff16b0a2", "size_in_bytes": 16103}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/metadata.py", "path_type": "hardlink", "sha256": "abec420aa4802bb1f3c99c4af40ebf1c05a686a4b5a01e01170d7eac74310624", "sha256_in_prefix": "abec420aa4802bb1f3c99c4af40ebf1c05a686a4b5a01e01170d7eac74310624", "size_in_bytes": 5884}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/util.py", "path_type": "hardlink", "sha256": "7b48e99ec6db33d42169a312c9aa7efd9814c5cc70a722c393a44772b76e3cb8", "sha256_in_prefix": "7b48e99ec6db33d42169a312c9aa7efd9814c5cc70a722c393a44772b76e3cb8", "size_in_bytes": 621}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "a608d1b4dbb83b0524779b31efa2696edade4007c18b15a4f43db7125ca73985", "sha256_in_prefix": "a608d1b4dbb83b0524779b31efa2696edade4007c18b15a4f43db7125ca73985", "size_in_bytes": 172}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "5e0923c2341860f4c6b32bf501cb6c32d0d07dc33e59ca4b8f51e6507fe6e098", "sha256_in_prefix": "5e0923c2341860f4c6b32bf501cb6c32d0d07dc33e59ca4b8f51e6507fe6e098", "size_in_bytes": 182}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_elffile.cpython-311.pyc", "path_type": "hardlink", "sha256": "8df240937774f00ed5f4a4ce02887cf184903fde1a162678a26bf3220f0b83f7", "sha256_in_prefix": "8df240937774f00ed5f4a4ce02887cf184903fde1a162678a26bf3220f0b83f7", "size_in_bytes": 5462}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_manylinux.cpython-311.pyc", "path_type": "hardlink", "sha256": "3c3a7274324bf6aa36d87a24215845b6c8741fd50372da51a4fc039be495e17d", "sha256_in_prefix": "3c3a7274324bf6aa36d87a24215845b6c8741fd50372da51a4fc039be495e17d", "size_in_bytes": 11058}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_musllinux.cpython-311.pyc", "path_type": "hardlink", "sha256": "3f2d05428c7a64a65db14aa9e0ff784a557608aa35e5736308d08b61cdf661de", "sha256_in_prefix": "3f2d05428c7a64a65db14aa9e0ff784a557608aa35e5736308d08b61cdf661de", "size_in_bytes": 5273}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_parser.cpython-311.pyc", "path_type": "hardlink", "sha256": "8273d68c46e415be742b0c447500252eb1010b0a8db1c41e3e76716ee19e903b", "sha256_in_prefix": "8273d68c46e415be742b0c447500252eb1010b0a8db1c41e3e76716ee19e903b", "size_in_bytes": 16296}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_structures.cpython-311.pyc", "path_type": "hardlink", "sha256": "f8a4b2a3847e15866b15e6db379e7c45df3ea51c17c2d0b241fcef940fa761a9", "sha256_in_prefix": "f8a4b2a3847e15866b15e6db379e7c45df3ea51c17c2d0b241fcef940fa761a9", "size_in_bytes": 3666}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-311.pyc", "path_type": "hardlink", "sha256": "217a135b1eb89b02528eb8cb13fc970f86483bc255e7f71849d259688b6aeee5", "sha256_in_prefix": "217a135b1eb89b02528eb8cb13fc970f86483bc255e7f71849d259688b6aeee5", "size_in_bytes": 8643}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/markers.cpython-311.pyc", "path_type": "hardlink", "sha256": "ceabdf4f7d2b2ef839c399d64b97701c0140ab861602aaffc5dd7606b97e4f07", "sha256_in_prefix": "ceabdf4f7d2b2ef839c399d64b97701c0140ab861602aaffc5dd7606b97e4f07", "size_in_bytes": 12031}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/requirements.cpython-311.pyc", "path_type": "hardlink", "sha256": "9eae03a33288d75be554e65eb7933476a577ee7382159d7b36f8bdbbbe01d289", "sha256_in_prefix": "9eae03a33288d75be554e65eb7933476a577ee7382159d7b36f8bdbbbe01d289", "size_in_bytes": 4701}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/specifiers.cpython-311.pyc", "path_type": "hardlink", "sha256": "7a78b3ad410dada9ed5d8f64ccb6f60280762ad23f9ebc82bfc50e999e25fc29", "sha256_in_prefix": "7a78b3ad410dada9ed5d8f64ccb6f60280762ad23f9ebc82bfc50e999e25fc29", "size_in_bytes": 42016}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/tags.cpython-311.pyc", "path_type": "hardlink", "sha256": "cfaef474a855d322ec938eefa98be808ffdf75ae11f312d24d04631a14bcf7ec", "sha256_in_prefix": "cfaef474a855d322ec938eefa98be808ffdf75ae11f312d24d04631a14bcf7ec", "size_in_bytes": 24612}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "bd4fa20547d9a8cc26e4bfeb7508950488df8ffeaa20907ed79ec13134490a72", "sha256_in_prefix": "bd4fa20547d9a8cc26e4bfeb7508950488df8ffeaa20907ed79ec13134490a72", "size_in_bytes": 8257}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/version.cpython-311.pyc", "path_type": "hardlink", "sha256": "bc7a4b0fa474ad3d82487aa70533d2a8449b1e4303b92571f360baa89a6151fa", "sha256_in_prefix": "bc7a4b0fa474ad3d82487aa70533d2a8449b1e4303b92571f360baa89a6151fa", "size_in_bytes": 21434}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py", "path_type": "hardlink", "sha256": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "sha256_in_prefix": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "size_in_bytes": 3266}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "sha256_in_prefix": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "size_in_bytes": 9588}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "sha256_in_prefix": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "size_in_bytes": 2674}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py", "path_type": "hardlink", "sha256": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "sha256_in_prefix": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "size_in_bytes": 10347}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "sha256_in_prefix": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "size_in_bytes": 5292}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py", "path_type": "hardlink", "sha256": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "sha256_in_prefix": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "size_in_bytes": 8232}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py", "path_type": "hardlink", "sha256": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "sha256_in_prefix": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "size_in_bytes": 2933}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py", "path_type": "hardlink", "sha256": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "sha256_in_prefix": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "size_in_bytes": 39778}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py", "path_type": "hardlink", "sha256": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "sha256_in_prefix": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "size_in_bytes": 18950}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py", "path_type": "hardlink", "sha256": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "sha256_in_prefix": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "size_in_bytes": 5268}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py", "path_type": "hardlink", "sha256": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "sha256_in_prefix": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "size_in_bytes": 16234}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/vendor.txt", "path_type": "hardlink", "sha256": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "sha256_in_prefix": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "size_in_bytes": 16}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/wheelfile.py", "path_type": "hardlink", "sha256": "0ed2435a864cbe7061e2578d3033c63a9ad053d77f769eaaf8c995d14fbee317", "sha256_in_prefix": "0ed2435a864cbe7061e2578d3033c63a9ad053d77f769eaaf8c995d14fbee317", "size_in_bytes": 7694}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "508ae4fe43081c64b0b0a2828588b3a8cc3430c6693d1676662569400b0dfdb1", "sha256_in_prefix": "508ae4fe43081c64b0b0a2828588b3a8cc3430c6693d1676662569400b0dfdb1", "size_in_bytes": 3575}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "f316f2e03fd9ade7ebbc0b154706848e2bb8fd568b90935109f0d8e3ce2b9bfe", "sha256_in_prefix": "f316f2e03fd9ade7ebbc0b154706848e2bb8fd568b90935109f0d8e3ce2b9bfe", "size_in_bytes": 1039}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "8806dda121df686a817d56f65ee47d26a4901c2a0eb0eb46eb2f42fcb4a9a85c", "sha256_in_prefix": "8806dda121df686a817d56f65ee47d26a4901c2a0eb0eb46eb2f42fcb4a9a85c", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/__init__.py", "path_type": "hardlink", "sha256": "42e235834d06e1f440706b7e1ea6d5d285889264a079d086198b071d8ccd6bc0", "sha256_in_prefix": "42e235834d06e1f440706b7e1ea6d5d285889264a079d086198b071d8ccd6bc0", "size_in_bytes": 13412}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "e9cef44fd4c5c8bab348b360223e9173c6bb711d9d78c308716009e51837c833", "sha256_in_prefix": "e9cef44fd4c5c8bab348b360223e9173c6bb711d9d78c308716009e51837c833", "size_in_bytes": 24496}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/glob.cpython-311.pyc", "path_type": "hardlink", "sha256": "2f34866254a81c67dee7c5c4aa1d4814b8c43735ca9b827d727780658b875e3c", "sha256_in_prefix": "2f34866254a81c67dee7c5c4aa1d4814b8c43735ca9b827d727780658b875e3c", "size_in_bytes": 5613}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "23dcf88394047ac182e22d9da1f2260f2fbc7bbcfdceb9dacc4bbf96adac8067", "sha256_in_prefix": "23dcf88394047ac182e22d9da1f2260f2fbc7bbcfdceb9dacc4bbf96adac8067", "size_in_bytes": 169}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/py310.cpython-311.pyc", "path_type": "hardlink", "sha256": "3b9031fe96d76f5b6c367972a57cd11c1c8040993622400607f6ce9977383a01", "sha256_in_prefix": "3b9031fe96d76f5b6c367972a57cd11c1c8040993622400607f6ce9977383a01", "size_in_bytes": 480}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/py310.py", "path_type": "hardlink", "sha256": "799a645b4cd1b6e9e484487c8e35f780219edb67a6a0a081270ef666de119210", "sha256_in_prefix": "799a645b4cd1b6e9e484487c8e35f780219edb67a6a0a081270ef666de119210", "size_in_bytes": 219}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/glob.py", "path_type": "hardlink", "sha256": "7ad5a99df1284727d4beb52c8bab13886984aef3f07ba1f363aa53f2383f9542", "sha256_in_prefix": "7ad5a99df1284727d4beb52c8bab13886984aef3f07ba1f363aa53f2383f9542", "size_in_bytes": 3082}, {"_path": "Lib/site-packages/setuptools/archive_util.py", "path_type": "hardlink", "sha256": "f02ed7d4a657a61a8b915a5976099ef1b9b653d08f437b669f1a563296c7d263", "sha256_in_prefix": "f02ed7d4a657a61a8b915a5976099ef1b9b653d08f437b669f1a563296c7d263", "size_in_bytes": 7332}, {"_path": "Lib/site-packages/setuptools/build_meta.py", "path_type": "hardlink", "sha256": "7ea271da0942fe13ceab731dd8c5e69323e56ed0116423f195b8a00e5e5f7a18", "sha256_in_prefix": "7ea271da0942fe13ceab731dd8c5e69323e56ed0116423f195b8a00e5e5f7a18", "size_in_bytes": 19151}, {"_path": "Lib/site-packages/setuptools/cli-32.exe", "path_type": "hardlink", "sha256": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "sha256_in_prefix": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/cli-64.exe", "path_type": "hardlink", "sha256": "bbb3de5707629e6a60a0c238cd477b28f07f0066982fda953fa6fcec39073a4a", "sha256_in_prefix": "bbb3de5707629e6a60a0c238cd477b28f07f0066982fda953fa6fcec39073a4a", "size_in_bytes": 14336}, {"_path": "Lib/site-packages/setuptools/cli-arm64.exe", "path_type": "hardlink", "sha256": "b9a7d08da880dfac8bcf548eba4b06fb59b6f09b17d33148a0f6618328926c61", "sha256_in_prefix": "b9a7d08da880dfac8bcf548eba4b06fb59b6f09b17d33148a0f6618328926c61", "size_in_bytes": 13824}, {"_path": "Lib/site-packages/setuptools/cli.exe", "path_type": "hardlink", "sha256": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "sha256_in_prefix": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/command/__init__.py", "path_type": "hardlink", "sha256": "25b0393e6a676760074111554f6af3e6efcf8c916ce7ae3b18e20ee4eb89cc34", "sha256_in_prefix": "25b0393e6a676760074111554f6af3e6efcf8c916ce7ae3b18e20ee4eb89cc34", "size_in_bytes": 397}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "71a58131cd0018bbedc30a382dc67f6cf6c4ca51cf2e11d41dcea2827955ede1", "sha256_in_prefix": "71a58131cd0018bbedc30a382dc67f6cf6c4ca51cf2e11d41dcea2827955ede1", "size_in_bytes": 598}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/_requirestxt.cpython-311.pyc", "path_type": "hardlink", "sha256": "11e6afd79414a349d60199692d8d71167050f36468bc311e15f270bcf48c9e8b", "sha256_in_prefix": "11e6afd79414a349d60199692d8d71167050f36468bc311e15f270bcf48c9e8b", "size_in_bytes": 7062}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/alias.cpython-311.pyc", "path_type": "hardlink", "sha256": "09ca20129fceb8c26345999eacd2036a89cdc197b249ed61e9b0c081dd2c7990", "sha256_in_prefix": "09ca20129fceb8c26345999eacd2036a89cdc197b249ed61e9b0c081dd2c7990", "size_in_bytes": 3865}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/bdist_egg.cpython-311.pyc", "path_type": "hardlink", "sha256": "0fe173c2fc5f26a4eecbcf2d6185ac5209121b79e596577ba7768bffc22baccd", "sha256_in_prefix": "0fe173c2fc5f26a4eecbcf2d6185ac5209121b79e596577ba7768bffc22baccd", "size_in_bytes": 25736}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/bdist_rpm.cpython-311.pyc", "path_type": "hardlink", "sha256": "059962237ef574207be9135b06318707b9f043499ac74f5bdb3ead2377d10807", "sha256_in_prefix": "059962237ef574207be9135b06318707b9f043499ac74f5bdb3ead2377d10807", "size_in_bytes": 2386}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/bdist_wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "33a5fbdc3fd7dbca4f17b4c664f7f75ab677f934e62e0404aa8eadc170863e06", "sha256_in_prefix": "33a5fbdc3fd7dbca4f17b4c664f7f75ab677f934e62e0404aa8eadc170863e06", "size_in_bytes": 30070}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build.cpython-311.pyc", "path_type": "hardlink", "sha256": "27dce0e2e61132c35315c4f1c218c136c0868147f3a99c27324d3da2483d4840", "sha256_in_prefix": "27dce0e2e61132c35315c4f1c218c136c0868147f3a99c27324d3da2483d4840", "size_in_bytes": 5705}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build_clib.cpython-311.pyc", "path_type": "hardlink", "sha256": "8ebcbebafccecc952a3fabc5838cab4f4ba169b6de9bb8dc18afc64b099770d7", "sha256_in_prefix": "8ebcbebafccecc952a3fabc5838cab4f4ba169b6de9bb8dc18afc64b099770d7", "size_in_bytes": 4406}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build_ext.cpython-311.pyc", "path_type": "hardlink", "sha256": "e9ddd213e25e37e3c97b113ad26e84130faf45b6360d70c7f045aa2f52bacf38", "sha256_in_prefix": "e9ddd213e25e37e3c97b113ad26e84130faf45b6360d70c7f045aa2f52bacf38", "size_in_bytes": 24921}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build_py.cpython-311.pyc", "path_type": "hardlink", "sha256": "6ffb4bce7aad71daad9ad94d74e14849621c60da70780e298829ae6af77b5677", "sha256_in_prefix": "6ffb4bce7aad71daad9ad94d74e14849621c60da70780e298829ae6af77b5677", "size_in_bytes": 24022}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/develop.cpython-311.pyc", "path_type": "hardlink", "sha256": "1ebd69c036c23e01b01c48941ac648afb91557e45ab329240a441c400d816ad8", "sha256_in_prefix": "1ebd69c036c23e01b01c48941ac648afb91557e45ab329240a441c400d816ad8", "size_in_bytes": 10586}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/dist_info.cpython-311.pyc", "path_type": "hardlink", "sha256": "1e27b326e1ebcc9fb30c3d7bec638dc39377db653b77c27dd116cb91d73d9e5b", "sha256_in_prefix": "1e27b326e1ebcc9fb30c3d7bec638dc39377db653b77c27dd116cb91d73d9e5b", "size_in_bytes": 5824}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/easy_install.cpython-311.pyc", "path_type": "hardlink", "sha256": "5e62520763128b6d3063ccf03fd967fa2d648224a2d9e7b74d2ec8e69d109a5f", "sha256_in_prefix": "5e62520763128b6d3063ccf03fd967fa2d648224a2d9e7b74d2ec8e69d109a5f", "size_in_bytes": 121338}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/editable_wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "4ce174748a9a3d04626cf3a9590a096d78ca83991375a9bf811bbc62f74d7c3c", "sha256_in_prefix": "4ce174748a9a3d04626cf3a9590a096d78ca83991375a9bf811bbc62f74d7c3c", "size_in_bytes": 54724}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/egg_info.cpython-311.pyc", "path_type": "hardlink", "sha256": "d1f5a197b6b9b98ec0c53694fa066c4b22dec511e9b7b2e4ec7373ff5149cab0", "sha256_in_prefix": "d1f5a197b6b9b98ec0c53694fa066c4b22dec511e9b7b2e4ec7373ff5149cab0", "size_in_bytes": 37034}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install.cpython-311.pyc", "path_type": "hardlink", "sha256": "1ae9bea6d3f138fdd5f86ccd8271eac12e3f6266b4d9ee703e3e83dd12d1ac39", "sha256_in_prefix": "1ae9bea6d3f138fdd5f86ccd8271eac12e3f6266b4d9ee703e3e83dd12d1ac39", "size_in_bytes": 7663}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-311.pyc", "path_type": "hardlink", "sha256": "7f86072a4620ed4fb4b4e14f9192d28f621a7b4eb43ce7816aaa6ae2e0be4619", "sha256_in_prefix": "7f86072a4620ed4fb4b4e14f9192d28f621a7b4eb43ce7816aaa6ae2e0be4619", "size_in_bytes": 3907}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install_lib.cpython-311.pyc", "path_type": "hardlink", "sha256": "713f9a198f558bf965c61dc086d3f2bc5140ab68750772371e17bffc7005f1a2", "sha256_in_prefix": "713f9a198f558bf965c61dc086d3f2bc5140ab68750772371e17bffc7005f1a2", "size_in_bytes": 6710}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install_scripts.cpython-311.pyc", "path_type": "hardlink", "sha256": "e41dbe2a10257be5bd57e090458fa17e32bf1c8b9bb67a26fd8f859920bdd215", "sha256_in_prefix": "e41dbe2a10257be5bd57e090458fa17e32bf1c8b9bb67a26fd8f859920bdd215", "size_in_bytes": 4348}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/rotate.cpython-311.pyc", "path_type": "hardlink", "sha256": "82be97ae86f764c139591d010313259077d8a9644e25339a1ec79bcfac818564", "sha256_in_prefix": "82be97ae86f764c139591d010313259077d8a9644e25339a1ec79bcfac818564", "size_in_bytes": 4245}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/saveopts.cpython-311.pyc", "path_type": "hardlink", "sha256": "3e333777a5ab132a5e3951307f510796bfa2a4ccbc0a635094114fe8a276fd6f", "sha256_in_prefix": "3e333777a5ab132a5e3951307f510796bfa2a4ccbc0a635094114fe8a276fd6f", "size_in_bytes": 1329}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/sdist.cpython-311.pyc", "path_type": "hardlink", "sha256": "eec3baec052fc52e09a18acbbca34a6657874afc572ad0ff23922f0ffedeb18e", "sha256_in_prefix": "eec3baec052fc52e09a18acbbca34a6657874afc572ad0ff23922f0ffedeb18e", "size_in_bytes": 13529}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/setopt.cpython-311.pyc", "path_type": "hardlink", "sha256": "03168ebfc6f187c8f1cbc53cabe259116cbd5fe70b8f6b21b894bd951a496eb5", "sha256_in_prefix": "03168ebfc6f187c8f1cbc53cabe259116cbd5fe70b8f6b21b894bd951a496eb5", "size_in_bytes": 7710}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/test.cpython-311.pyc", "path_type": "hardlink", "sha256": "4c13dc1471935b093fcb502c13a041b5303968f90063705b53940c579d50e7bc", "sha256_in_prefix": "4c13dc1471935b093fcb502c13a041b5303968f90063705b53940c579d50e7bc", "size_in_bytes": 2095}, {"_path": "Lib/site-packages/setuptools/command/_requirestxt.py", "path_type": "hardlink", "sha256": "b36308bf9bd673b37fe661f389420fbe5e958e83c8509b22bf4712e225c4cb93", "sha256_in_prefix": "b36308bf9bd673b37fe661f389420fbe5e958e83c8509b22bf4712e225c4cb93", "size_in_bytes": 4227}, {"_path": "Lib/site-packages/setuptools/command/alias.py", "path_type": "hardlink", "sha256": "6a218cc3501e378aea263af736bdd25436cb5e3823b9fae2c1c30cd670668876", "sha256_in_prefix": "6a218cc3501e378aea263af736bdd25436cb5e3823b9fae2c1c30cd670668876", "size_in_bytes": 2383}, {"_path": "Lib/site-packages/setuptools/command/bdist_egg.py", "path_type": "hardlink", "sha256": "326cd3e0607d7bb15427e2b2a16cb20c645d93beeacec83c375550bd52f1df91", "sha256_in_prefix": "326cd3e0607d7bb15427e2b2a16cb20c645d93beeacec83c375550bd52f1df91", "size_in_bytes": 16526}, {"_path": "Lib/site-packages/setuptools/command/bdist_rpm.py", "path_type": "hardlink", "sha256": "f7dc151416bc105de01db12ad744288ee61ca71df35ff34b09864da5b1bae865", "sha256_in_prefix": "f7dc151416bc105de01db12ad744288ee61ca71df35ff34b09864da5b1bae865", "size_in_bytes": 1427}, {"_path": "Lib/site-packages/setuptools/command/bdist_wheel.py", "path_type": "hardlink", "sha256": "ea6b9f861f3a733bcd9f76c62a0e6036fc1cdb8eb3b2571cd19363336b9c9738", "sha256_in_prefix": "ea6b9f861f3a733bcd9f76c62a0e6036fc1cdb8eb3b2571cd19363336b9c9738", "size_in_bytes": 22883}, {"_path": "Lib/site-packages/setuptools/command/build.py", "path_type": "hardlink", "sha256": "0bd4d0afbb7fab74de856b1cc443d46d71fbaf4f290b2374b43abdaf13c5a28e", "sha256_in_prefix": "0bd4d0afbb7fab74de856b1cc443d46d71fbaf4f290b2374b43abdaf13c5a28e", "size_in_bytes": 6028}, {"_path": "Lib/site-packages/setuptools/command/build_clib.py", "path_type": "hardlink", "sha256": "bf1a795a6cd1e914189a15d7c80cf8c34d30f0def1c02a8f99ab8dba4833ac9d", "sha256_in_prefix": "bf1a795a6cd1e914189a15d7c80cf8c34d30f0def1c02a8f99ab8dba4833ac9d", "size_in_bytes": 4736}, {"_path": "Lib/site-packages/setuptools/command/build_ext.py", "path_type": "hardlink", "sha256": "0ee1f835c81a430576b22f7edbbb815a669dfb401dc42e54edc0def132a99c80", "sha256_in_prefix": "0ee1f835c81a430576b22f7edbbb815a669dfb401dc42e54edc0def132a99c80", "size_in_bytes": 18264}, {"_path": "Lib/site-packages/setuptools/command/build_py.py", "path_type": "hardlink", "sha256": "1b61f1bbe5aace1e9dc566d18d11df112fd29656141942abc87ff4102b31d955", "sha256_in_prefix": "1b61f1bbe5aace1e9dc566d18d11df112fd29656141942abc87ff4102b31d955", "size_in_bytes": 15327}, {"_path": "Lib/site-packages/setuptools/command/develop.py", "path_type": "hardlink", "sha256": "49243120a6573fbfa450e7d34f86b7588a33b2c8833a2bc741feb5a34df850ee", "sha256_in_prefix": "49243120a6573fbfa450e7d34f86b7588a33b2c8833a2bc741feb5a34df850ee", "size_in_bytes": 6854}, {"_path": "Lib/site-packages/setuptools/command/dist_info.py", "path_type": "hardlink", "sha256": "16e785a88fadefe29e3f513c3f21bd0347f8b4309faf0d175dfeef9da6078d8e", "sha256_in_prefix": "16e785a88fadefe29e3f513c3f21bd0347f8b4309faf0d175dfeef9da6078d8e", "size_in_bytes": 3508}, {"_path": "Lib/site-packages/setuptools/command/easy_install.py", "path_type": "hardlink", "sha256": "258a6df184d3f82ab3362750e09c9589f0bc33fcdf275892b5d45c1b2ae61db7", "sha256_in_prefix": "258a6df184d3f82ab3362750e09c9589f0bc33fcdf275892b5d45c1b2ae61db7", "size_in_bytes": 88305}, {"_path": "Lib/site-packages/setuptools/command/editable_wheel.py", "path_type": "hardlink", "sha256": "a84d33564e2acc8302f50d7cfd48bc8a824b6b9e146dd9852aa4b1bc6c91d262", "sha256_in_prefix": "a84d33564e2acc8302f50d7cfd48bc8a824b6b9e146dd9852aa4b1bc6c91d262", "size_in_bytes": 35665}, {"_path": "Lib/site-packages/setuptools/command/egg_info.py", "path_type": "hardlink", "sha256": "fef4a920d30ff30e6878cafd907ed5a920e9e97f16157868c8c8f8054b39138d", "sha256_in_prefix": "fef4a920d30ff30e6878cafd907ed5a920e9e97f16157868c8c8f8054b39138d", "size_in_bytes": 25610}, {"_path": "Lib/site-packages/setuptools/command/install.py", "path_type": "hardlink", "sha256": "a8e4fe2594814b9b5eded1a3bd7af702a8288219fa1c8f70d547784a432b54dd", "sha256_in_prefix": "a8e4fe2594814b9b5eded1a3bd7af702a8288219fa1c8f70d547784a432b54dd", "size_in_bytes": 6208}, {"_path": "Lib/site-packages/setuptools/command/install_egg_info.py", "path_type": "hardlink", "sha256": "6eb6114466f4ffccc9dfe7d0d1f9e47970b35f6ada2cd5f4e496bb9fbddc2492", "sha256_in_prefix": "6eb6114466f4ffccc9dfe7d0d1f9e47970b35f6ada2cd5f4e496bb9fbddc2492", "size_in_bytes": 2046}, {"_path": "Lib/site-packages/setuptools/command/install_lib.py", "path_type": "hardlink", "sha256": "dfefc9467be74314c3d25495f40b67c68930b48a6f35a3c194a1a9da2e52e71f", "sha256_in_prefix": "dfefc9467be74314c3d25495f40b67c68930b48a6f35a3c194a1a9da2e52e71f", "size_in_bytes": 4133}, {"_path": "Lib/site-packages/setuptools/command/install_scripts.py", "path_type": "hardlink", "sha256": "9906c8c7d72e30849a123d853a2e98bbbfeefeade0a3f6de07a829870042af02", "sha256_in_prefix": "9906c8c7d72e30849a123d853a2e98bbbfeefeade0a3f6de07a829870042af02", "size_in_bytes": 2614}, {"_path": "Lib/site-packages/setuptools/command/launcher manifest.xml", "path_type": "hardlink", "sha256": "c652db8d6ac1d35b4a0b4fa195590e2a48923dbccc9a5d9e38fb49fee7029db1", "sha256_in_prefix": "c652db8d6ac1d35b4a0b4fa195590e2a48923dbccc9a5d9e38fb49fee7029db1", "size_in_bytes": 628}, {"_path": "Lib/site-packages/setuptools/command/rotate.py", "path_type": "hardlink", "sha256": "f6bf20ded91bbe3ef239592be9cd9c99cc6fe4b36ac808a30036e68c9fd9cb6e", "sha256_in_prefix": "f6bf20ded91bbe3ef239592be9cd9c99cc6fe4b36ac808a30036e68c9fd9cb6e", "size_in_bytes": 2145}, {"_path": "Lib/site-packages/setuptools/command/saveopts.py", "path_type": "hardlink", "sha256": "99500f31120613df2097a7974370b65a8faa3ce825f656c7f90fd8b1b2eea9e8", "sha256_in_prefix": "99500f31120613df2097a7974370b65a8faa3ce825f656c7f90fd8b1b2eea9e8", "size_in_bytes": 657}, {"_path": "Lib/site-packages/setuptools/command/sdist.py", "path_type": "hardlink", "sha256": "9066c66ef3271e6dc65fc0cc40088a3f27efcd6aadba55849d93796d3db65464", "sha256_in_prefix": "9066c66ef3271e6dc65fc0cc40088a3f27efcd6aadba55849d93796d3db65464", "size_in_bytes": 7277}, {"_path": "Lib/site-packages/setuptools/command/setopt.py", "path_type": "hardlink", "sha256": "323272fb4a428e7282a2e62583fa2e4f20761d99fd1b3e0263426ea5d5c4de7a", "sha256_in_prefix": "323272fb4a428e7282a2e62583fa2e4f20761d99fd1b3e0263426ea5d5c4de7a", "size_in_bytes": 5019}, {"_path": "Lib/site-packages/setuptools/command/test.py", "path_type": "hardlink", "sha256": "93bc5cabb0fb6c47a18316ab6f0f9d5b702d98664e46acfc1e3291e85189de39", "sha256_in_prefix": "93bc5cabb0fb6c47a18316ab6f0f9d5b702d98664e46acfc1e3291e85189de39", "size_in_bytes": 1343}, {"_path": "Lib/site-packages/setuptools/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "9ef1f7b1115c267e08c1ced7268063f08fa31bcd61c3e5556b20750545b4044c", "sha256_in_prefix": "9ef1f7b1115c267e08c1ced7268063f08fa31bcd61c3e5556b20750545b4044c", "size_in_bytes": 156}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py310.cpython-311.pyc", "path_type": "hardlink", "sha256": "ff523df0c6d6ed0491eefcb2b9bd35e2417411e58720240f25cb1f65904c5584", "sha256_in_prefix": "ff523df0c6d6ed0491eefcb2b9bd35e2417411e58720240f25cb1f65904c5584", "size_in_bytes": 325}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py311.cpython-311.pyc", "path_type": "hardlink", "sha256": "61ed4df09fb8c5d14d2cd81e351cef9d4fbc424b38ab2abe89404e41a5a49b01", "sha256_in_prefix": "61ed4df09fb8c5d14d2cd81e351cef9d4fbc424b38ab2abe89404e41a5a49b01", "size_in_bytes": 1629}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py312.cpython-311.pyc", "path_type": "hardlink", "sha256": "562f43e3a3601a02683f4b73827470f17e2ae1bd6d390f3e2176ce5cac9668ac", "sha256_in_prefix": "562f43e3a3601a02683f4b73827470f17e2ae1bd6d390f3e2176ce5cac9668ac", "size_in_bytes": 479}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py39.cpython-311.pyc", "path_type": "hardlink", "sha256": "5d2f048ac045e0857d0d1a5e04791027981876f54f8a01e472029153c4df49ed", "sha256_in_prefix": "5d2f048ac045e0857d0d1a5e04791027981876f54f8a01e472029153c4df49ed", "size_in_bytes": 275}, {"_path": "Lib/site-packages/setuptools/compat/py310.py", "path_type": "hardlink", "sha256": "f2cab059ccc872b9337806e16a29b8a4a55de2d5d975caa679b81dbf38e2d2b7", "sha256_in_prefix": "f2cab059ccc872b9337806e16a29b8a4a55de2d5d975caa679b81dbf38e2d2b7", "size_in_bytes": 141}, {"_path": "Lib/site-packages/setuptools/compat/py311.py", "path_type": "hardlink", "sha256": "7bab49005c1910ff36866301975d0761e4b2a5e968fd38b6c138ca65528bc0e1", "sha256_in_prefix": "7bab49005c1910ff36866301975d0761e4b2a5e968fd38b6c138ca65528bc0e1", "size_in_bytes": 790}, {"_path": "Lib/site-packages/setuptools/compat/py312.py", "path_type": "hardlink", "sha256": "bd8295b5dadd393b0efd1f747499045ec1707cc245b881497e5848807ae327e6", "sha256_in_prefix": "bd8295b5dadd393b0efd1f747499045ec1707cc245b881497e5848807ae327e6", "size_in_bytes": 366}, {"_path": "Lib/site-packages/setuptools/compat/py39.py", "path_type": "hardlink", "sha256": "04932d9e47dcab24df71caa3610c5fa11b54da74e759a104481564b214e25ea6", "sha256_in_prefix": "04932d9e47dcab24df71caa3610c5fa11b54da74e759a104481564b214e25ea6", "size_in_bytes": 493}, {"_path": "Lib/site-packages/setuptools/config/NOTICE", "path_type": "hardlink", "sha256": "2dddf08818297a3b89d43d95ff659d8da85741108c9136dfa3a4d856c0623bd8", "sha256_in_prefix": "2dddf08818297a3b89d43d95ff659d8da85741108c9136dfa3a4d856c0623bd8", "size_in_bytes": 493}, {"_path": "Lib/site-packages/setuptools/config/__init__.py", "path_type": "hardlink", "sha256": "6a23e72fd0499f53ba31f9ae357ca7f16d8ba7cbbdaa2cd156ac0f88e74f2236", "sha256_in_prefix": "6a23e72fd0499f53ba31f9ae357ca7f16d8ba7cbbdaa2cd156ac0f88e74f2236", "size_in_bytes": 1499}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "3e47c312508a80d1193d8ef4a2672f6a6138b65fe17f1767339e6bbea915ff76", "sha256_in_prefix": "3e47c312508a80d1193d8ef4a2672f6a6138b65fe17f1767339e6bbea915ff76", "size_in_bytes": 2126}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/_apply_pyprojecttoml.cpython-311.pyc", "path_type": "hardlink", "sha256": "5c7d9c3a44351c2ec6d0903e949c5fed6f1f178491b1dd3bc8691c71fdf7837f", "sha256_in_prefix": "5c7d9c3a44351c2ec6d0903e949c5fed6f1f178491b1dd3bc8691c71fdf7837f", "size_in_bytes": 25578}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/expand.cpython-311.pyc", "path_type": "hardlink", "sha256": "0dcef6f87c892178bd52dfbf8abe3b4b0cc9868ff839675cea8769fc2e4d0773", "sha256_in_prefix": "0dcef6f87c892178bd52dfbf8abe3b4b0cc9868ff839675cea8769fc2e4d0773", "size_in_bytes": 27077}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/pyprojecttoml.cpython-311.pyc", "path_type": "hardlink", "sha256": "a691c17ee1a1e960ae29d367bdee178d9b851b266aad5b13e2a8b1a7ae19aad6", "sha256_in_prefix": "a691c17ee1a1e960ae29d367bdee178d9b851b266aad5b13e2a8b1a7ae19aad6", "size_in_bytes": 26330}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/setupcfg.cpython-311.pyc", "path_type": "hardlink", "sha256": "f61a4616672ce1b7ce76baa5568ef5915edca7145b9545f6a1cffeaedf822dcb", "sha256_in_prefix": "f61a4616672ce1b7ce76baa5568ef5915edca7145b9545f6a1cffeaedf822dcb", "size_in_bytes": 34906}, {"_path": "Lib/site-packages/setuptools/config/_apply_pyprojecttoml.py", "path_type": "hardlink", "sha256": "b1d9b9ed36dd286d439fdbd79b515fb552d1cebf8fe7ea9f06038fcf60a34502", "sha256_in_prefix": "b1d9b9ed36dd286d439fdbd79b515fb552d1cebf8fe7ea9f06038fcf60a34502", "size_in_bytes": 15457}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/NOTICE", "path_type": "hardlink", "sha256": "09c9bcea95ca086f8bc5bed174e40bc835b297d40fb5f86bbbb570fe0a5581a7", "sha256_in_prefix": "09c9bcea95ca086f8bc5bed174e40bc835b297d40fb5f86bbbb570fe0a5581a7", "size_in_bytes": 18737}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__init__.py", "path_type": "hardlink", "sha256": "767a7a4fb78f3f5479cf83ae0bb15dd9d905948aed21f8b351fbe91893fa9f3d", "sha256_in_prefix": "767a7a4fb78f3f5479cf83ae0bb15dd9d905948aed21f8b351fbe91893fa9f3d", "size_in_bytes": 1042}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "e68726a821387ad89b9936857d69398a63a0e331f26435fe9df0baf10e23ad93", "sha256_in_prefix": "e68726a821387ad89b9936857d69398a63a0e331f26435fe9df0baf10e23ad93", "size_in_bytes": 2305}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/error_reporting.cpython-311.pyc", "path_type": "hardlink", "sha256": "155ca93d4baca3076839da0ebf82f57267805ab1ad03bf39370abe25d7ce375b", "sha256_in_prefix": "155ca93d4baca3076839da0ebf82f57267805ab1ad03bf39370abe25d7ce375b", "size_in_bytes": 20889}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/extra_validations.cpython-311.pyc", "path_type": "hardlink", "sha256": "1e7eeda2823a245b15a7fd28b1d80f90c80a13a664585cfa585f5420520a41f7", "sha256_in_prefix": "1e7eeda2823a245b15a7fd28b1d80f90c80a13a664585cfa585f5420520a41f7", "size_in_bytes": 2129}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_exceptions.cpython-311.pyc", "path_type": "hardlink", "sha256": "88ba9c104917e3bae656d785a123ca1b7661dc0228fe38895faa6cf1f76cc621", "sha256_in_prefix": "88ba9c104917e3bae656d785a123ca1b7661dc0228fe38895faa6cf1f76cc621", "size_in_bytes": 3205}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_validations.cpython-311.pyc", "path_type": "hardlink", "sha256": "34afa9544f5ac7242103c998868ca91dbc9d86f456427a204409ad79effef1f3", "sha256_in_prefix": "34afa9544f5ac7242103c998868ca91dbc9d86f456427a204409ad79effef1f3", "size_in_bytes": 249278}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/formats.cpython-311.pyc", "path_type": "hardlink", "sha256": "824d56ef0cfbe451da5e02e64c77b6366b4bbc7f76f17da16bfa0278e2a363da", "sha256_in_prefix": "824d56ef0cfbe451da5e02e64c77b6366b4bbc7f76f17da16bfa0278e2a363da", "size_in_bytes": 19349}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/error_reporting.py", "path_type": "hardlink", "sha256": "99e95d0fb9c141da25421bc6fb8debd547be814d67ece440251f3abe1dd1aef9", "sha256_in_prefix": "99e95d0fb9c141da25421bc6fb8debd547be814d67ece440251f3abe1dd1aef9", "size_in_bytes": 11813}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/extra_validations.py", "path_type": "hardlink", "sha256": "91dd12598aeca7721717d28600cf10a5e68aa46c8cb0d80bfad8e1f533df8726", "sha256_in_prefix": "91dd12598aeca7721717d28600cf10a5e68aa46c8cb0d80bfad8e1f533df8726", "size_in_bytes": 1625}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py", "path_type": "hardlink", "sha256": "c3be3d260a8a8bc72504570e6dd71b655aac985e2827f401ca16754866d414dc", "sha256_in_prefix": "c3be3d260a8a8bc72504570e6dd71b655aac985e2827f401ca16754866d414dc", "size_in_bytes": 1612}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py", "path_type": "hardlink", "sha256": "4fc46f2528b9dd805f0c1e2644fb85ea0b0fc71f3ebbc2985f9eb2deaa24a7fe", "sha256_in_prefix": "4fc46f2528b9dd805f0c1e2644fb85ea0b0fc71f3ebbc2985f9eb2deaa24a7fe", "size_in_bytes": 335460}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/formats.py", "path_type": "hardlink", "sha256": "323cfa463c7504c0f0d974cc01f4beb0ce71e45bf9697d9993fab933feeb7ff7", "sha256_in_prefix": "323cfa463c7504c0f0d974cc01f4beb0ce71e45bf9697d9993fab933feeb7ff7", "size_in_bytes": 12814}, {"_path": "Lib/site-packages/setuptools/config/distutils.schema.json", "path_type": "hardlink", "sha256": "4dca77da44678703911b0ffda7a1848b4f258f6875e6d411cce6016f31a67015", "sha256_in_prefix": "4dca77da44678703911b0ffda7a1848b4f258f6875e6d411cce6016f31a67015", "size_in_bytes": 972}, {"_path": "Lib/site-packages/setuptools/config/expand.py", "path_type": "hardlink", "sha256": "8ec65451a53ff2ecbe6739490b49219a91a30954656d5fac17230893e4866084", "sha256_in_prefix": "8ec65451a53ff2ecbe6739490b49219a91a30954656d5fac17230893e4866084", "size_in_bytes": 15659}, {"_path": "Lib/site-packages/setuptools/config/pyprojecttoml.py", "path_type": "hardlink", "sha256": "a71abeb1888c40f33dc00ba9700156131078ef723e67553e66906c99ed4b0333", "sha256_in_prefix": "a71abeb1888c40f33dc00ba9700156131078ef723e67553e66906c99ed4b0333", "size_in_bytes": 18142}, {"_path": "Lib/site-packages/setuptools/config/setupcfg.py", "path_type": "hardlink", "sha256": "ec33002f4fafa387d3ebf7691b55f6d0abe2e12f9c0e37c77b993f8330274149", "sha256_in_prefix": "ec33002f4fafa387d3ebf7691b55f6d0abe2e12f9c0e37c77b993f8330274149", "size_in_bytes": 25634}, {"_path": "Lib/site-packages/setuptools/config/setuptools.schema.json", "path_type": "hardlink", "sha256": "759051b921276646ada1596dd645701bca1c4de45d3bb043d31bce58a1f9e0f6", "sha256_in_prefix": "759051b921276646ada1596dd645701bca1c4de45d3bb043d31bce58a1f9e0f6", "size_in_bytes": 16071}, {"_path": "Lib/site-packages/setuptools/depends.py", "path_type": "hardlink", "sha256": "22c3dd2c113a5e9a6d532a7c5ad8a1383f0ef8f067af645ecde88a0454f94ad7", "sha256_in_prefix": "22c3dd2c113a5e9a6d532a7c5ad8a1383f0ef8f067af645ecde88a0454f94ad7", "size_in_bytes": 5542}, {"_path": "Lib/site-packages/setuptools/discovery.py", "path_type": "hardlink", "sha256": "90405cf2faa2d59abc908187f34ab443fc69e1f15d1da6d040cd841a0d9e198d", "sha256_in_prefix": "90405cf2faa2d59abc908187f34ab443fc69e1f15d1da6d040cd841a0d9e198d", "size_in_bytes": 21104}, {"_path": "Lib/site-packages/setuptools/dist.py", "path_type": "hardlink", "sha256": "e9bceb5738643dd9c1d40291123307dfb75e2b22a97ab6150f5e724f51f9e4d0", "sha256_in_prefix": "e9bceb5738643dd9c1d40291123307dfb75e2b22a97ab6150f5e724f51f9e4d0", "size_in_bytes": 36729}, {"_path": "Lib/site-packages/setuptools/errors.py", "path_type": "hardlink", "sha256": "a9d1c3e45a2784d33a82477ceb7fb8f5869c497911fe4ce1dc3d256348765592", "sha256_in_prefix": "a9d1c3e45a2784d33a82477ceb7fb8f5869c497911fe4ce1dc3d256348765592", "size_in_bytes": 2988}, {"_path": "Lib/site-packages/setuptools/extension.py", "path_type": "hardlink", "sha256": "779a05f34db64d6128d19ed6b13f857efaf2e70f5bbdfcf5866dcff3ddbf21d1", "sha256_in_prefix": "779a05f34db64d6128d19ed6b13f857efaf2e70f5bbdfcf5866dcff3ddbf21d1", "size_in_bytes": 6457}, {"_path": "Lib/site-packages/setuptools/glob.py", "path_type": "hardlink", "sha256": "88c0b2d25862ac5dbdb33d9f6bd07a295195bd49e2cb5d5216974b9755026fe2", "sha256_in_prefix": "88c0b2d25862ac5dbdb33d9f6bd07a295195bd49e2cb5d5216974b9755026fe2", "size_in_bytes": 4852}, {"_path": "Lib/site-packages/setuptools/gui-32.exe", "path_type": "hardlink", "sha256": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "sha256_in_prefix": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/gui-64.exe", "path_type": "hardlink", "sha256": "3471b6140eadc6412277dbbefe3fef8c345a0f1a59776086b80a3618c3a83e3b", "sha256_in_prefix": "3471b6140eadc6412277dbbefe3fef8c345a0f1a59776086b80a3618c3a83e3b", "size_in_bytes": 14336}, {"_path": "Lib/site-packages/setuptools/gui-arm64.exe", "path_type": "hardlink", "sha256": "e694f4743405c8b5926ff457db6fe7f1a12dec7c16a9c3864784d3f4e07ae097", "sha256_in_prefix": "e694f4743405c8b5926ff457db6fe7f1a12dec7c16a9c3864784d3f4e07ae097", "size_in_bytes": 13824}, {"_path": "Lib/site-packages/setuptools/gui.exe", "path_type": "hardlink", "sha256": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "sha256_in_prefix": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/installer.py", "path_type": "hardlink", "sha256": "585f056624173f6acdb3f2e95fff02fc0f25eec662be7e57ce4f60ea5406da0e", "sha256_in_prefix": "585f056624173f6acdb3f2e95fff02fc0f25eec662be7e57ce4f60ea5406da0e", "size_in_bytes": 4970}, {"_path": "Lib/site-packages/setuptools/launch.py", "path_type": "hardlink", "sha256": "56c601bd986292ffa8fee25ceb4c8cfb6fcf0900793f9bd21672e5e42e412790", "sha256_in_prefix": "56c601bd986292ffa8fee25ceb4c8cfb6fcf0900793f9bd21672e5e42e412790", "size_in_bytes": 812}, {"_path": "Lib/site-packages/setuptools/logging.py", "path_type": "hardlink", "sha256": "05829dd1bcdb882bd407d5c4b5f09869887d17f40da7162346c1765e6c39d64f", "sha256_in_prefix": "05829dd1bcdb882bd407d5c4b5f09869887d17f40da7162346c1765e6c39d64f", "size_in_bytes": 1241}, {"_path": "Lib/site-packages/setuptools/modified.py", "path_type": "hardlink", "sha256": "186cdcef5c8491fc0b575f87a97470c2c9f38676fdee4d43dd1ec7f0413fd33e", "sha256_in_prefix": "186cdcef5c8491fc0b575f87a97470c2c9f38676fdee4d43dd1ec7f0413fd33e", "size_in_bytes": 190}, {"_path": "Lib/site-packages/setuptools/monkey.py", "path_type": "hardlink", "sha256": "7f1effd349e3901d9c0ab76d4d6a990cacdd2ac4f3f5df461d9b4c068ce0c0ea", "sha256_in_prefix": "7f1effd349e3901d9c0ab76d4d6a990cacdd2ac4f3f5df461d9b4c068ce0c0ea", "size_in_bytes": 3573}, {"_path": "Lib/site-packages/setuptools/msvc.py", "path_type": "hardlink", "sha256": "1e5fb47f772ec58f74b3777b1f1585ea5813a751bdf4eeb9c1e4f0ec95900c49", "sha256_in_prefix": "1e5fb47f772ec58f74b3777b1f1585ea5813a751bdf4eeb9c1e4f0ec95900c49", "size_in_bytes": 40791}, {"_path": "Lib/site-packages/setuptools/namespaces.py", "path_type": "hardlink", "sha256": "f0daf135ae19ae47ef8682154ced8fcea2f284ee80d429542c35fa9aec38b3ad", "sha256_in_prefix": "f0daf135ae19ae47ef8682154ced8fcea2f284ee80d429542c35fa9aec38b3ad", "size_in_bytes": 3155}, {"_path": "Lib/site-packages/setuptools/package_index.py", "path_type": "hardlink", "sha256": "0abaf29e62a562250fe8df0934e4c14eb109388986b00a1edc2f0955b944217b", "sha256_in_prefix": "0abaf29e62a562250fe8df0934e4c14eb109388986b00a1edc2f0955b944217b", "size_in_bytes": 39271}, {"_path": "Lib/site-packages/setuptools/sandbox.py", "path_type": "hardlink", "sha256": "88f9b320fbc70e5e3673014ad209d4a56908ce28175e3753232ffc1181a0c904", "sha256_in_prefix": "88f9b320fbc70e5e3673014ad209d4a56908ce28175e3753232ffc1181a0c904", "size_in_bytes": 14550}, {"_path": "Lib/site-packages/setuptools/script (dev).tmpl", "path_type": "hardlink", "sha256": "454cd0cc2414697b7074bb581d661b21098e6844b906baaad45bd403fb6efb92", "sha256_in_prefix": "454cd0cc2414697b7074bb581d661b21098e6844b906baaad45bd403fb6efb92", "size_in_bytes": 218}, {"_path": "Lib/site-packages/setuptools/script.tmpl", "path_type": "hardlink", "sha256": "5864ede6989eccedbb73e0dbc7a9794384f715fdb4039cfbf3bda1bf76808586", "sha256_in_prefix": "5864ede6989eccedbb73e0dbc7a9794384f715fdb4039cfbf3bda1bf76808586", "size_in_bytes": 138}, {"_path": "Lib/site-packages/setuptools/tests/__init__.py", "path_type": "hardlink", "sha256": "02705f96cda225b4c343398c29e2d1b7ef65c6168e1d454e644817bfcf54c2fb", "sha256_in_prefix": "02705f96cda225b4c343398c29e2d1b7ef65c6168e1d454e644817bfcf54c2fb", "size_in_bytes": 335}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "7373894d6c75d7ca6570094b02ca0e90c0f1efa3f48116029e175b6fa5330a88", "sha256_in_prefix": "7373894d6c75d7ca6570094b02ca0e90c0f1efa3f48116029e175b6fa5330a88", "size_in_bytes": 663}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/contexts.cpython-311.pyc", "path_type": "hardlink", "sha256": "2f4c29d372fd44bc981215ed7f2634d1b8761b06840c5b2f5f620eb2b66c1ff6", "sha256_in_prefix": "2f4c29d372fd44bc981215ed7f2634d1b8761b06840c5b2f5f620eb2b66c1ff6", "size_in_bytes": 7712}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/environment.cpython-311.pyc", "path_type": "hardlink", "sha256": "d240a716da564de0e799bad0b1beb5b5bec3754fbf4bd6d95470b03e146988d8", "sha256_in_prefix": "d240a716da564de0e799bad0b1beb5b5bec3754fbf4bd6d95470b03e146988d8", "size_in_bytes": 3822}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/fixtures.cpython-311.pyc", "path_type": "hardlink", "sha256": "a4e981f7ecc0e503865d373fb4f4e46c1c04bcbe93f947d4f30e34677229bcf3", "sha256_in_prefix": "a4e981f7ecc0e503865d373fb4f4e46c1c04bcbe93f947d4f30e34677229bcf3", "size_in_bytes": 8265}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/mod_with_constant.cpython-311.pyc", "path_type": "hardlink", "sha256": "f161cac0d822503d00c649ef7bbe4d79f8f68b95fff541ec5d94c4108483f8c9", "sha256_in_prefix": "f161cac0d822503d00c649ef7bbe4d79f8f68b95fff541ec5d94c4108483f8c9", "size_in_bytes": 184}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/namespaces.cpython-311.pyc", "path_type": "hardlink", "sha256": "0b1eae84847a355ac74f720499841b45f67181407dfc39c614248cbf4872214f", "sha256_in_prefix": "0b1eae84847a355ac74f720499841b45f67181407dfc39c614248cbf4872214f", "size_in_bytes": 4637}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/script-with-bom.cpython-311.pyc", "path_type": "hardlink", "sha256": "06e289dfc4ddcb8d54f47db016b4092a63529925511d87b5c6103cbc3e1f3aae", "sha256_in_prefix": "06e289dfc4ddcb8d54f47db016b4092a63529925511d87b5c6103cbc3e1f3aae", "size_in_bytes": 178}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/server.cpython-311.pyc", "path_type": "hardlink", "sha256": "b838291986e0f7aefd6ca01f1370fdb387dd048c0c55ad687419acd984fff041", "sha256_in_prefix": "b838291986e0f7aefd6ca01f1370fdb387dd048c0c55ad687419acd984fff041", "size_in_bytes": 5229}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_archive_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "9e24e9a5351724d449e40ae356793ca074517bca37c94cdb9c80dfa1ebabd558", "sha256_in_prefix": "9e24e9a5351724d449e40ae356793ca074517bca37c94cdb9c80dfa1ebabd558", "size_in_bytes": 2125}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_deprecations.cpython-311.pyc", "path_type": "hardlink", "sha256": "f81afed3459d84157325c9323e22d395567e882301ca1a8c681c9455017d234f", "sha256_in_prefix": "f81afed3459d84157325c9323e22d395567e882301ca1a8c681c9455017d234f", "size_in_bytes": 1771}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_egg.cpython-311.pyc", "path_type": "hardlink", "sha256": "d1655f7cfb4bd924afa5132ad2a2f8f0e4fdfae28ed16f2b027eafaac96077dd", "sha256_in_prefix": "d1655f7cfb4bd924afa5132ad2a2f8f0e4fdfae28ed16f2b027eafaac96077dd", "size_in_bytes": 4557}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "f48135aa266aebd67b7d4287ab3a613129c15a92975bbd5bf95040c3fbf05410", "sha256_in_prefix": "f48135aa266aebd67b7d4287ab3a613129c15a92975bbd5bf95040c3fbf05410", "size_in_bytes": 33847}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build.cpython-311.pyc", "path_type": "hardlink", "sha256": "cbe8fda1611ac9ff2957b996495fd4193d56364557cd5870b72fd14971f50393", "sha256_in_prefix": "cbe8fda1611ac9ff2957b996495fd4193d56364557cd5870b72fd14971f50393", "size_in_bytes": 1808}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_clib.cpython-311.pyc", "path_type": "hardlink", "sha256": "578c520e0cf6b9d351c075a649ee82c2f757019325185212199041de5a0c173b", "sha256_in_prefix": "578c520e0cf6b9d351c075a649ee82c2f757019325185212199041de5a0c173b", "size_in_bytes": 4664}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_ext.cpython-311.pyc", "path_type": "hardlink", "sha256": "306a33984f20e787cc5550a490949a80261442ea4657c747215b5110928e0260", "sha256_in_prefix": "306a33984f20e787cc5550a490949a80261442ea4657c747215b5110928e0260", "size_in_bytes": 15067}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_meta.cpython-311.pyc", "path_type": "hardlink", "sha256": "55b453c93e837a0fe3e6d15cdf8db3dbefaf1c818d4f11b839d999a7407e8c5c", "sha256_in_prefix": "55b453c93e837a0fe3e6d15cdf8db3dbefaf1c818d4f11b839d999a7407e8c5c", "size_in_bytes": 49331}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_py.cpython-311.pyc", "path_type": "hardlink", "sha256": "77b08b592a7cdc66d08027a527157b8a193008d5cc94f1ce7ce47f3fde8485bc", "sha256_in_prefix": "77b08b592a7cdc66d08027a527157b8a193008d5cc94f1ce7ce47f3fde8485bc", "size_in_bytes": 19246}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_config_discovery.cpython-311.pyc", "path_type": "hardlink", "sha256": "19c001eefd09879bbbce73fa3f06004db6edf465b5b2112d297de405bd685078", "sha256_in_prefix": "19c001eefd09879bbbce73fa3f06004db6edf465b5b2112d297de405bd685078", "size_in_bytes": 35351}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_core_metadata.cpython-311.pyc", "path_type": "hardlink", "sha256": "b10f8df3fd109d4e2b305dd06152234f97471f8801ceefd18e70964dad028d7a", "sha256_in_prefix": "b10f8df3fd109d4e2b305dd06152234f97471f8801ceefd18e70964dad028d7a", "size_in_bytes": 14373}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_depends.cpython-311.pyc", "path_type": "hardlink", "sha256": "4571d720f5b24cceb23653e01de0f1f1c5837897c23a36207a9a866fa273b615", "sha256_in_prefix": "4571d720f5b24cceb23653e01de0f1f1c5837897c23a36207a9a866fa273b615", "size_in_bytes": 1000}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_develop.cpython-311.pyc", "path_type": "hardlink", "sha256": "878f89c3f0b644b01d2517d2bc4c941738080ae9fe2d366f5f27366ad90aa843", "sha256_in_prefix": "878f89c3f0b644b01d2517d2bc4c941738080ae9fe2d366f5f27366ad90aa843", "size_in_bytes": 10112}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_dist.cpython-311.pyc", "path_type": "hardlink", "sha256": "0f257cb15bdfe290359d4c9dd3dbbcbf6044e10c67526b00281da83c771ad948", "sha256_in_prefix": "0f257cb15bdfe290359d4c9dd3dbbcbf6044e10c67526b00281da83c771ad948", "size_in_bytes": 13021}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_dist_info.cpython-311.pyc", "path_type": "hardlink", "sha256": "52cf1ef8d2c3a2589786314b54a2fa0f2b468b6a8e53b4e09134ddbdfabdb794", "sha256_in_prefix": "52cf1ef8d2c3a2589786314b54a2fa0f2b468b6a8e53b4e09134ddbdfabdb794", "size_in_bytes": 12399}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_distutils_adoption.cpython-311.pyc", "path_type": "hardlink", "sha256": "6b1fbcbd4b79a0f6d4787b2a5a4f6a8ffa5238ef7a1594b33f4628c65b04d8b4", "sha256_in_prefix": "6b1fbcbd4b79a0f6d4787b2a5a4f6a8ffa5238ef7a1594b33f4628c65b04d8b4", "size_in_bytes": 7372}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_easy_install.cpython-311.pyc", "path_type": "hardlink", "sha256": "4976f08e9a250fc64aca2d6ed2c62fc271905f9d99100998857d220827f8179f", "sha256_in_prefix": "4976f08e9a250fc64aca2d6ed2c62fc271905f9d99100998857d220827f8179f", "size_in_bytes": 81133}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_editable_install.cpython-311.pyc", "path_type": "hardlink", "sha256": "b9385c7a9c6517bc50f17c390c0e5ae0d1a12e00ef6492cc74d202fb2ba97b17", "sha256_in_prefix": "b9385c7a9c6517bc50f17c390c0e5ae0d1a12e00ef6492cc74d202fb2ba97b17", "size_in_bytes": 67799}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_egg_info.cpython-311.pyc", "path_type": "hardlink", "sha256": "ada6162c50479334c3e66c99534722ee88e5ae132237ff919670f7b86bde23d0", "sha256_in_prefix": "ada6162c50479334c3e66c99534722ee88e5ae132237ff919670f7b86bde23d0", "size_in_bytes": 54333}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_extern.cpython-311.pyc", "path_type": "hardlink", "sha256": "469d80dbf6b8afcbf50fa94245098bba4c0db0fafc088bd04f4b9e78146d72c6", "sha256_in_prefix": "469d80dbf6b8afcbf50fa94245098bba4c0db0fafc088bd04f4b9e78146d72c6", "size_in_bytes": 890}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_find_packages.cpython-311.pyc", "path_type": "hardlink", "sha256": "58a70cd3353e6d6c51a6bdc64c67f7084c5e5d48767a731e3bf7c7a96f9e8ead", "sha256_in_prefix": "58a70cd3353e6d6c51a6bdc64c67f7084c5e5d48767a731e3bf7c7a96f9e8ead", "size_in_bytes": 13108}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_find_py_modules.cpython-311.pyc", "path_type": "hardlink", "sha256": "ceecb908fc157025d1530636e356c13a7456d318d692b56a01b48f4efc578026", "sha256_in_prefix": "ceecb908fc157025d1530636e356c13a7456d318d692b56a01b48f4efc578026", "size_in_bytes": 4204}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_glob.cpython-311.pyc", "path_type": "hardlink", "sha256": "cab2c58a21220fda4ad7143807fd1cfca19e04d4b073b79b2aafaef3605d49b0", "sha256_in_prefix": "cab2c58a21220fda4ad7143807fd1cfca19e04d4b073b79b2aafaef3605d49b0", "size_in_bytes": 1503}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_install_scripts.cpython-311.pyc", "path_type": "hardlink", "sha256": "7cf4573c2fcd463835e924e78144be2be30249040097d28e71e610ab9bfdab2c", "sha256_in_prefix": "7cf4573c2fcd463835e924e78144be2be30249040097d28e71e610ab9bfdab2c", "size_in_bytes": 7375}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_logging.cpython-311.pyc", "path_type": "hardlink", "sha256": "2923c5109cd424bc355b8d20d6d903c4ab2dbb8fe41aae930e45ee3e03f3f62b", "sha256_in_prefix": "2923c5109cd424bc355b8d20d6d903c4ab2dbb8fe41aae930e45ee3e03f3f62b", "size_in_bytes": 3418}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_manifest.cpython-311.pyc", "path_type": "hardlink", "sha256": "eee49652a1b89874827ddba42359c35585451ee970f79e41764d5ff8921e9cd5", "sha256_in_prefix": "eee49652a1b89874827ddba42359c35585451ee970f79e41764d5ff8921e9cd5", "size_in_bytes": 30792}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_namespaces.cpython-311.pyc", "path_type": "hardlink", "sha256": "5676f1a039fe11f068e75e1066b13eb4a0c355a26b31cd86441be1c8c884bf31", "sha256_in_prefix": "5676f1a039fe11f068e75e1066b13eb4a0c355a26b31cd86441be1c8c884bf31", "size_in_bytes": 6347}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_packageindex.cpython-311.pyc", "path_type": "hardlink", "sha256": "6c59dca130a2ad45a830947dcfc293d7fde4e38aee648d68417666392d49025c", "sha256_in_prefix": "6c59dca130a2ad45a830947dcfc293d7fde4e38aee648d68417666392d49025c", "size_in_bytes": 20245}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_sandbox.cpython-311.pyc", "path_type": "hardlink", "sha256": "6890fc6177987a89e8a7ccb8de528e436c1d2528f9e64b9b672d2030428b591c", "sha256_in_prefix": "6890fc6177987a89e8a7ccb8de528e436c1d2528f9e64b9b672d2030428b591c", "size_in_bytes": 11441}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_sdist.cpython-311.pyc", "path_type": "hardlink", "sha256": "536c48fcc5fc5a9fdee3e577def80891ab32fe7bd5aa7efb06d31010ff17eb16", "sha256_in_prefix": "536c48fcc5fc5a9fdee3e577def80891ab32fe7bd5aa7efb06d31010ff17eb16", "size_in_bytes": 52908}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_setopt.cpython-311.pyc", "path_type": "hardlink", "sha256": "a3fde2994c44ab7ed4323604a66fc11f89030ab10865875a0f54029f83ad64e4", "sha256_in_prefix": "a3fde2994c44ab7ed4323604a66fc11f89030ab10865875a0f54029f83ad64e4", "size_in_bytes": 3435}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_setuptools.cpython-311.pyc", "path_type": "hardlink", "sha256": "a22324cdf384353702b730dd463f70655cb0c6a09b8a1372fc3e73d3754a4259", "sha256_in_prefix": "a22324cdf384353702b730dd463f70655cb0c6a09b8a1372fc3e73d3754a4259", "size_in_bytes": 20302}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_unicode_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "9b97b92a5743df25f59c06cc51b2bbcb74645464d222f6f6640e3931a17d2380", "sha256_in_prefix": "9b97b92a5743df25f59c06cc51b2bbcb74645464d222f6f6640e3931a17d2380", "size_in_bytes": 829}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_virtualenv.cpython-311.pyc", "path_type": "hardlink", "sha256": "997c504386bfef89f78b696f794aaf39d2a9e9b52c9b664caa63b94f42e3c4ab", "sha256_in_prefix": "997c504386bfef89f78b696f794aaf39d2a9e9b52c9b664caa63b94f42e3c4ab", "size_in_bytes": 4903}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_warnings.cpython-311.pyc", "path_type": "hardlink", "sha256": "efdef2d42b752775cd07fc32016030b7b8aac81dc83b15dc112c376d3244cc6a", "sha256_in_prefix": "efdef2d42b752775cd07fc32016030b7b8aac81dc83b15dc112c376d3244cc6a", "size_in_bytes": 4622}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "3be857325f93ea0b684998def57f82b9b21aa129ad9c1d649d0882f45aa58ff9", "sha256_in_prefix": "3be857325f93ea0b684998def57f82b9b21aa129ad9c1d649d0882f45aa58ff9", "size_in_bytes": 23391}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_windows_wrappers.cpython-311.pyc", "path_type": "hardlink", "sha256": "93481468705ed37ab8643faecc91fb571a9aff61770ca08de9559e2fcb3d067f", "sha256_in_prefix": "93481468705ed37ab8643faecc91fb571a9aff61770ca08de9559e2fcb3d067f", "size_in_bytes": 11497}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/text.cpython-311.pyc", "path_type": "hardlink", "sha256": "ab96c6fabdcc91950d87a702467180dd92014f97ca7b686915eb0d1639cb0dad", "sha256_in_prefix": "ab96c6fabdcc91950d87a702467180dd92014f97ca7b686915eb0d1639cb0dad", "size_in_bytes": 563}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/textwrap.cpython-311.pyc", "path_type": "hardlink", "sha256": "06ab4723b77168a370958887655bdd3b79b26702488ba267c77f38fc7463bb97", "sha256_in_prefix": "06ab4723b77168a370958887655bdd3b79b26702488ba267c77f38fc7463bb97", "size_in_bytes": 433}, {"_path": "Lib/site-packages/setuptools/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/tests/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "22057e303c32c12d3420d214a82bfadb6eaea650b8e721b86cd728d0df71f198", "sha256_in_prefix": "22057e303c32c12d3420d214a82bfadb6eaea650b8e721b86cd728d0df71f198", "size_in_bytes": 162}, {"_path": "Lib/site-packages/setuptools/tests/compat/__pycache__/py39.cpython-311.pyc", "path_type": "hardlink", "sha256": "624bbcdb7c2844a406c8331c1dcfc7f9bbcaf6c79f0438ca06eb4c672dc4046c", "sha256_in_prefix": "624bbcdb7c2844a406c8331c1dcfc7f9bbcaf6c79f0438ca06eb4c672dc4046c", "size_in_bytes": 363}, {"_path": "Lib/site-packages/setuptools/tests/compat/py39.py", "path_type": "hardlink", "sha256": "794cbbfc5fba2914ce20a97ebdeb2152ee88d0475349d059321d04574959d7e8", "sha256_in_prefix": "794cbbfc5fba2914ce20a97ebdeb2152ee88d0475349d059321d04574959d7e8", "size_in_bytes": 135}, {"_path": "Lib/site-packages/setuptools/tests/config/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "2d771ff771ef7b336bcdbc3e449fa467fc0729ac100ebf8eaef8c148d6962e5f", "sha256_in_prefix": "2d771ff771ef7b336bcdbc3e449fa467fc0729ac100ebf8eaef8c148d6962e5f", "size_in_bytes": 162}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_apply_pyprojecttoml.cpython-311.pyc", "path_type": "hardlink", "sha256": "22002f600b691215831db7d9bdd077a5e20c759367c91213626a853572e903a7", "sha256_in_prefix": "22002f600b691215831db7d9bdd077a5e20c759367c91213626a853572e903a7", "size_in_bytes": 32546}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_expand.cpython-311.pyc", "path_type": "hardlink", "sha256": "c383b823dc5eeeb007852c603b9c6e02fed93efad0a0302ae53e23af47e95c5b", "sha256_in_prefix": "c383b823dc5eeeb007852c603b9c6e02fed93efad0a0302ae53e23af47e95c5b", "size_in_bytes": 12762}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml.cpython-311.pyc", "path_type": "hardlink", "sha256": "c6ed715be016df5a76bd880304180be6fd86250165e0f9cef2cd8412530db394", "sha256_in_prefix": "c6ed715be016df5a76bd880304180be6fd86250165e0f9cef2cd8412530db394", "size_in_bytes": 18441}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml_dynamic_deps.cpython-311.pyc", "path_type": "hardlink", "sha256": "36654de6829d50d801a27d5370e3a983c73451f49934b6add6f06d9ffa43cf8f", "sha256_in_prefix": "36654de6829d50d801a27d5370e3a983c73451f49934b6add6f06d9ffa43cf8f", "size_in_bytes": 3996}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_setupcfg.cpython-311.pyc", "path_type": "hardlink", "sha256": "1d1921e80637406f79f16d3d0f79b94e0532307d016fc9da48764e389a0a1534", "sha256_in_prefix": "1d1921e80637406f79f16d3d0f79b94e0532307d016fc9da48764e389a0a1534", "size_in_bytes": 53153}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/__init__.py", "path_type": "hardlink", "sha256": "d16ac85d90df1671644558a8ddaf99e917df44c65b73702e26cb56693ae8af18", "sha256_in_prefix": "d16ac85d90df1671644558a8ddaf99e917df44c65b73702e26cb56693ae8af18", "size_in_bytes": 1762}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "56c4856db936a55ca9dce58be101aeed642cd3d24096d96e9d184939b0da5c90", "sha256_in_prefix": "56c4856db936a55ca9dce58be101aeed642cd3d24096d96e9d184939b0da5c90", "size_in_bytes": 3748}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/preload.cpython-311.pyc", "path_type": "hardlink", "sha256": "2c2654f33490c7aab2439b53899aee0d85c3688e4e495b683817b580421c6039", "sha256_in_prefix": "2c2654f33490c7aab2439b53899aee0d85c3688e4e495b683817b580421c6039", "size_in_bytes": 854}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/preload.py", "path_type": "hardlink", "sha256": "b081866696377263293308896186181c6da27d9264bc9804a2d445b62ba55752", "sha256_in_prefix": "b081866696377263293308896186181c6da27d9264bc9804a2d445b62ba55752", "size_in_bytes": 450}, {"_path": "Lib/site-packages/setuptools/tests/config/setupcfg_examples.txt", "path_type": "hardlink", "sha256": "7006d5bc26e4159b9350beb1451cd182ac81d2b2ef2537efc370f7d20968d8e1", "sha256_in_prefix": "7006d5bc26e4159b9350beb1451cd182ac81d2b2ef2537efc370f7d20968d8e1", "size_in_bytes": 1912}, {"_path": "Lib/site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py", "path_type": "hardlink", "sha256": "715f73023ad9fffb01aedb13ec60a740b74aa6cbae6e589907e3a107a302c4c9", "sha256_in_prefix": "715f73023ad9fffb01aedb13ec60a740b74aa6cbae6e589907e3a107a302c4c9", "size_in_bytes": 19258}, {"_path": "Lib/site-packages/setuptools/tests/config/test_expand.py", "path_type": "hardlink", "sha256": "c899a1ab9d8ca933c47ede9039295947cc8de9d90956d94e713a3d0fba6a1975", "sha256_in_prefix": "c899a1ab9d8ca933c47ede9039295947cc8de9d90956d94e713a3d0fba6a1975", "size_in_bytes": 8111}, {"_path": "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml.py", "path_type": "hardlink", "sha256": "33141ec0c72765c65364086825b191315bd1b3679195b1d0b2700904eaa51de7", "sha256_in_prefix": "33141ec0c72765c65364086825b191315bd1b3679195b1d0b2700904eaa51de7", "size_in_bytes": 12398}, {"_path": "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py", "path_type": "hardlink", "sha256": "8777fd7cfcbea31b42fc73e0c6d3063352e0745cf4d5162d4cbe3bb9b159a793", "sha256_in_prefix": "8777fd7cfcbea31b42fc73e0c6d3063352e0745cf4d5162d4cbe3bb9b159a793", "size_in_bytes": 3072}, {"_path": "Lib/site-packages/setuptools/tests/config/test_setupcfg.py", "path_type": "hardlink", "sha256": "b75dacbae8cb5b813ec404437c4c72d354664a4185d65da5b01bd7aa104f7edb", "sha256_in_prefix": "b75dacbae8cb5b813ec404437c4c72d354664a4185d65da5b01bd7aa104f7edb", "size_in_bytes": 33361}, {"_path": "Lib/site-packages/setuptools/tests/contexts.py", "path_type": "hardlink", "sha256": "4c07592b19a6a1dc75131315a34d68e10a518e9229a385f72162aafc19e3c695", "sha256_in_prefix": "4c07592b19a6a1dc75131315a34d68e10a518e9229a385f72162aafc19e3c695", "size_in_bytes": 3480}, {"_path": "Lib/site-packages/setuptools/tests/environment.py", "path_type": "hardlink", "sha256": "f79fd4b536918aebf0602f5e5ca1076e7d36903b59cacbd9ab75192663d48f52", "sha256_in_prefix": "f79fd4b536918aebf0602f5e5ca1076e7d36903b59cacbd9ab75192663d48f52", "size_in_bytes": 3102}, {"_path": "Lib/site-packages/setuptools/tests/fixtures.py", "path_type": "hardlink", "sha256": "f95ee20fa05e136134470e9d56f4ce0a6dfa246f194d39eb5e13741884a582b8", "sha256_in_prefix": "f95ee20fa05e136134470e9d56f4ce0a6dfa246f194d39eb5e13741884a582b8", "size_in_bytes": 5197}, {"_path": "Lib/site-packages/setuptools/tests/indexes/test_links_priority/external.html", "path_type": "hardlink", "sha256": "78bf5eb8eb84f7724a65daa55f104e9476cac08b8db8876aec6051a6c68f31c5", "sha256_in_prefix": "78bf5eb8eb84f7724a65daa55f104e9476cac08b8db8876aec6051a6c68f31c5", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/index.html", "path_type": "hardlink", "sha256": "0c3f932abed4538cc08c71f3e157b1603352033476ee57af4a1d5cfa4dd974b1", "sha256_in_prefix": "0c3f932abed4538cc08c71f3e157b1603352033476ee57af4a1d5cfa4dd974b1", "size_in_bytes": 174}, {"_path": "Lib/site-packages/setuptools/tests/integration/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/tests/integration/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "75794637373219ee2d0d7da5f0d3d6572a39fe3a441c70f32654f8d3cc566e6b", "sha256_in_prefix": "75794637373219ee2d0d7da5f0d3d6572a39fe3a441c70f32654f8d3cc566e6b", "size_in_bytes": 167}, {"_path": "Lib/site-packages/setuptools/tests/integration/__pycache__/helpers.cpython-311.pyc", "path_type": "hardlink", "sha256": "4abcc35d3ebc50f8662ecd0998b2f48d60ce97142ac247a9d2fd745645f3998b", "sha256_in_prefix": "4abcc35d3ebc50f8662ecd0998b2f48d60ce97142ac247a9d2fd745645f3998b", "size_in_bytes": 5332}, {"_path": "Lib/site-packages/setuptools/tests/integration/__pycache__/test_pip_install_sdist.cpython-311.pyc", "path_type": "hardlink", "sha256": "9c12e359921877557b7cf93bfddb9ce708072951c50f6e36cef10d8204918bef", "sha256_in_prefix": "9c12e359921877557b7cf93bfddb9ce708072951c50f6e36cef10d8204918bef", "size_in_bytes": 10795}, {"_path": "Lib/site-packages/setuptools/tests/integration/helpers.py", "path_type": "hardlink", "sha256": "dcf1dc4bd48203e7f05499943f669de4d40eb6d240113239367a1cff1ae83b99", "sha256_in_prefix": "dcf1dc4bd48203e7f05499943f669de4d40eb6d240113239367a1cff1ae83b99", "size_in_bytes": 2522}, {"_path": "Lib/site-packages/setuptools/tests/integration/test_pip_install_sdist.py", "path_type": "hardlink", "sha256": "79bcf6253be29db1345ca9be2371da4a4ba79baa0494d688cdcad37ef7fa3984", "sha256_in_prefix": "79bcf6253be29db1345ca9be2371da4a4ba79baa0494d688cdcad37ef7fa3984", "size_in_bytes": 8288}, {"_path": "Lib/site-packages/setuptools/tests/mod_with_constant.py", "path_type": "hardlink", "sha256": "5ff2a3f34339e70d6d990e1feee658f7565300ba3884a553e841f1818a1c50c4", "sha256_in_prefix": "5ff2a3f34339e70d6d990e1feee658f7565300ba3884a553e841f1818a1c50c4", "size_in_bytes": 22}, {"_path": "Lib/site-packages/setuptools/tests/namespaces.py", "path_type": "hardlink", "sha256": "1cf708de74793021565e96800c82757f02b1ca671080192ec3cec87393d44804", "sha256_in_prefix": "1cf708de74793021565e96800c82757f02b1ca671080192ec3cec87393d44804", "size_in_bytes": 2774}, {"_path": "Lib/site-packages/setuptools/tests/script-with-bom.py", "path_type": "hardlink", "sha256": "851460222cc450b1a21bf653368318e3a1e12a1c6959fcb9146703e906e1e5f7", "sha256_in_prefix": "851460222cc450b1a21bf653368318e3a1e12a1c6959fcb9146703e906e1e5f7", "size_in_bytes": 18}, {"_path": "Lib/site-packages/setuptools/tests/server.py", "path_type": "hardlink", "sha256": "65124e28a4065286b4de28cf16b162a8d21de36f046fdbb2ff2b0e66d6a050db", "sha256_in_prefix": "65124e28a4065286b4de28cf16b162a8d21de36f046fdbb2ff2b0e66d6a050db", "size_in_bytes": 2403}, {"_path": "Lib/site-packages/setuptools/tests/test_archive_util.py", "path_type": "hardlink", "sha256": "6eeb8a758f17916dba3dedc8280a014461c6d0c0ee9a7b8da0f8365ac010cc88", "sha256_in_prefix": "6eeb8a758f17916dba3dedc8280a014461c6d0c0ee9a7b8da0f8365ac010cc88", "size_in_bytes": 845}, {"_path": "Lib/site-packages/setuptools/tests/test_bdist_deprecations.py", "path_type": "hardlink", "sha256": "ef95eade0627efd2c8232bac125b5b1da9f46c4800b767bf09a2fb28b4bcf8a4", "sha256_in_prefix": "ef95eade0627efd2c8232bac125b5b1da9f46c4800b767bf09a2fb28b4bcf8a4", "size_in_bytes": 775}, {"_path": "Lib/site-packages/setuptools/tests/test_bdist_egg.py", "path_type": "hardlink", "sha256": "71d7d9b16a459834526d9795bbeebd4f442209cfb6e3aadb523b64b81ee20626", "sha256_in_prefix": "71d7d9b16a459834526d9795bbeebd4f442209cfb6e3aadb523b64b81ee20626", "size_in_bytes": 1851}, {"_path": "Lib/site-packages/setuptools/tests/test_bdist_wheel.py", "path_type": "hardlink", "sha256": "0fe4cd8b73498677967f4c890a6a59bf6ade3f87e7b9f6c9c14e34317b0c6909", "sha256_in_prefix": "0fe4cd8b73498677967f4c890a6a59bf6ade3f87e7b9f6c9c14e34317b0c6909", "size_in_bytes": 19480}, {"_path": "Lib/site-packages/setuptools/tests/test_build.py", "path_type": "hardlink", "sha256": "c0980ccf68701c00dc2c583e9d7af045586eb3b8639807841a0ae9210c021a70", "sha256_in_prefix": "c0980ccf68701c00dc2c583e9d7af045586eb3b8639807841a0ae9210c021a70", "size_in_bytes": 798}, {"_path": "Lib/site-packages/setuptools/tests/test_build_clib.py", "path_type": "hardlink", "sha256": "6d7e755d101fe2e3bb22e1c5a6378f9e82bc984ef837682ca1e12a17ea1f830b", "sha256_in_prefix": "6d7e755d101fe2e3bb22e1c5a6378f9e82bc984ef837682ca1e12a17ea1f830b", "size_in_bytes": 3123}, {"_path": "Lib/site-packages/setuptools/tests/test_build_ext.py", "path_type": "hardlink", "sha256": "5e940f141ba1bd71fbd4fe9868b983c7f8d7cdf0f0ea3d7b10a52b72af2f9196", "sha256_in_prefix": "5e940f141ba1bd71fbd4fe9868b983c7f8d7cdf0f0ea3d7b10a52b72af2f9196", "size_in_bytes": 10036}, {"_path": "Lib/site-packages/setuptools/tests/test_build_meta.py", "path_type": "hardlink", "sha256": "8a92dbf9ec21a11daa9deb4523cbf60d9320449e8cea6a9d839d8e871e9556ed", "sha256_in_prefix": "8a92dbf9ec21a11daa9deb4523cbf60d9320449e8cea6a9d839d8e871e9556ed", "size_in_bytes": 33570}, {"_path": "Lib/site-packages/setuptools/tests/test_build_py.py", "path_type": "hardlink", "sha256": "d8c3a23df67d0afc0839de53355ea10aaf54ad70371d7266e6f8f918aee8a6cc", "sha256_in_prefix": "d8c3a23df67d0afc0839de53355ea10aaf54ad70371d7266e6f8f918aee8a6cc", "size_in_bytes": 14185}, {"_path": "Lib/site-packages/setuptools/tests/test_config_discovery.py", "path_type": "hardlink", "sha256": "e76fc711d03dedd81a32fb9db272190f2ec86193ef1dbe64e987fd000d7de780", "sha256_in_prefix": "e76fc711d03dedd81a32fb9db272190f2ec86193ef1dbe64e987fd000d7de780", "size_in_bytes": 22562}, {"_path": "Lib/site-packages/setuptools/tests/test_core_metadata.py", "path_type": "hardlink", "sha256": "ef15a695282323faffb54b1c055b7e920d893f5d04b7984c348d1ff2732d28f0", "sha256_in_prefix": "ef15a695282323faffb54b1c055b7e920d893f5d04b7984c348d1ff2732d28f0", "size_in_bytes": 12183}, {"_path": "Lib/site-packages/setuptools/tests/test_depends.py", "path_type": "hardlink", "sha256": "c90057a106cd425262b7a99b455a33e816f9e777f7b0daead369598a6373e576", "sha256_in_prefix": "c90057a106cd425262b7a99b455a33e816f9e777f7b0daead369598a6373e576", "size_in_bytes": 424}, {"_path": "Lib/site-packages/setuptools/tests/test_develop.py", "path_type": "hardlink", "sha256": "08bcd767cf9be7e5454ee6aee0fe325c474bc7551dc9315c39fad5d2ac9421d1", "sha256_in_prefix": "08bcd767cf9be7e5454ee6aee0fe325c474bc7551dc9315c39fad5d2ac9421d1", "size_in_bytes": 5142}, {"_path": "Lib/site-packages/setuptools/tests/test_dist.py", "path_type": "hardlink", "sha256": "9c6ef2e3522e959f698c7f9b8b039cb5c40e69fa516cd633d08e6c0d7f1dd690", "sha256_in_prefix": "9c6ef2e3522e959f698c7f9b8b039cb5c40e69fa516cd633d08e6c0d7f1dd690", "size_in_bytes": 8811}, {"_path": "Lib/site-packages/setuptools/tests/test_dist_info.py", "path_type": "hardlink", "sha256": "7b1aaa5395d292c4625e352ee26c754a637efe39cfa083cf5b0b0ba8173c7d04", "sha256_in_prefix": "7b1aaa5395d292c4625e352ee26c754a637efe39cfa083cf5b0b0ba8173c7d04", "size_in_bytes": 7090}, {"_path": "Lib/site-packages/setuptools/tests/test_distutils_adoption.py", "path_type": "hardlink", "sha256": "669749db00b2ab6cc6c6c0407234f65fbd95b23d185c8a56fbb7e1ca76f7eb52", "sha256_in_prefix": "669749db00b2ab6cc6c6c0407234f65fbd95b23d185c8a56fbb7e1ca76f7eb52", "size_in_bytes": 4747}, {"_path": "Lib/site-packages/setuptools/tests/test_easy_install.py", "path_type": "hardlink", "sha256": "d2851e846b290e21973b28950ca8392cf62c366b5552167c14e841a0d6278a0b", "sha256_in_prefix": "d2851e846b290e21973b28950ca8392cf62c366b5552167c14e841a0d6278a0b", "size_in_bytes": 53241}, {"_path": "Lib/site-packages/setuptools/tests/test_editable_install.py", "path_type": "hardlink", "sha256": "5b693c62670ef6c09995e22312d2f56ff7bd335e8a3b92fd6b51a04e6371dae6", "sha256_in_prefix": "5b693c62670ef6c09995e22312d2f56ff7bd335e8a3b92fd6b51a04e6371dae6", "size_in_bytes": 43304}, {"_path": "Lib/site-packages/setuptools/tests/test_egg_info.py", "path_type": "hardlink", "sha256": "32f256bc89f9619462def406a61e125b40061c27fbdc6dcbdc7773f409c06709", "sha256_in_prefix": "32f256bc89f9619462def406a61e125b40061c27fbdc6dcbdc7773f409c06709", "size_in_bytes": 44145}, {"_path": "Lib/site-packages/setuptools/tests/test_extern.py", "path_type": "hardlink", "sha256": "ae9294ea809c92cba62f07f94de2a50e5b854344d47db3f04cb41ba71705ac25", "sha256_in_prefix": "ae9294ea809c92cba62f07f94de2a50e5b854344d47db3f04cb41ba71705ac25", "size_in_bytes": 296}, {"_path": "Lib/site-packages/setuptools/tests/test_find_packages.py", "path_type": "hardlink", "sha256": "0932c0713cd619604b09c776680b14564bcede26eb96a7b114174328e58fa2af", "sha256_in_prefix": "0932c0713cd619604b09c776680b14564bcede26eb96a7b114174328e58fa2af", "size_in_bytes": 7819}, {"_path": "Lib/site-packages/setuptools/tests/test_find_py_modules.py", "path_type": "hardlink", "sha256": "cd08ee8481b94d03764893e2c7d011a380cbff0f382e7f10b070d48e36ebb404", "sha256_in_prefix": "cd08ee8481b94d03764893e2c7d011a380cbff0f382e7f10b070d48e36ebb404", "size_in_bytes": 2404}, {"_path": "Lib/site-packages/setuptools/tests/test_glob.py", "path_type": "hardlink", "sha256": "a57314aa27487c61b64ccfb967d3364fb0f3fce2cf0fa5fe697f10894a45bf65", "sha256_in_prefix": "a57314aa27487c61b64ccfb967d3364fb0f3fce2cf0fa5fe697f10894a45bf65", "size_in_bytes": 881}, {"_path": "Lib/site-packages/setuptools/tests/test_install_scripts.py", "path_type": "hardlink", "sha256": "6c915788e62631dfbb66007d2ae7a1fef9a2881b530f78c314e0929334b2b3d4", "sha256_in_prefix": "6c915788e62631dfbb66007d2ae7a1fef9a2881b530f78c314e0929334b2b3d4", "size_in_bytes": 3441}, {"_path": "Lib/site-packages/setuptools/tests/test_logging.py", "path_type": "hardlink", "sha256": "f4c7aa27f8e0413944f4ce1637b62e142f626212ab4fd7ec24868a1b52fbde9e", "sha256_in_prefix": "f4c7aa27f8e0413944f4ce1637b62e142f626212ab4fd7ec24868a1b52fbde9e", "size_in_bytes": 2095}, {"_path": "Lib/site-packages/setuptools/tests/test_manifest.py", "path_type": "hardlink", "sha256": "378518f18d53be33a229eacb1d217a2e41ee7a75c697cb70ea8fa2be58f77b90", "sha256_in_prefix": "378518f18d53be33a229eacb1d217a2e41ee7a75c697cb70ea8fa2be58f77b90", "size_in_bytes": 18761}, {"_path": "Lib/site-packages/setuptools/tests/test_namespaces.py", "path_type": "hardlink", "sha256": "63abada1ee4f1c7a8bfc39606b0a81f45f17a6c5033efbf0d6c40c7a72b4e1ed", "sha256_in_prefix": "63abada1ee4f1c7a8bfc39606b0a81f45f17a6c5033efbf0d6c40c7a72b4e1ed", "size_in_bytes": 4515}, {"_path": "Lib/site-packages/setuptools/tests/test_packageindex.py", "path_type": "hardlink", "sha256": "cdec075d7e3c5e181898ec053ac870d1e353e344a1b14cede4c9d807d3f34473", "sha256_in_prefix": "cdec075d7e3c5e181898ec053ac870d1e353e344a1b14cede4c9d807d3f34473", "size_in_bytes": 8975}, {"_path": "Lib/site-packages/setuptools/tests/test_sandbox.py", "path_type": "hardlink", "sha256": "b0b68a05192f43736923464d7b02235a2c024e6bae89ef083e4250cb1fb9917d", "sha256_in_prefix": "b0b68a05192f43736923464d7b02235a2c024e6bae89ef083e4250cb1fb9917d", "size_in_bytes": 4333}, {"_path": "Lib/site-packages/setuptools/tests/test_sdist.py", "path_type": "hardlink", "sha256": "25d003b93d8c5a205221c34b777a3da02e32d9718fe456484fd0e9f3d45d5d3f", "sha256_in_prefix": "25d003b93d8c5a205221c34b777a3da02e32d9718fe456484fd0e9f3d45d5d3f", "size_in_bytes": 32440}, {"_path": "Lib/site-packages/setuptools/tests/test_setopt.py", "path_type": "hardlink", "sha256": "dd5c713380137cff8fe001a70e3a160a71ebe7e8bd0921104c5614d7e1539ef2", "sha256_in_prefix": "dd5c713380137cff8fe001a70e3a160a71ebe7e8bd0921104c5614d7e1539ef2", "size_in_bytes": 1365}, {"_path": "Lib/site-packages/setuptools/tests/test_setuptools.py", "path_type": "hardlink", "sha256": "3a9aeb76e514813c6e4c2c00b1223d4d49892458e3b6bfb76fab84b96e42358f", "sha256_in_prefix": "3a9aeb76e514813c6e4c2c00b1223d4d49892458e3b6bfb76fab84b96e42358f", "size_in_bytes": 8978}, {"_path": "Lib/site-packages/setuptools/tests/test_unicode_utils.py", "path_type": "hardlink", "sha256": "c567c4125f239100adf68b615135c97c599dc804c0160809b36b53c636ee99bc", "sha256_in_prefix": "c567c4125f239100adf68b615135c97c599dc804c0160809b36b53c636ee99bc", "size_in_bytes": 316}, {"_path": "Lib/site-packages/setuptools/tests/test_virtualenv.py", "path_type": "hardlink", "sha256": "83e9e30bff494c0b35615c7fd5d189fd0e919489cee2a295bbdf9702035be936", "sha256_in_prefix": "83e9e30bff494c0b35615c7fd5d189fd0e919489cee2a295bbdf9702035be936", "size_in_bytes": 3730}, {"_path": "Lib/site-packages/setuptools/tests/test_warnings.py", "path_type": "hardlink", "sha256": "cf0476cdc9c2782783a882d994938f01cbb23c7a03bc6bb53ad3956222cc93be", "sha256_in_prefix": "cf0476cdc9c2782783a882d994938f01cbb23c7a03bc6bb53ad3956222cc93be", "size_in_bytes": 3347}, {"_path": "Lib/site-packages/setuptools/tests/test_wheel.py", "path_type": "hardlink", "sha256": "a55e673d45f22751081b3ad651be5c303c2b28a2b60c87fc7bed38a46951ed86", "sha256_in_prefix": "a55e673d45f22751081b3ad651be5c303c2b28a2b60c87fc7bed38a46951ed86", "size_in_bytes": 19259}, {"_path": "Lib/site-packages/setuptools/tests/test_windows_wrappers.py", "path_type": "hardlink", "sha256": "d859a011f1465f9395d2454ea4f3c46480802fbe93adc35b7dadae4c2cecd77e", "sha256_in_prefix": "d859a011f1465f9395d2454ea4f3c46480802fbe93adc35b7dadae4c2cecd77e", "size_in_bytes": 7894}, {"_path": "Lib/site-packages/setuptools/tests/text.py", "path_type": "hardlink", "sha256": "6b5db5f7ba4c553bc1e85016434ba34fc7c84222c8589945025d5409a0d40df8", "sha256_in_prefix": "6b5db5f7ba4c553bc1e85016434ba34fc7c84222c8589945025d5409a0d40df8", "size_in_bytes": 123}, {"_path": "Lib/site-packages/setuptools/tests/textwrap.py", "path_type": "hardlink", "sha256": "14d34dabf322684271f3c3e7b1b250211c668f5aa681c00e0975d1b0e0cf24de", "sha256_in_prefix": "14d34dabf322684271f3c3e7b1b250211c668f5aa681c00e0975d1b0e0cf24de", "size_in_bytes": 98}, {"_path": "Lib/site-packages/setuptools/unicode_utils.py", "path_type": "hardlink", "sha256": "683bc86593ed327dde0b57294b8b5ad16ba065267bb29a0acb11aa65dec01f3c", "sha256_in_prefix": "683bc86593ed327dde0b57294b8b5ad16ba065267bb29a0acb11aa65dec01f3c", "size_in_bytes": 3181}, {"_path": "Lib/site-packages/setuptools/version.py", "path_type": "hardlink", "sha256": "58909e52ecaaef80289364de2bdf8e7b164ebbc5eb950cbbfb2d0112e58da2f4", "sha256_in_prefix": "58909e52ecaaef80289364de2bdf8e7b164ebbc5eb950cbbfb2d0112e58da2f4", "size_in_bytes": 161}, {"_path": "Lib/site-packages/setuptools/warnings.py", "path_type": "hardlink", "sha256": "5f3dde112ad811d3f47589557ab3fc040d1eb3e51b90544013880e9d7526c6e2", "sha256_in_prefix": "5f3dde112ad811d3f47589557ab3fc040d1eb3e51b90544013880e9d7526c6e2", "size_in_bytes": 3714}, {"_path": "Lib/site-packages/setuptools/wheel.py", "path_type": "hardlink", "sha256": "c3e17c9fda9d76eb0309a742f0f589964679baf95791a1b2b5082791e9d91a2c", "sha256_in_prefix": "c3e17c9fda9d76eb0309a742f0f589964679baf95791a1b2b5082791e9d91a2c", "size_in_bytes": 8628}, {"_path": "Lib/site-packages/setuptools/windows_support.py", "path_type": "hardlink", "sha256": "c16e0860b33506fed9d4c69ab8fdb198f8f2cbec249909d7772bd7b1c01ff5fc", "sha256_in_prefix": "c16e0860b33506fed9d4c69ab8fdb198f8f2cbec249909d7772bd7b1c01ff5fc", "size_in_bytes": 726}], "paths_version": 1}, "requested_spec": "None", "sha256": "31fa4fa6a25d95069af5677c6096ed9363804fdf54ad2be31f1256e1699d3bb0", "size": 2339387, "subdir": "win-64", "timestamp": 1726678611000, "url": "https://repo.anaconda.com/pkgs/main/win-64/setuptools-75.1.0-py311haa95532_0.conda", "version": "75.1.0"}