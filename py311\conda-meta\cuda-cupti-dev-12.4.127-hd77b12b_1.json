{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": ["cuda-cupti-static >=12.4.127"], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0", "cuda-cupti 12.4.127 hd77b12b_1"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-cupti-dev-12.4.127-hd77b12b_1", "files": ["Library/include/cuda_stdint.h", "Library/include/cupti.h", "Library/include/cupti_activity.h", "Library/include/cupti_activity_deprecated.h", "Library/include/cupti_callbacks.h", "Library/include/cupti_checkpoint.h", "Library/include/cupti_common.h", "Library/include/cupti_driver_cbid.h", "Library/include/cupti_events.h", "Library/include/cupti_metrics.h", "Library/include/cupti_nvtx_cbid.h", "Library/include/cupti_pcsampling.h", "Library/include/cupti_pcsampling_util.h", "Library/include/cupti_profiler_target.h", "Library/include/cupti_result.h", "Library/include/cupti_runtime_cbid.h", "Library/include/cupti_sass_metrics.h", "Library/include/cupti_target.h", "Library/include/cupti_version.h", "Library/include/generated_cudaD3D10_meta.h", "Library/include/generated_cudaD3D11_meta.h", "Library/include/generated_cudaD3D9_meta.h", "Library/include/generated_cudaGL_meta.h", "Library/include/generated_cuda_d3d10_interop_meta.h", "Library/include/generated_cuda_d3d11_interop_meta.h", "Library/include/generated_cuda_d3d9_interop_meta.h", "Library/include/generated_cuda_gl_interop_meta.h", "Library/include/generated_cuda_meta.h", "Library/include/generated_cuda_runtime_api_meta.h", "Library/include/generated_cudart_removed_meta.h", "Library/include/generated_nvtx_meta.h", "Library/include/nvperf_common.h", "Library/include/nvperf_cuda_host.h", "Library/include/nvperf_host.h", "Library/include/nvperf_target.h", "Library/lib/checkpoint.lib", "Library/lib/cupti.lib", "Library/lib/nvperf_host.lib", "Library/lib/nvperf_target.lib", "Library/lib/pcsamplingutil.lib"], "fn": "cuda-cupti-dev-12.4.127-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-cupti-dev-12.4.127-hd77b12b_1", "type": 1}, "md5": "7be81edc4d1d9b1896a38d2e57758c86", "name": "cuda-cupti-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-cupti-dev-12.4.127-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/include/cuda_stdint.h", "path_type": "hardlink", "sha256": "15cf80a04f152499300fc8a830e19abc1872eec803735692a91282aa07d08751", "sha256_in_prefix": "15cf80a04f152499300fc8a830e19abc1872eec803735692a91282aa07d08751", "size_in_bytes": 4205}, {"_path": "Library/include/cupti.h", "path_type": "hardlink", "sha256": "536e83ba6afddd41cb5f07beb390dd2732e2bb2fdc7cf90da68574c406d09dbc", "sha256_in_prefix": "536e83ba6afddd41cb5f07beb390dd2732e2bb2fdc7cf90da68574c406d09dbc", "size_in_bytes": 4820}, {"_path": "Library/include/cupti_activity.h", "path_type": "hardlink", "sha256": "3796fb651d9983e407f2d007d8089f53a218e96b167a5452ab23908176b18b6e", "sha256_in_prefix": "3796fb651d9983e407f2d007d8089f53a218e96b167a5452ab23908176b18b6e", "size_in_bytes": 218156}, {"_path": "Library/include/cupti_activity_deprecated.h", "path_type": "hardlink", "sha256": "143fb4c2fca3090a2b7337e43347bb86483d85d5a99baf50b908f680916bbdd7", "sha256_in_prefix": "143fb4c2fca3090a2b7337e43347bb86483d85d5a99baf50b908f680916bbdd7", "size_in_bytes": 126310}, {"_path": "Library/include/cupti_callbacks.h", "path_type": "hardlink", "sha256": "9284e78c53d19528d020ee84b2036ae210fa392c8b8f6cba05e0cecb00471a94", "sha256_in_prefix": "9284e78c53d19528d020ee84b2036ae210fa392c8b8f6cba05e0cecb00471a94", "size_in_bytes": 30589}, {"_path": "Library/include/cupti_checkpoint.h", "path_type": "hardlink", "sha256": "1c7f8b9a3ce702039bd344c5c3e58208d46526a611fff246e03ea90e36a968e9", "sha256_in_prefix": "1c7f8b9a3ce702039bd344c5c3e58208d46526a611fff246e03ea90e36a968e9", "size_in_bytes": 5391}, {"_path": "Library/include/cupti_common.h", "path_type": "hardlink", "sha256": "fd80cc339e1eb0f3a175ff93b1d0b66ef417c4d5cb730cd327dbe0086a726bc0", "sha256_in_prefix": "fd80cc339e1eb0f3a175ff93b1d0b66ef417c4d5cb730cd327dbe0086a726bc0", "size_in_bytes": 3607}, {"_path": "Library/include/cupti_driver_cbid.h", "path_type": "hardlink", "sha256": "7cf5ee908f7254a188a8a6df3886b1d4bde84061b5cd57c0fcb0b7a6d40dd4f3", "sha256_in_prefix": "7cf5ee908f7254a188a8a6df3886b1d4bde84061b5cd57c0fcb0b7a6d40dd4f3", "size_in_bytes": 75229}, {"_path": "Library/include/cupti_events.h", "path_type": "hardlink", "sha256": "d29a626c36caa72a7a32d81889e6b26a3e2bf23299e9a308121e58f76aeb7723", "sha256_in_prefix": "d29a626c36caa72a7a32d81889e6b26a3e2bf23299e9a308121e58f76aeb7723", "size_in_bytes": 53246}, {"_path": "Library/include/cupti_metrics.h", "path_type": "hardlink", "sha256": "d6ef3eb2c32728abdd7da530604a611e154d5777279fac7af37b3cdf2ef72ad4", "sha256_in_prefix": "d6ef3eb2c32728abdd7da530604a611e154d5777279fac7af37b3cdf2ef72ad4", "size_in_bytes": 32973}, {"_path": "Library/include/cupti_nvtx_cbid.h", "path_type": "hardlink", "sha256": "bfa701f28a09cb9f91e0134ecdf328400a6c2db284ce16c691de952504af4374", "sha256_in_prefix": "bfa701f28a09cb9f91e0134ecdf328400a6c2db284ce16c691de952504af4374", "size_in_bytes": 6023}, {"_path": "Library/include/cupti_pcsampling.h", "path_type": "hardlink", "sha256": "4d5b87e6cbe65fe0dcc558965c86505ef773455b39750ef94f7ff75a96fce424", "sha256_in_prefix": "4d5b87e6cbe65fe0dcc558965c86505ef773455b39750ef94f7ff75a96fce424", "size_in_bytes": 33080}, {"_path": "Library/include/cupti_pcsampling_util.h", "path_type": "hardlink", "sha256": "eaf1e3537f6b6cdf7c58cb88d17449ef01637ea70e7cdc263bc956f04f593510", "sha256_in_prefix": "eaf1e3537f6b6cdf7c58cb88d17449ef01637ea70e7cdc263bc956f04f593510", "size_in_bytes": 12832}, {"_path": "Library/include/cupti_profiler_target.h", "path_type": "hardlink", "sha256": "fec910f6d81947b704b4ac1548d5d6071c65affe20690892c215779f1b6029f6", "sha256_in_prefix": "fec910f6d81947b704b4ac1548d5d6071c65affe20690892c215779f1b6029f6", "size_in_bytes": 32732}, {"_path": "Library/include/cupti_result.h", "path_type": "hardlink", "sha256": "44e0a4624b53bbdc41351d15f396631abc27331755f8245b98f599f7fb5efff2", "sha256_in_prefix": "44e0a4624b53bbdc41351d15f396631abc27331755f8245b98f599f7fb5efff2", "size_in_bytes": 12949}, {"_path": "Library/include/cupti_runtime_cbid.h", "path_type": "hardlink", "sha256": "f944b378887cd4b44f22025e46fdcdf977ad75847911403d4c7bc4742793a68a", "sha256_in_prefix": "f944b378887cd4b44f22025e46fdcdf977ad75847911403d4c7bc4742793a68a", "size_in_bytes": 46917}, {"_path": "Library/include/cupti_sass_metrics.h", "path_type": "hardlink", "sha256": "0c71609f41bfbed405b1e05012056957e1afa47716580a83688cfb01f97f2c63", "sha256_in_prefix": "0c71609f41bfbed405b1e05012056957e1afa47716580a83688cfb01f97f2c63", "size_in_bytes": 20109}, {"_path": "Library/include/cupti_target.h", "path_type": "hardlink", "sha256": "9f1c28a369cf043de7dc441880292685ceb6d3cc60489b2e151954f517523229", "sha256_in_prefix": "9f1c28a369cf043de7dc441880292685ceb6d3cc60489b2e151954f517523229", "size_in_bytes": 1306}, {"_path": "Library/include/cupti_version.h", "path_type": "hardlink", "sha256": "53d1e94725095424be72fd775b6c331eb98b656cdfb59253db45ffd374cb9447", "sha256_in_prefix": "53d1e94725095424be72fd775b6c331eb98b656cdfb59253db45ffd374cb9447", "size_in_bytes": 4559}, {"_path": "Library/include/generated_cudaD3D10_meta.h", "path_type": "hardlink", "sha256": "3d21cf541fc64a3e8d111477fc94dc9ebbfed929aea5609eb69602f32745db8a", "sha256_in_prefix": "3d21cf541fc64a3e8d111477fc94dc9ebbfed929aea5609eb69602f32745db8a", "size_in_bytes": 4439}, {"_path": "Library/include/generated_cudaD3D11_meta.h", "path_type": "hardlink", "sha256": "fa87ddc95dab6f95e25d319aea7c64020f673e42e6b1ab963a3710bbc461d16d", "sha256_in_prefix": "fa87ddc95dab6f95e25d319aea7c64020f673e42e6b1ab963a3710bbc461d16d", "size_in_bytes": 1823}, {"_path": "Library/include/generated_cudaD3D9_meta.h", "path_type": "hardlink", "sha256": "89a25b6eee7cbc560ecaee18b97d9007122d1f4f62a096dae0296f30655fddd0", "sha256_in_prefix": "89a25b6eee7cbc560ecaee18b97d9007122d1f4f62a096dae0296f30655fddd0", "size_in_bytes": 5379}, {"_path": "Library/include/generated_cudaGL_meta.h", "path_type": "hardlink", "sha256": "67e4b157a27f114e8cdd3c6cffac917ac38672a6ce0931f47c640eb99b371117", "sha256_in_prefix": "67e4b157a27f114e8cdd3c6cffac917ac38672a6ce0931f47c640eb99b371117", "size_in_bytes": 3344}, {"_path": "Library/include/generated_cuda_d3d10_interop_meta.h", "path_type": "hardlink", "sha256": "76c86ea44b708215f9290a048fc4a0504e8b7c9af6b14c0deff203622548a13f", "sha256_in_prefix": "76c86ea44b708215f9290a048fc4a0504e8b7c9af6b14c0deff203622548a13f", "size_in_bytes": 3364}, {"_path": "Library/include/generated_cuda_d3d11_interop_meta.h", "path_type": "hardlink", "sha256": "9d29f4ec811d51a1625b1d7cb5643d160853e64fd77c0cbd6af988329eec1e6e", "sha256_in_prefix": "9d29f4ec811d51a1625b1d7cb5643d160853e64fd77c0cbd6af988329eec1e6e", "size_in_bytes": 1500}, {"_path": "Library/include/generated_cuda_d3d9_interop_meta.h", "path_type": "hardlink", "sha256": "928816f80222c48cabc8976f3c998288778cd55d1f465cd4d32373a7f7084c41", "sha256_in_prefix": "928816f80222c48cabc8976f3c998288778cd55d1f465cd4d32373a7f7084c41", "size_in_bytes": 4178}, {"_path": "Library/include/generated_cuda_gl_interop_meta.h", "path_type": "hardlink", "sha256": "a312356f67f7bfb4fca94967306235e4488adb5e05f2bd15edc9c1588a003d8b", "sha256_in_prefix": "a312356f67f7bfb4fca94967306235e4488adb5e05f2bd15edc9c1588a003d8b", "size_in_bytes": 2492}, {"_path": "Library/include/generated_cuda_meta.h", "path_type": "hardlink", "sha256": "cd6aed1e1ccb06f9b5fb5dca606360dbcb07376fdc06090d2076fdfb0e95fc7d", "sha256_in_prefix": "cd6aed1e1ccb06f9b5fb5dca606360dbcb07376fdc06090d2076fdfb0e95fc7d", "size_in_bytes": 98202}, {"_path": "Library/include/generated_cuda_runtime_api_meta.h", "path_type": "hardlink", "sha256": "c929dd209b578107268bab524fff07c9b746ee9487067165af7042ce0c6a9ef9", "sha256_in_prefix": "c929dd209b578107268bab524fff07c9b746ee9487067165af7042ce0c6a9ef9", "size_in_bytes": 71994}, {"_path": "Library/include/generated_cudart_removed_meta.h", "path_type": "hardlink", "sha256": "59ae12d892a86e92e22b7ccd2994f71552eeb8a9f546f8cf2710b25e524bf51c", "sha256_in_prefix": "59ae12d892a86e92e22b7ccd2994f71552eeb8a9f546f8cf2710b25e524bf51c", "size_in_bytes": 5334}, {"_path": "Library/include/generated_nvtx_meta.h", "path_type": "hardlink", "sha256": "5d657bc5eb8c75c36402ac0b0f93b5e26a54e42b440ed4a04531758d2b95285c", "sha256_in_prefix": "5d657bc5eb8c75c36402ac0b0f93b5e26a54e42b440ed4a04531758d2b95285c", "size_in_bytes": 7760}, {"_path": "Library/include/nvperf_common.h", "path_type": "hardlink", "sha256": "9631222d4c395b4ea99a5ba274c98c700da83121a091c41db0d0ba3430f5f317", "sha256_in_prefix": "9631222d4c395b4ea99a5ba274c98c700da83121a091c41db0d0ba3430f5f317", "size_in_bytes": 17648}, {"_path": "Library/include/nvperf_cuda_host.h", "path_type": "hardlink", "sha256": "2f54d647dd64ea7f96004d39e902241b43a711234a949fc541ffde0af40910b9", "sha256_in_prefix": "2f54d647dd64ea7f96004d39e902241b43a711234a949fc541ffde0af40910b9", "size_in_bytes": 8495}, {"_path": "Library/include/nvperf_host.h", "path_type": "hardlink", "sha256": "a0431cfd588bab76d36078d8f497cacee53f6d94e53c3b11677f1a0c35ed6f6d", "sha256_in_prefix": "a0431cfd588bab76d36078d8f497cacee53f6d94e53c3b11677f1a0c35ed6f6d", "size_in_bytes": 70084}, {"_path": "Library/include/nvperf_target.h", "path_type": "hardlink", "sha256": "c40f907f98356e9bd31e87a68532419b2608d2514465503bb10e4f2829d7c94d", "sha256_in_prefix": "c40f907f98356e9bd31e87a68532419b2608d2514465503bb10e4f2829d7c94d", "size_in_bytes": 23136}, {"_path": "Library/lib/checkpoint.lib", "path_type": "hardlink", "sha256": "ff97ea93e8194163c0d5e891c42ee939f2cddfaa294ddb7ef394987ebffec5f1", "sha256_in_prefix": "ff97ea93e8194163c0d5e891c42ee939f2cddfaa294ddb7ef394987ebffec5f1", "size_in_bytes": 2256}, {"_path": "Library/lib/cupti.lib", "path_type": "hardlink", "sha256": "c6ecbb1494ba6377db7656eee2751f55347f2b31c67306bc11de7ae14e846dd0", "sha256_in_prefix": "c6ecbb1494ba6377db7656eee2751f55347f2b31c67306bc11de7ae14e846dd0", "size_in_bytes": 40082}, {"_path": "Library/lib/nvperf_host.lib", "path_type": "hardlink", "sha256": "10035a4b977a3aab5665047b5d4daa97c72f744b59407c9844ef16221e333c2f", "sha256_in_prefix": "10035a4b977a3aab5665047b5d4daa97c72f744b59407c9844ef16221e333c2f", "size_in_bytes": 126970}, {"_path": "Library/lib/nvperf_target.lib", "path_type": "hardlink", "sha256": "3e84b292194a364040bd3d21d081dd28e4f83e8eb9b9f7066487454507aff7fd", "sha256_in_prefix": "3e84b292194a364040bd3d21d081dd28e4f83e8eb9b9f7066487454507aff7fd", "size_in_bytes": 96522}, {"_path": "Library/lib/pcsamplingutil.lib", "path_type": "hardlink", "sha256": "8a74657f0cbe290afae750ba25742ea6d3b0d3e297965f2f08d62856898fa0e7", "sha256_in_prefix": "8a74657f0cbe290afae750ba25742ea6d3b0d3e297965f2f08d62856898fa0e7", "size_in_bytes": 2914}], "paths_version": 1}, "requested_spec": "None", "sha256": "39e47cc6362dc43cf003a251805644281e15ba79c7cc12ae1a3ca40072a8b732", "size": 163558, "subdir": "win-64", "timestamp": 1714771971000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-cupti-dev-12.4.127-hd77b12b_1.conda", "version": "12.4.127"}