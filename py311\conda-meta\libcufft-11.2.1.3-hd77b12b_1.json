{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libcufft-11.2.1.3-hd77b12b_1", "files": ["Library/bin/cufft64_11.dll", "Library/bin/cufftw64_11.dll"], "fn": "libcufft-11.2.1.3-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libcufft-11.2.1.3-hd77b12b_1", "type": 1}, "md5": "ec30ddb1c1cf15a0a2794048dd5ffbb4", "name": "libcu<PERSON>t", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libcufft-11.2.1.3-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/bin/cufft64_11.dll", "path_type": "hardlink", "sha256": "67f6fa9488c3b2bf15a126ddd3a823c5540764095c80aca4f2b165228c721f5e", "sha256_in_prefix": "67f6fa9488c3b2bf15a126ddd3a823c5540764095c80aca4f2b165228c721f5e", "size_in_bytes": 291784192}, {"_path": "Library/bin/cufftw64_11.dll", "path_type": "hardlink", "sha256": "38c1818bb05645e713044bee9fddf912b0c140a5fecd0d7eec5932fddccfa99d", "sha256_in_prefix": "38c1818bb05645e713044bee9fddf912b0c140a5fecd0d7eec5932fddccfa99d", "size_in_bytes": 163328}], "paths_version": 1}, "requested_spec": "None", "sha256": "906a47efac1d99f01fe8782c2421b83c889040a7cd9c4b35696cc111b4e55cff", "size": 181999607, "subdir": "win-64", "timestamp": 1714677697000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libcufft-11.2.1.3-hd77b12b_1.conda", "version": "11.2.1.3"}