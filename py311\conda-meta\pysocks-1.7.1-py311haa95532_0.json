{"build": "py311haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["win_inet_pton", "python >=3.11,<3.12.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\pysocks-1.7.1-py311haa95532_0", "files": ["Lib/site-packages/PySocks-1.7.1.dist-info/INSTALLER", "Lib/site-packages/PySocks-1.7.1.dist-info/LICENSE", "Lib/site-packages/PySocks-1.7.1.dist-info/METADATA", "Lib/site-packages/PySocks-1.7.1.dist-info/RECORD", "Lib/site-packages/PySocks-1.7.1.dist-info/REQUESTED", "Lib/site-packages/PySocks-1.7.1.dist-info/WHEEL", "Lib/site-packages/PySocks-1.7.1.dist-info/direct_url.json", "Lib/site-packages/PySocks-1.7.1.dist-info/top_level.txt", "Lib/site-packages/__pycache__/socks.cpython-311.pyc", "Lib/site-packages/__pycache__/sockshandler.cpython-311.pyc", "Lib/site-packages/socks.py", "Lib/site-packages/sockshandler.py"], "fn": "pysocks-1.7.1-py311haa95532_0.conda", "license": "BSD 3-<PERSON><PERSON>", "link": {"source": "D:\\anaconda3\\pkgs\\pysocks-1.7.1-py311haa95532_0", "type": 1}, "md5": "83206dfd8eb48ddd4acb273a89a26bb6", "name": "pysocks", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\pysocks-1.7.1-py311haa95532_0.conda", "paths_data": {"paths": [{"_path": "Lib/site-packages/PySocks-1.7.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/PySocks-1.7.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "7027e214e014eb78b7adcc1ceda5aca713a79fc4f6a0c52c9da5b3e707e6ffe9", "sha256_in_prefix": "7027e214e014eb78b7adcc1ceda5aca713a79fc4f6a0c52c9da5b3e707e6ffe9", "size_in_bytes": 1401}, {"_path": "Lib/site-packages/PySocks-1.7.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "4538555a792bc26e1faf5213c069afaaa0d7018c47646fd6228c917504c19388", "sha256_in_prefix": "4538555a792bc26e1faf5213c069afaaa0d7018c47646fd6228c917504c19388", "size_in_bytes": 13237}, {"_path": "Lib/site-packages/PySocks-1.7.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "6f53f3a1c0c4918ab5190112d36b0ebaf01ef3356dcd4f922030c8a4b2b8cc8f", "sha256_in_prefix": "6f53f3a1c0c4918ab5190112d36b0ebaf01ef3356dcd4f922030c8a4b2b8cc8f", "size_in_bytes": 884}, {"_path": "Lib/site-packages/PySocks-1.7.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/PySocks-1.7.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1b5e87e00dc87a84269cead8578b9e6462928e18a95f1f3373c9eef451a5bcc0", "sha256_in_prefix": "1b5e87e00dc87a84269cead8578b9e6462928e18a95f1f3373c9eef451a5bcc0", "size_in_bytes": 92}, {"_path": "Lib/site-packages/PySocks-1.7.1.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "0a31ba670c63c51093dd16fab5d40ee1b0b87098a4263794fe08bb3da88610bd", "sha256_in_prefix": "0a31ba670c63c51093dd16fab5d40ee1b0b87098a4263794fe08bb3da88610bd", "size_in_bytes": 71}, {"_path": "Lib/site-packages/PySocks-1.7.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "4ca48e21f0850682bd118f050586d8a82dcf59ddfef86d79a61f67f3e4073c39", "sha256_in_prefix": "4ca48e21f0850682bd118f050586d8a82dcf59ddfef86d79a61f67f3e4073c39", "size_in_bytes": 19}, {"_path": "Lib/site-packages/__pycache__/socks.cpython-311.pyc", "path_type": "hardlink", "sha256": "bb17b153f0711f20e8e74d0f91d9e33c63911767225bc27609d8eaab899ca446", "sha256_in_prefix": "bb17b153f0711f20e8e74d0f91d9e33c63911767225bc27609d8eaab899ca446", "size_in_bytes": 38472}, {"_path": "Lib/site-packages/__pycache__/sockshandler.cpython-311.pyc", "path_type": "hardlink", "sha256": "f391b854d804309589db0278bcd78bcaf12083d1eaaa82ff78845c3c00e85b68", "sha256_in_prefix": "f391b854d804309589db0278bcd78bcaf12083d1eaaa82ff78845c3c00e85b68", "size_in_bytes": 7239}, {"_path": "Lib/site-packages/socks.py", "path_type": "hardlink", "sha256": "c4e627dbbb7d206adb4c1cd6b1452e3dad1806ea6582253293390e0795798856", "sha256_in_prefix": "c4e627dbbb7d206adb4c1cd6b1452e3dad1806ea6582253293390e0795798856", "size_in_bytes": 31086}, {"_path": "Lib/site-packages/sockshandler.py", "path_type": "hardlink", "sha256": "d926068fea70b7592380ba19026c9e6845c27990d64667d54bf406ea412b1ad6", "sha256_in_prefix": "d926068fea70b7592380ba19026c9e6845c27990d64667d54bf406ea412b1ad6", "size_in_bytes": 3966}], "paths_version": 1}, "requested_spec": "None", "sha256": "e69aeaf2ac152ab5d3dbaf9897760552457a87d4648334458740ea7dfdaab115", "size": 37059, "subdir": "win-64", "timestamp": 1676426019000, "url": "https://repo.anaconda.com/pkgs/main/win-64/pysocks-1.7.1-py311haa95532_0.conda", "version": "1.7.1"}