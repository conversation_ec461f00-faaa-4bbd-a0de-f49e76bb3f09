#!/usr/bin/env python3
"""
测试脚本：验证BEN2背景移除的MCP功能。
"""

import requests
import json
import time

def test_mcp_server(base_url="http://localhost:7860"):
    """测试MCP服务器是否正在运行并可访问。"""

    print("正在测试BEN2 MCP服务器...")

    # 测试1：检查主Gradio应用是否运行
    try:
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            print("✅ Gradio应用正在运行")
        else:
            print(f"❌ Gradio应用返回状态码：{response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到Gradio应用：{e}")
        return False
    
    # 测试2：检查MCP架构端点是否可访问
    schema_url = f"{base_url}/gradio_api/mcp/schema"
    try:
        response = requests.get(schema_url, timeout=10)
        if response.status_code == 200:
            print("✅ MCP架构端点可访问")
            schema = response.json()

            # 检查工具是否正确定义
            if "tools" in schema:
                tools = schema["tools"]
                print(f"✅ 找到 {len(tools)} 个MCP工具：")
                for tool in tools:
                    print(f"   - {tool.get('name', '未知')}: {tool.get('description', '无描述')}")
            else:
                print("❌ MCP架构中未找到工具")
                return False

        else:
            print(f"❌ MCP架构端点返回状态码：{response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法访问MCP架构端点：{e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ MCP架构端点返回无效JSON响应：{e}")
        return False
    
    # 测试3：检查SSE端点是否可访问
    sse_url = f"{base_url}/gradio_api/mcp/sse"
    try:
        response = requests.get(sse_url, timeout=5, stream=True)
        if response.status_code == 200:
            print("✅ MCP SSE端点可访问")
        else:
            print(f"❌ MCP SSE端点返回状态码：{response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法访问MCP SSE端点：{e}")
        return False

    print("\n🎉 所有MCP测试通过！服务器已准备好供MCP客户端使用。")
    print(f"\nMCP服务器详情：")
    print(f"  - 主应用：{base_url}")
    print(f"  - MCP架构：{schema_url}")
    print(f"  - MCP SSE端点：{sse_url}")
    print(f"\n要与MCP客户端一起使用，请配置它们连接到：")
    print(f"  {sse_url}")

    return True

if __name__ == "__main__":
    import sys
    
    base_url = "http://localhost:7860"
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    
    print(f"正在测试MCP服务器：{base_url}")
    print("确保BEN2应用正在运行：python app.py")
    print("-" * 50)

    success = test_mcp_server(base_url)

    if not success:
        print("\n❌ MCP服务器测试失败！")
        print("请确保：")
        print("1. 应用正在运行：python app.py")
        print("2. 服务器已完全启动")
        print("3. 防火墙没有阻止连接")
        sys.exit(1)
    else:
        print("\n✅ MCP服务器工作正常！")
        sys.exit(0)
