#!/usr/bin/env python3
"""
Test script to verify MCP functionality for BEN2 background removal.
"""

import requests
import json
import time

def test_mcp_server(base_url="http://localhost:7860"):
    """Test if the MCP server is running and accessible."""
    
    print("Testing BEN2 MCP Server...")
    
    # Test 1: Check if the main Gradio app is running
    try:
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            print("✅ Gradio app is running")
        else:
            print(f"❌ Gradio app returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to Gradio app: {e}")
        return False
    
    # Test 2: Check if MCP schema endpoint is accessible
    schema_url = f"{base_url}/gradio_api/mcp/schema"
    try:
        response = requests.get(schema_url, timeout=10)
        if response.status_code == 200:
            print("✅ MCP schema endpoint is accessible")
            schema = response.json()
            
            # Check if tools are properly defined
            if "tools" in schema:
                tools = schema["tools"]
                print(f"✅ Found {len(tools)} MCP tools:")
                for tool in tools:
                    print(f"   - {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')}")
            else:
                print("❌ No tools found in MCP schema")
                return False
                
        else:
            print(f"❌ MCP schema endpoint returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot access MCP schema endpoint: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON response from MCP schema endpoint: {e}")
        return False
    
    # Test 3: Check if SSE endpoint is accessible
    sse_url = f"{base_url}/gradio_api/mcp/sse"
    try:
        response = requests.get(sse_url, timeout=5, stream=True)
        if response.status_code == 200:
            print("✅ MCP SSE endpoint is accessible")
        else:
            print(f"❌ MCP SSE endpoint returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot access MCP SSE endpoint: {e}")
        return False
    
    print("\n🎉 All MCP tests passed! The server is ready for MCP clients.")
    print(f"\nMCP Server Details:")
    print(f"  - Main App: {base_url}")
    print(f"  - MCP Schema: {schema_url}")
    print(f"  - MCP SSE Endpoint: {sse_url}")
    print(f"\nTo use with MCP clients, configure them to connect to:")
    print(f"  {sse_url}")
    
    return True

if __name__ == "__main__":
    import sys
    
    base_url = "http://localhost:7860"
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    
    print(f"Testing MCP server at: {base_url}")
    print("Make sure the BEN2 app is running with: python app.py")
    print("-" * 50)
    
    success = test_mcp_server(base_url)
    
    if not success:
        print("\n❌ MCP server test failed!")
        print("Make sure:")
        print("1. The app is running: python app.py")
        print("2. The server has started completely")
        print("3. No firewall is blocking the connection")
        sys.exit(1)
    else:
        print("\n✅ MCP server is working correctly!")
        sys.exit(0)
