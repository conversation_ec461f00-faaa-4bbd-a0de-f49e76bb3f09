{"build": "haa95532_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["cuda-cudart 12.4.127.*", "cuda-nvrtc 12.4.127.*", "cuda-opencl 12.4.127.*", "libcublas 12.4.5.8.*", "libcufft 11.2.1.3.*", "libcurand 10.3.5.147.*", "libcusolver 11.6.1.9.*", "libcusparse 12.3.1.170.*", "libnpp 12.2.5.30.*", "libnvfatbin 12.4.127.*", "libnvjitlink 12.4.127.*", "libnvjpeg 12.3.1.117.*"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-libraries-12.4.1-haa95532_1", "files": [], "fn": "cuda-libraries-12.4.1-haa95532_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-libraries-12.4.1-haa95532_1", "type": 1}, "md5": "f2f79ffcc6bc121591084b95b4bf46a8", "name": "cuda-libraries", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-libraries-12.4.1-haa95532_1.conda", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "85f591c6767c94d53f2df3f305b81d37e75b782f44a2d49b753590f52e8bbbc1", "size": 19851, "subdir": "win-64", "timestamp": 1716218696000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-libraries-12.4.1-haa95532_1.conda", "version": "12.4.1"}