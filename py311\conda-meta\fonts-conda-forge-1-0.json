{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["font-ttf-dejavu-sans-mono", "font-ttf-inconsolata", "font-ttf-source-code-pro", "font-ttf-ubuntu"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\fonts-conda-forge-1-0", "files": [], "fn": "fonts-conda-forge-1-0.tar.bz2", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "D:\\anaconda3\\pkgs\\fonts-conda-forge-1-0", "type": 1}, "md5": "f766549260d6815b0c52253f1fb1bb29", "name": "fonts-conda-forge", "noarch": "generic", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\fonts-conda-forge-1-0.tar.bz2", "package_type": "noarch_generic", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "53f23a3319466053818540bcdf2091f253cbdbab1e0e9ae7b9e509dcaa2a5e38", "size": 4102, "subdir": "noarch", "timestamp": 1566932280000, "url": "https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2", "version": "1"}