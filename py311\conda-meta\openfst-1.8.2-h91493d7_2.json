{"build": "h91493d7_2", "build_number": 2, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": [], "depends": ["dlfcn-win32 >=1.3.0,<2.0a0", "ucrt", "vc >=14.2,<15", "vs2015_runtime >=14.29.30037"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\openfst-1.8.2-h91493d7_2", "files": ["Library/bin/arc_lookahead-fst.dll", "Library/bin/compact16_acceptor-fst.dll", "Library/bin/compact16_string-fst.dll", "Library/bin/compact16_unweighted-fst.dll", "Library/bin/compact16_unweighted_acceptor-fst.dll", "Library/bin/compact16_weighted_string-fst.dll", "Library/bin/compact64_acceptor-fst.dll", "Library/bin/compact64_string-fst.dll", "Library/bin/compact64_unweighted-fst.dll", "Library/bin/compact64_unweighted_acceptor-fst.dll", "Library/bin/compact64_weighted_string-fst.dll", "Library/bin/compact8_acceptor-fst.dll", "Library/bin/compact8_string-fst.dll", "Library/bin/compact8_unweighted-fst.dll", "Library/bin/compact8_unweighted_acceptor-fst.dll", "Library/bin/compact8_weighted_string-fst.dll", "Library/bin/const16-fst.dll", "Library/bin/const64-fst.dll", "Library/bin/const8-fst.dll", "Library/bin/farcompilestrings.exe", "Library/bin/farconvert.exe", "Library/bin/farcreate.exe", "Library/bin/farencode.exe", "Library/bin/farequal.exe", "Library/bin/farextract.exe", "Library/bin/farinfo.exe", "Library/bin/farisomorphic.exe", "Library/bin/farprintstrings.exe", "Library/bin/fst.dll", "Library/bin/fstarcsort.exe", "Library/bin/fstclosure.exe", "Library/bin/fstcompact.dll", "Library/bin/fstcompile.exe", "Library/bin/fstcompose.exe", "Library/bin/fstcompress.exe", "Library/bin/fstcompressscript.dll", "Library/bin/fstconcat.exe", "Library/bin/fstconnect.exe", "Library/bin/fstconst.dll", "Library/bin/fstconvert.exe", "Library/bin/fstdeterminize.exe", "Library/bin/fstdifference.exe", "Library/bin/fstdisambiguate.exe", "Library/bin/fstdraw.exe", "Library/bin/fstencode.exe", "Library/bin/fstepsnormalize.exe", "Library/bin/fstequal.exe", "Library/bin/fstequivalent.exe", "Library/bin/fstfar.dll", "Library/bin/fstfarscript.dll", "Library/bin/fstinfo.exe", "Library/bin/fstintersect.exe", "Library/bin/fstinvert.exe", "Library/bin/fstisomorphic.exe", "Library/bin/fstlinear.exe", "Library/bin/fstlinearscript.dll", "Library/bin/fstloglinearapply.exe", "Library/bin/fstlookahead.dll", "Library/bin/fstmap.exe", "Library/bin/fstminimize.exe", "Library/bin/fstmpdtscript.dll", "Library/bin/fstngram.dll", "Library/bin/fstpdtscript.dll", "Library/bin/fstprint.exe", "Library/bin/fstproject.exe", "Library/bin/fstprune.exe", "Library/bin/fstpush.exe", "Library/bin/fstrandgen.exe", "Library/bin/fstrelabel.exe", "Library/bin/fstreplace.exe", "Library/bin/fstreverse.exe", "Library/bin/fstreweight.exe", "Library/bin/fstrmepsilon.exe", "Library/bin/fstscript.dll", "Library/bin/fstshortestdistance.exe", "Library/bin/fstshortestpath.exe", "Library/bin/fstspecial.dll", "Library/bin/fstspecialconvert.exe", "Library/bin/fstsymbols.exe", "Library/bin/fstsynchronize.exe", "Library/bin/fsttopsort.exe", "Library/bin/fstunion.exe", "Library/bin/ilabel_lookahead-fst.dll", "Library/bin/linear-classifier-fst.dll", "Library/bin/linear-tagger-fst.dll", "Library/bin/mpdtcompose.exe", "Library/bin/mpdtexpand.exe", "Library/bin/mpdtinfo.exe", "Library/bin/mpdtreverse.exe", "Library/bin/olabel_lookahead-fst.dll", "Library/bin/pdtcompose.exe", "Library/bin/pdtexpand.exe", "Library/bin/pdtinfo.exe", "Library/bin/pdtreplace.exe", "Library/bin/pdtreverse.exe", "Library/bin/pdtshortestpath.exe", "Library/include/fst/accumulator.h", "Library/include/fst/add-on.h", "Library/include/fst/arc-arena.h", "Library/include/fst/arc-map.h", "Library/include/fst/arc.h", "Library/include/fst/arcfilter.h", "Library/include/fst/arcsort.h", "Library/include/fst/bi-table.h", "Library/include/fst/cache.h", "Library/include/fst/closure.h", "Library/include/fst/compact-fst.h", "Library/include/fst/compat.h", "Library/include/fst/complement.h", "Library/include/fst/compose-filter.h", "Library/include/fst/compose.h", "Library/include/fst/concat.h", "Library/include/fst/connect.h", "Library/include/fst/const-fst.h", "Library/include/fst/determinize.h", "Library/include/fst/dfs-visit.h", "Library/include/fst/difference.h", "Library/include/fst/disambiguate.h", "Library/include/fst/edit-fst.h", "Library/include/fst/encode.h", "Library/include/fst/epsnormalize.h", "Library/include/fst/equal.h", "Library/include/fst/equivalent.h", "Library/include/fst/error-weight.h", "Library/include/fst/expanded-fst.h", "Library/include/fst/expander-cache.h", "Library/include/fst/expectation-weight.h", "Library/include/fst/exports/exports.h", "Library/include/fst/exports/fst_Export.h", "Library/include/fst/exports/fstcompact_Export.h", "Library/include/fst/exports/fstcompressscript_Export.h", "Library/include/fst/exports/fstconst_Export.h", "Library/include/fst/exports/fstfar_Export.h", "Library/include/fst/exports/fstfarscript_Export.h", "Library/include/fst/exports/fstlinearscript_Export.h", "Library/include/fst/exports/fstmpdtscript_Export.h", "Library/include/fst/exports/fstngram_Export.h", "Library/include/fst/exports/fstpdtscript_Export.h", "Library/include/fst/exports/fstscript_Export.h", "Library/include/fst/exports/fstspecial_Export.h", "Library/include/fst/extensions/compress/compress.h", "Library/include/fst/extensions/compress/compressscript.h", "Library/include/fst/extensions/compress/elias.h", "Library/include/fst/extensions/far/compile-strings.h", "Library/include/fst/extensions/far/convert.h", "Library/include/fst/extensions/far/create.h", "Library/include/fst/extensions/far/encode.h", "Library/include/fst/extensions/far/equal.h", "Library/include/fst/extensions/far/extract.h", "Library/include/fst/extensions/far/far-class.h", "Library/include/fst/extensions/far/far.h", "Library/include/fst/extensions/far/farlib.h", "Library/include/fst/extensions/far/farscript.h", "Library/include/fst/extensions/far/getters.h", "Library/include/fst/extensions/far/info.h", "Library/include/fst/extensions/far/isomorphic.h", "Library/include/fst/extensions/far/map-reduce.h", "Library/include/fst/extensions/far/print-strings.h", "Library/include/fst/extensions/far/script-impl.h", "Library/include/fst/extensions/far/stlist.h", "Library/include/fst/extensions/far/sttable.h", "Library/include/fst/extensions/linear/linear-fst-data-builder.h", "Library/include/fst/extensions/linear/linear-fst-data.h", "Library/include/fst/extensions/linear/linear-fst.h", "Library/include/fst/extensions/linear/linearscript.h", "Library/include/fst/extensions/linear/loglinear-apply.h", "Library/include/fst/extensions/linear/trie.h", "Library/include/fst/extensions/mpdt/compose.h", "Library/include/fst/extensions/mpdt/expand.h", "Library/include/fst/extensions/mpdt/info.h", "Library/include/fst/extensions/mpdt/mpdt.h", "Library/include/fst/extensions/mpdt/mpdtlib.h", "Library/include/fst/extensions/mpdt/mpdtscript.h", "Library/include/fst/extensions/mpdt/read_write_utils.h", "Library/include/fst/extensions/mpdt/reverse.h", "Library/include/fst/extensions/ngram/bitmap-index.h", "Library/include/fst/extensions/ngram/ngram-fst.h", "Library/include/fst/extensions/ngram/nthbit.h", "Library/include/fst/extensions/pdt/collection.h", "Library/include/fst/extensions/pdt/compose.h", "Library/include/fst/extensions/pdt/expand.h", "Library/include/fst/extensions/pdt/getters.h", "Library/include/fst/extensions/pdt/info.h", "Library/include/fst/extensions/pdt/paren.h", "Library/include/fst/extensions/pdt/pdt.h", "Library/include/fst/extensions/pdt/pdtlib.h", "Library/include/fst/extensions/pdt/pdtscript.h", "Library/include/fst/extensions/pdt/replace.h", "Library/include/fst/extensions/pdt/reverse.h", "Library/include/fst/extensions/pdt/shortest-path.h", "Library/include/fst/extensions/special/phi-fst.h", "Library/include/fst/extensions/special/rho-fst.h", "Library/include/fst/extensions/special/sigma-fst.h", "Library/include/fst/factor-weight.h", "Library/include/fst/filter-state.h", "Library/include/fst/flags.h", "Library/include/fst/float-weight.h", "Library/include/fst/fst-decl.h", "Library/include/fst/fst.h", "Library/include/fst/fstlib.h", "Library/include/fst/generic-register.h", "Library/include/fst/heap.h", "Library/include/fst/icu.h", "Library/include/fst/intersect.h", "Library/include/fst/interval-set.h", "Library/include/fst/invert.h", "Library/include/fst/isomorphic.h", "Library/include/fst/label-reachable.h", "Library/include/fst/lexicographic-weight.h", "Library/include/fst/lock.h", "Library/include/fst/log.h", "Library/include/fst/lookahead-filter.h", "Library/include/fst/lookahead-matcher.h", "Library/include/fst/mapped-file.h", "Library/include/fst/matcher-fst.h", "Library/include/fst/matcher.h", "Library/include/fst/memory.h", "Library/include/fst/minimize.h", "Library/include/fst/mutable-fst.h", "Library/include/fst/pair-weight.h", "Library/include/fst/partition.h", "Library/include/fst/power-weight-mappers.h", "Library/include/fst/power-weight.h", "Library/include/fst/product-weight.h", "Library/include/fst/project.h", "Library/include/fst/properties.h", "Library/include/fst/prune.h", "Library/include/fst/push.h", "Library/include/fst/queue.h", "Library/include/fst/randequivalent.h", "Library/include/fst/randgen.h", "Library/include/fst/rational.h", "Library/include/fst/register.h", "Library/include/fst/relabel.h", "Library/include/fst/replace-util.h", "Library/include/fst/replace.h", "Library/include/fst/reverse.h", "Library/include/fst/reweight.h", "Library/include/fst/rmepsilon.h", "Library/include/fst/rmfinalepsilon.h", "Library/include/fst/script/arc-class.h", "Library/include/fst/script/arcfilter-impl.h", "Library/include/fst/script/arciterator-class.h", "Library/include/fst/script/arcsort.h", "Library/include/fst/script/arg-packs.h", "Library/include/fst/script/closure.h", "Library/include/fst/script/compile-impl.h", "Library/include/fst/script/compile.h", "Library/include/fst/script/compose.h", "Library/include/fst/script/concat.h", "Library/include/fst/script/connect.h", "Library/include/fst/script/convert.h", "Library/include/fst/script/decode.h", "Library/include/fst/script/determinize.h", "Library/include/fst/script/difference.h", "Library/include/fst/script/disambiguate.h", "Library/include/fst/script/draw-impl.h", "Library/include/fst/script/draw.h", "Library/include/fst/script/encode.h", "Library/include/fst/script/encodemapper-class.h", "Library/include/fst/script/epsnormalize.h", "Library/include/fst/script/equal.h", "Library/include/fst/script/equivalent.h", "Library/include/fst/script/fst-class.h", "Library/include/fst/script/fstscript-decl.h", "Library/include/fst/script/fstscript.h", "Library/include/fst/script/getters.h", "Library/include/fst/script/info-impl.h", "Library/include/fst/script/info.h", "Library/include/fst/script/intersect.h", "Library/include/fst/script/invert.h", "Library/include/fst/script/isomorphic.h", "Library/include/fst/script/map.h", "Library/include/fst/script/minimize.h", "Library/include/fst/script/print-impl.h", "Library/include/fst/script/print.h", "Library/include/fst/script/project.h", "Library/include/fst/script/prune.h", "Library/include/fst/script/push.h", "Library/include/fst/script/randequivalent.h", "Library/include/fst/script/randgen.h", "Library/include/fst/script/relabel.h", "Library/include/fst/script/replace.h", "Library/include/fst/script/reverse.h", "Library/include/fst/script/reweight.h", "Library/include/fst/script/rmepsilon.h", "Library/include/fst/script/script-impl.h", "Library/include/fst/script/shortest-distance.h", "Library/include/fst/script/shortest-path.h", "Library/include/fst/script/stateiterator-class.h", "Library/include/fst/script/synchronize.h", "Library/include/fst/script/text-io.h", "Library/include/fst/script/topsort.h", "Library/include/fst/script/union.h", "Library/include/fst/script/verify.h", "Library/include/fst/script/weight-class.h", "Library/include/fst/set-weight.h", "Library/include/fst/shortest-distance.h", "Library/include/fst/shortest-path.h", "Library/include/fst/signed-log-weight.h", "Library/include/fst/sparse-power-weight.h", "Library/include/fst/sparse-tuple-weight.h", "Library/include/fst/state-map.h", "Library/include/fst/state-reachable.h", "Library/include/fst/state-table.h", "Library/include/fst/statesort.h", "Library/include/fst/string-weight.h", "Library/include/fst/string.h", "Library/include/fst/symbol-table-ops.h", "Library/include/fst/symbol-table.h", "Library/include/fst/synchronize.h", "Library/include/fst/test-properties.h", "Library/include/fst/test/algo_test.h", "Library/include/fst/test/compactors.h", "Library/include/fst/test/fst_test.h", "Library/include/fst/test/rand-fst.h", "Library/include/fst/test/weight-tester.h", "Library/include/fst/topsort.h", "Library/include/fst/tuple-weight.h", "Library/include/fst/union-find.h", "Library/include/fst/union-weight.h", "Library/include/fst/union.h", "Library/include/fst/util.h", "Library/include/fst/vector-fst.h", "Library/include/fst/verify.h", "Library/include/fst/visit.h", "Library/include/fst/weight.h", "Library/include/fst/windows_defs.inc", "Library/lib/fst.lib", "Library/lib/fstcompressscript.lib", "Library/lib/fstfar.lib", "Library/lib/fstfarscript.lib", "Library/lib/fstlinearscript.lib", "Library/lib/fstmpdtscript.lib", "Library/lib/fstngram.lib", "Library/lib/fstpdtscript.lib", "Library/lib/fstscript.lib", "Library/lib/fstspecial.lib"], "fn": "openfst-1.8.2-h91493d7_2.tar.bz2", "license": "Apache-2.0", "link": {"source": "D:\\anaconda3\\pkgs\\openfst-1.8.2-h91493d7_2", "type": 1}, "md5": "e341983755c791cf992fd5d5443c6789", "name": "openfst", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\openfst-1.8.2-h91493d7_2.tar.bz2", "paths_data": {"paths": [{"_path": "Library/bin/arc_lookahead-fst.dll", "path_type": "hardlink", "sha256": "e56e826f4f2f6993a00e1dafa840055bc14eb9387f5333c896257f517a234a28", "sha256_in_prefix": "e56e826f4f2f6993a00e1dafa840055bc14eb9387f5333c896257f517a234a28", "size_in_bytes": 181760}, {"_path": "Library/bin/compact16_acceptor-fst.dll", "path_type": "hardlink", "sha256": "bdf2534938b05a5f61d4df300acc9313955dc3514a746bce1ad20ccca85b09b4", "sha256_in_prefix": "bdf2534938b05a5f61d4df300acc9313955dc3514a746bce1ad20ccca85b09b4", "size_in_bytes": 221184}, {"_path": "Library/bin/compact16_string-fst.dll", "path_type": "hardlink", "sha256": "4126e1a28c4012302e8f225648f7d880b1549e471d8569aa9b9d63053646a5ac", "sha256_in_prefix": "4126e1a28c4012302e8f225648f7d880b1549e471d8569aa9b9d63053646a5ac", "size_in_bytes": 214528}, {"_path": "Library/bin/compact16_unweighted-fst.dll", "path_type": "hardlink", "sha256": "2acc99192429e525126546b57934eeaa66ff7e123ef857373eb7c850886abb64", "sha256_in_prefix": "2acc99192429e525126546b57934eeaa66ff7e123ef857373eb7c850886abb64", "size_in_bytes": 216576}, {"_path": "Library/bin/compact16_unweighted_acceptor-fst.dll", "path_type": "hardlink", "sha256": "6f73e6c1f5973f1d6b77370d400de5d3e0e3bf488144960df462ddc1cf469160", "sha256_in_prefix": "6f73e6c1f5973f1d6b77370d400de5d3e0e3bf488144960df462ddc1cf469160", "size_in_bytes": 215552}, {"_path": "Library/bin/compact16_weighted_string-fst.dll", "path_type": "hardlink", "sha256": "dda46f56054c13d1a82ae7c7606a13d25c368faf390eb57791b24bcfc7e6c3d2", "sha256_in_prefix": "dda46f56054c13d1a82ae7c7606a13d25c368faf390eb57791b24bcfc7e6c3d2", "size_in_bytes": 220672}, {"_path": "Library/bin/compact64_acceptor-fst.dll", "path_type": "hardlink", "sha256": "cc9574c330c7d5e456fa5aab37127b1e1a3d89a1b00291fafc5ccb6c5fb9c310", "sha256_in_prefix": "cc9574c330c7d5e456fa5aab37127b1e1a3d89a1b00291fafc5ccb6c5fb9c310", "size_in_bytes": 222208}, {"_path": "Library/bin/compact64_string-fst.dll", "path_type": "hardlink", "sha256": "55535ec5a650db4783622cb8208da18e642441f4c2741e8f842e2cbd96a4b2e5", "sha256_in_prefix": "55535ec5a650db4783622cb8208da18e642441f4c2741e8f842e2cbd96a4b2e5", "size_in_bytes": 214528}, {"_path": "Library/bin/compact64_unweighted-fst.dll", "path_type": "hardlink", "sha256": "1b55c74952ec00588830ef4e7b093038cf36e880a8b96a8e3c7727bf8329879d", "sha256_in_prefix": "1b55c74952ec00588830ef4e7b093038cf36e880a8b96a8e3c7727bf8329879d", "size_in_bytes": 217600}, {"_path": "Library/bin/compact64_unweighted_acceptor-fst.dll", "path_type": "hardlink", "sha256": "2c518102be3af2ef6ba7a27d924fc7cd01413449775de758932a7a4ad892f950", "sha256_in_prefix": "2c518102be3af2ef6ba7a27d924fc7cd01413449775de758932a7a4ad892f950", "size_in_bytes": 215552}, {"_path": "Library/bin/compact64_weighted_string-fst.dll", "path_type": "hardlink", "sha256": "3bbde8d30e2f56c2f9874456501082827dbbc8ded95ee60db53a2781a8c99554", "sha256_in_prefix": "3bbde8d30e2f56c2f9874456501082827dbbc8ded95ee60db53a2781a8c99554", "size_in_bytes": 220672}, {"_path": "Library/bin/compact8_acceptor-fst.dll", "path_type": "hardlink", "sha256": "b6b9d164ffa0d456e82ab73449568def5e3741a64c168e7b9796c1feabb9728a", "sha256_in_prefix": "b6b9d164ffa0d456e82ab73449568def5e3741a64c168e7b9796c1feabb9728a", "size_in_bytes": 221184}, {"_path": "Library/bin/compact8_string-fst.dll", "path_type": "hardlink", "sha256": "82adb352a4ff96ae85449f5b2028b211c24eb95572d3aafea94ea0ee52533251", "sha256_in_prefix": "82adb352a4ff96ae85449f5b2028b211c24eb95572d3aafea94ea0ee52533251", "size_in_bytes": 214016}, {"_path": "Library/bin/compact8_unweighted-fst.dll", "path_type": "hardlink", "sha256": "e87be64becf6f45e238e7e05054f92fadf82bc253b121fe4b8c04001da8e1e63", "sha256_in_prefix": "e87be64becf6f45e238e7e05054f92fadf82bc253b121fe4b8c04001da8e1e63", "size_in_bytes": 216576}, {"_path": "Library/bin/compact8_unweighted_acceptor-fst.dll", "path_type": "hardlink", "sha256": "500018dddd0e4ab7b4afba18026eab197bb2d8276ea137d28d8192e910f8f322", "sha256_in_prefix": "500018dddd0e4ab7b4afba18026eab197bb2d8276ea137d28d8192e910f8f322", "size_in_bytes": 215552}, {"_path": "Library/bin/compact8_weighted_string-fst.dll", "path_type": "hardlink", "sha256": "abb5060c5b087d73d4385646e0586ff271dae22824ac97ee8ad89fd8ceb454a3", "sha256_in_prefix": "abb5060c5b087d73d4385646e0586ff271dae22824ac97ee8ad89fd8ceb454a3", "size_in_bytes": 220672}, {"_path": "Library/bin/const16-fst.dll", "path_type": "hardlink", "sha256": "ba80a437dd6736d4a3a3b2dbbd93e1a5433fde83ea5e08b104521105a75c5e3e", "sha256_in_prefix": "ba80a437dd6736d4a3a3b2dbbd93e1a5433fde83ea5e08b104521105a75c5e3e", "size_in_bytes": 128000}, {"_path": "Library/bin/const64-fst.dll", "path_type": "hardlink", "sha256": "95c44feb83276125de17808fe8403d9025d76b2b90204c5fa14e6b46f6956881", "sha256_in_prefix": "95c44feb83276125de17808fe8403d9025d76b2b90204c5fa14e6b46f6956881", "size_in_bytes": 127488}, {"_path": "Library/bin/const8-fst.dll", "path_type": "hardlink", "sha256": "5d6fb42d8f6ee0348d7c2687ee750aebe95bcf76b4c3d3fc6952a3e0bda802c0", "sha256_in_prefix": "5d6fb42d8f6ee0348d7c2687ee750aebe95bcf76b4c3d3fc6952a3e0bda802c0", "size_in_bytes": 128000}, {"_path": "Library/bin/farcompilestrings.exe", "path_type": "hardlink", "sha256": "9489008237ca2ce3de7beef7e0504a81a9eb26fc983e527e9f2e86314de2e75b", "sha256_in_prefix": "9489008237ca2ce3de7beef7e0504a81a9eb26fc983e527e9f2e86314de2e75b", "size_in_bytes": 56320}, {"_path": "Library/bin/farconvert.exe", "path_type": "hardlink", "sha256": "26c28e9a7ab257c1e29360c09c7883756a2d7a31f072c1395aefc587e5fa0b2f", "sha256_in_prefix": "26c28e9a7ab257c1e29360c09c7883756a2d7a31f072c1395aefc587e5fa0b2f", "size_in_bytes": 28672}, {"_path": "Library/bin/farcreate.exe", "path_type": "hardlink", "sha256": "c6c69b967ea8d85438a5b9819a0d422827af5f32d0b3530dc788d6bf0de52976", "sha256_in_prefix": "c6c69b967ea8d85438a5b9819a0d422827af5f32d0b3530dc788d6bf0de52976", "size_in_bytes": 51200}, {"_path": "Library/bin/farencode.exe", "path_type": "hardlink", "sha256": "1cd72e56c80b224f66d21bdbb687b79db95fe11d241b24fb70771c60194c5926", "sha256_in_prefix": "1cd72e56c80b224f66d21bdbb687b79db95fe11d241b24fb70771c60194c5926", "size_in_bytes": 33280}, {"_path": "Library/bin/farequal.exe", "path_type": "hardlink", "sha256": "af151fb306f8ae3728e3dc889cd76ecd0d432107ec7f6b53fc32762895e119a6", "sha256_in_prefix": "af151fb306f8ae3728e3dc889cd76ecd0d432107ec7f6b53fc32762895e119a6", "size_in_bytes": 30208}, {"_path": "Library/bin/farextract.exe", "path_type": "hardlink", "sha256": "8c643d3e7c5e7b64702775c3997ad65459e8c78abb1ea0e13d810d0953425cb6", "sha256_in_prefix": "8c643d3e7c5e7b64702775c3997ad65459e8c78abb1ea0e13d810d0953425cb6", "size_in_bytes": 33792}, {"_path": "Library/bin/farinfo.exe", "path_type": "hardlink", "sha256": "bbbc9d6c9e6acb5ff1a827a0103632ff2b90de64dd1e817168380fe179214466", "sha256_in_prefix": "bbbc9d6c9e6acb5ff1a827a0103632ff2b90de64dd1e817168380fe179214466", "size_in_bytes": 29184}, {"_path": "Library/bin/farisomorphic.exe", "path_type": "hardlink", "sha256": "ba44a1c7d3321b94b04ed7081537bd4e9ca126fedfabc65d9c699e2ee95a0a21", "sha256_in_prefix": "ba44a1c7d3321b94b04ed7081537bd4e9ca126fedfabc65d9c699e2ee95a0a21", "size_in_bytes": 31232}, {"_path": "Library/bin/farprintstrings.exe", "path_type": "hardlink", "sha256": "5bb6abae51c6b6b3b9a353b4fe87290e1ea5fd9017ab07504e15f8f896899f78", "sha256_in_prefix": "5bb6abae51c6b6b3b9a353b4fe87290e1ea5fd9017ab07504e15f8f896899f78", "size_in_bytes": 38912}, {"_path": "Library/bin/fst.dll", "path_type": "hardlink", "sha256": "a2c51c02f9ae8cd563d39ecf5dd78ff480d0ef38ecb5c94361621afc886f2b55", "sha256_in_prefix": "a2c51c02f9ae8cd563d39ecf5dd78ff480d0ef38ecb5c94361621afc886f2b55", "size_in_bytes": 732160}, {"_path": "Library/bin/fstarcsort.exe", "path_type": "hardlink", "sha256": "a78ccb15c50794313f4448626ec13a7cbbe4ca7d6e6509b055848c44384bdc35", "sha256_in_prefix": "a78ccb15c50794313f4448626ec13a7cbbe4ca7d6e6509b055848c44384bdc35", "size_in_bytes": 25600}, {"_path": "Library/bin/fstclosure.exe", "path_type": "hardlink", "sha256": "14cc753a34096bc7652075fdadbfc2bbae4e73f6fa8851f6fe16ed63ad5a2940", "sha256_in_prefix": "14cc753a34096bc7652075fdadbfc2bbae4e73f6fa8851f6fe16ed63ad5a2940", "size_in_bytes": 26112}, {"_path": "Library/bin/fstcompact.dll", "path_type": "hardlink", "sha256": "4598970c46e217aa0389d9b28e0b9a4e2750c230ca38f14e148319bb6fab1acb", "sha256_in_prefix": "4598970c46e217aa0389d9b28e0b9a4e2750c230ca38f14e148319bb6fab1acb", "size_in_bytes": 975360}, {"_path": "Library/bin/fstcompile.exe", "path_type": "hardlink", "sha256": "112c0db481004abce4e18e220b1c4b011d9e83d2885656eaadf2e50c624c190c", "sha256_in_prefix": "112c0db481004abce4e18e220b1c4b011d9e83d2885656eaadf2e50c624c190c", "size_in_bytes": 45568}, {"_path": "Library/bin/fstcompose.exe", "path_type": "hardlink", "sha256": "144ffe7a452dd89adfeb38e724fd01c454ee9cbe0d0c2f15a490ded814446957", "sha256_in_prefix": "144ffe7a452dd89adfeb38e724fd01c454ee9cbe0d0c2f15a490ded814446957", "size_in_bytes": 30208}, {"_path": "Library/bin/fstcompress.exe", "path_type": "hardlink", "sha256": "16c6d60c74d4125b4f44e5f58254fb1b0d5f5de99ae87ed5935ea37470c394b1", "sha256_in_prefix": "16c6d60c74d4125b4f44e5f58254fb1b0d5f5de99ae87ed5935ea37470c394b1", "size_in_bytes": 29696}, {"_path": "Library/bin/fstcompressscript.dll", "path_type": "hardlink", "sha256": "f69251f1cc39d40c46ba141a7f7b3d0d736add191a7a10517983c1794ab6f0cf", "sha256_in_prefix": "f69251f1cc39d40c46ba141a7f7b3d0d736add191a7a10517983c1794ab6f0cf", "size_in_bytes": 257536}, {"_path": "Library/bin/fstconcat.exe", "path_type": "hardlink", "sha256": "778cb398cef672442d566b99b08030cea07ffdcb03eff8f53426eb6803a807cc", "sha256_in_prefix": "778cb398cef672442d566b99b08030cea07ffdcb03eff8f53426eb6803a807cc", "size_in_bytes": 19456}, {"_path": "Library/bin/fstconnect.exe", "path_type": "hardlink", "sha256": "b7f123f4876b082084eb8d3b85733ff9eb830abadbda582ff169a02b3f875a9c", "sha256_in_prefix": "b7f123f4876b082084eb8d3b85733ff9eb830abadbda582ff169a02b3f875a9c", "size_in_bytes": 16384}, {"_path": "Library/bin/fstconst.dll", "path_type": "hardlink", "sha256": "54d82de9670d9f69bd2a4a575eb85e6f106c2e4f77da94e9820b9ceeaa454c1a", "sha256_in_prefix": "54d82de9670d9f69bd2a4a575eb85e6f106c2e4f77da94e9820b9ceeaa454c1a", "size_in_bytes": 183296}, {"_path": "Library/bin/fstconvert.exe", "path_type": "hardlink", "sha256": "29eab1d80ae1189889130dcab091cdc3a350e557c4dfa4bc06281163508ed87e", "sha256_in_prefix": "29eab1d80ae1189889130dcab091cdc3a350e557c4dfa4bc06281163508ed87e", "size_in_bytes": 23040}, {"_path": "Library/bin/fstdeterminize.exe", "path_type": "hardlink", "sha256": "a7daddf63b910f0549db0b813ee4063c0476f4872887b0bcb10be62fb0b9e40f", "sha256_in_prefix": "a7daddf63b910f0549db0b813ee4063c0476f4872887b0bcb10be62fb0b9e40f", "size_in_bytes": 35328}, {"_path": "Library/bin/fstdifference.exe", "path_type": "hardlink", "sha256": "f3988d715eed83fd8e65f54d6678d1f3029032126ccc236e817f4b937b4f46b9", "sha256_in_prefix": "f3988d715eed83fd8e65f54d6678d1f3029032126ccc236e817f4b937b4f46b9", "size_in_bytes": 29696}, {"_path": "Library/bin/fstdisambiguate.exe", "path_type": "hardlink", "sha256": "7dd2f0f8fb9db90dd4b04da95d4b40e2f03ab9cf340b86a683b331609823d56a", "sha256_in_prefix": "7dd2f0f8fb9db90dd4b04da95d4b40e2f03ab9cf340b86a683b331609823d56a", "size_in_bytes": 30208}, {"_path": "Library/bin/fstdraw.exe", "path_type": "hardlink", "sha256": "6186e80fa35c00d2c3090c60bcb19770840bb93541d6ce38ad424c21c3061e21", "sha256_in_prefix": "6186e80fa35c00d2c3090c60bcb19770840bb93541d6ce38ad424c21c3061e21", "size_in_bytes": 52224}, {"_path": "Library/bin/fstencode.exe", "path_type": "hardlink", "sha256": "8952872b199e6498c11d2094148522739f1464428d85536e9431ff35d8c61a5e", "sha256_in_prefix": "8952872b199e6498c11d2094148522739f1464428d85536e9431ff35d8c61a5e", "size_in_bytes": 24064}, {"_path": "Library/bin/fstepsnormalize.exe", "path_type": "hardlink", "sha256": "da0cd4d77ade3cfc0466b24d5c24e3c87d23189f788626c94ccc12fcfb8db556", "sha256_in_prefix": "da0cd4d77ade3cfc0466b24d5c24e3c87d23189f788626c94ccc12fcfb8db556", "size_in_bytes": 26112}, {"_path": "Library/bin/fstequal.exe", "path_type": "hardlink", "sha256": "49d4f2e61fa46a4b1d7b575a93f891dc0be03e5fe4e52effbc3bd8ca3bbd7a6c", "sha256_in_prefix": "49d4f2e61fa46a4b1d7b575a93f891dc0be03e5fe4e52effbc3bd8ca3bbd7a6c", "size_in_bytes": 24064}, {"_path": "Library/bin/fstequivalent.exe", "path_type": "hardlink", "sha256": "345ce1f903fc0d2c28398aa3dcfbdd20857f78e2f8a7278a3b783bed9e083037", "sha256_in_prefix": "345ce1f903fc0d2c28398aa3dcfbdd20857f78e2f8a7278a3b783bed9e083037", "size_in_bytes": 35840}, {"_path": "Library/bin/fstfar.dll", "path_type": "hardlink", "sha256": "b0263995044e3d9cd986c78efb057a1721f768cec987f85e30e1fdae6250bb15", "sha256_in_prefix": "b0263995044e3d9cd986c78efb057a1721f768cec987f85e30e1fdae6250bb15", "size_in_bytes": 25088}, {"_path": "Library/bin/fstfarscript.dll", "path_type": "hardlink", "sha256": "947391343383b23afe72d8fa43b981421582960d44da1629d487e4230beeb6ed", "sha256_in_prefix": "947391343383b23afe72d8fa43b981421582960d44da1629d487e4230beeb6ed", "size_in_bytes": 879104}, {"_path": "Library/bin/fstinfo.exe", "path_type": "hardlink", "sha256": "b25a571cb5154253adaf3f8471476bb0b1c186f6ff5994aaafc07ef621012e46", "sha256_in_prefix": "b25a571cb5154253adaf3f8471476bb0b1c186f6ff5994aaafc07ef621012e46", "size_in_bytes": 43520}, {"_path": "Library/bin/fstintersect.exe", "path_type": "hardlink", "sha256": "384c9e7bd862fc930c21d389fbaa3732e49dfa0cbc27f5d0dbd30ffa3c8b9c0e", "sha256_in_prefix": "384c9e7bd862fc930c21d389fbaa3732e49dfa0cbc27f5d0dbd30ffa3c8b9c0e", "size_in_bytes": 29696}, {"_path": "Library/bin/fstinvert.exe", "path_type": "hardlink", "sha256": "f179be6612cff8f33c5c7a984a1e797d5b97d9968ed8677873f63f59690654c9", "sha256_in_prefix": "f179be6612cff8f33c5c7a984a1e797d5b97d9968ed8677873f63f59690654c9", "size_in_bytes": 15872}, {"_path": "Library/bin/fstisomorphic.exe", "path_type": "hardlink", "sha256": "d8c38e329d56d0d41bee84f3db1be4b97415cb25ccec90328708e9dab304fe37", "sha256_in_prefix": "d8c38e329d56d0d41bee84f3db1be4b97415cb25ccec90328708e9dab304fe37", "size_in_bytes": 24064}, {"_path": "Library/bin/fstlinear.exe", "path_type": "hardlink", "sha256": "d39896157c80136dcf73abbea8ecfb68c8c65a6e9600fddffbd533dfe824fccb", "sha256_in_prefix": "d39896157c80136dcf73abbea8ecfb68c8c65a6e9600fddffbd533dfe824fccb", "size_in_bytes": 26624}, {"_path": "Library/bin/fstlinearscript.dll", "path_type": "hardlink", "sha256": "74393c8c818589717382ff93d136c27e18835b490dfa763126c9c1ebe0e4b560", "sha256_in_prefix": "74393c8c818589717382ff93d136c27e18835b490dfa763126c9c1ebe0e4b560", "size_in_bytes": 359936}, {"_path": "Library/bin/fstloglinearapply.exe", "path_type": "hardlink", "sha256": "57a3bccc16476ea049f11a84a28a156dacc5f655cc09e827ac42f3ab70c31d82", "sha256_in_prefix": "57a3bccc16476ea049f11a84a28a156dacc5f655cc09e827ac42f3ab70c31d82", "size_in_bytes": 849920}, {"_path": "Library/bin/fstlookahead.dll", "path_type": "hardlink", "sha256": "e13b4d8d5e089f6db4abb7928663a0196c857156ff8a104522364c6f62c69d52", "sha256_in_prefix": "e13b4d8d5e089f6db4abb7928663a0196c857156ff8a104522364c6f62c69d52", "size_in_bytes": 401408}, {"_path": "Library/bin/fstmap.exe", "path_type": "hardlink", "sha256": "6064abb4f006de6718e41a631e8911b19278b8e8920c0fd29cc4f583d4d71390", "sha256_in_prefix": "6064abb4f006de6718e41a631e8911b19278b8e8920c0fd29cc4f583d4d71390", "size_in_bytes": 32256}, {"_path": "Library/bin/fstminimize.exe", "path_type": "hardlink", "sha256": "935d681f5d918d5f70aa3635285f997faf9f14244db3f267f0b9c218ea54186e", "sha256_in_prefix": "935d681f5d918d5f70aa3635285f997faf9f14244db3f267f0b9c218ea54186e", "size_in_bytes": 29696}, {"_path": "Library/bin/fstmpdtscript.dll", "path_type": "hardlink", "sha256": "c7706b0b7d483b12998cacb59224d8a2df9aa47e73a3fc0ae5d866c4e176d4ad", "sha256_in_prefix": "c7706b0b7d483b12998cacb59224d8a2df9aa47e73a3fc0ae5d866c4e176d4ad", "size_in_bytes": 334336}, {"_path": "Library/bin/fstngram.dll", "path_type": "hardlink", "sha256": "8fbc2168fb09e48e135fd4a01e13e1408689cbbddb4735b643009546d724d895", "sha256_in_prefix": "8fbc2168fb09e48e135fd4a01e13e1408689cbbddb4735b643009546d724d895", "size_in_bytes": 170496}, {"_path": "Library/bin/fstpdtscript.dll", "path_type": "hardlink", "sha256": "1defc74c03e4f8ca859c5538cd400b8b73bb77930f1e446c23d293398d607683", "sha256_in_prefix": "1defc74c03e4f8ca859c5538cd400b8b73bb77930f1e446c23d293398d607683", "size_in_bytes": 585728}, {"_path": "Library/bin/fstprint.exe", "path_type": "hardlink", "sha256": "ac57b07b78ac0070755e8512fff9bf5a11db3824c5d752f7e6bdad799de2dc32", "sha256_in_prefix": "ac57b07b78ac0070755e8512fff9bf5a11db3824c5d752f7e6bdad799de2dc32", "size_in_bytes": 46592}, {"_path": "Library/bin/fstproject.exe", "path_type": "hardlink", "sha256": "d3d5e2907be3dafa5dfdd41a7221eef88547e8bbcd2b73574e69173d1533d045", "sha256_in_prefix": "d3d5e2907be3dafa5dfdd41a7221eef88547e8bbcd2b73574e69173d1533d045", "size_in_bytes": 26112}, {"_path": "Library/bin/fstprune.exe", "path_type": "hardlink", "sha256": "7778931b93550f09abcafe919617bc5866c99b3228152cf8062863bd01296fed", "sha256_in_prefix": "7778931b93550f09abcafe919617bc5866c99b3228152cf8062863bd01296fed", "size_in_bytes": 29184}, {"_path": "Library/bin/fstpush.exe", "path_type": "hardlink", "sha256": "f4abf03c0557ce3ae07df91599195895e0948216b21621b81dc240a9eb08a53d", "sha256_in_prefix": "f4abf03c0557ce3ae07df91599195895e0948216b21621b81dc240a9eb08a53d", "size_in_bytes": 32256}, {"_path": "Library/bin/fstrandgen.exe", "path_type": "hardlink", "sha256": "e8fa2e635c3e2714d72be73da24e05d9f2fe20b4fd018b1a3ca374458ab6c40a", "sha256_in_prefix": "e8fa2e635c3e2714d72be73da24e05d9f2fe20b4fd018b1a3ca374458ab6c40a", "size_in_bytes": 34816}, {"_path": "Library/bin/fstrelabel.exe", "path_type": "hardlink", "sha256": "8a0f3b5e5b26b03db42de666b51da68bed11ebcbdbb307ba7fda9e07723e1d3a", "sha256_in_prefix": "8a0f3b5e5b26b03db42de666b51da68bed11ebcbdbb307ba7fda9e07723e1d3a", "size_in_bytes": 50688}, {"_path": "Library/bin/fstreplace.exe", "path_type": "hardlink", "sha256": "047a21fd7316fedee2b47f60106c6a981fcf5832e8f3962eff6f054f29dee541", "sha256_in_prefix": "047a21fd7316fedee2b47f60106c6a981fcf5832e8f3962eff6f054f29dee541", "size_in_bytes": 36352}, {"_path": "Library/bin/fstreverse.exe", "path_type": "hardlink", "sha256": "e86f4e332a7afde4a5b1d51612cc425ee7af22793a38fa8da204d1f096ac76a1", "sha256_in_prefix": "e86f4e332a7afde4a5b1d51612cc425ee7af22793a38fa8da204d1f096ac76a1", "size_in_bytes": 21504}, {"_path": "Library/bin/fstreweight.exe", "path_type": "hardlink", "sha256": "fb7f34a7e52f0a1155de9a314bb5649c93979712cd29bb7fda8757c3391d3cf4", "sha256_in_prefix": "fb7f34a7e52f0a1155de9a314bb5649c93979712cd29bb7fda8757c3391d3cf4", "size_in_bytes": 27648}, {"_path": "Library/bin/fstrmepsilon.exe", "path_type": "hardlink", "sha256": "62fc0fe5c6cf624d2d8dddf9d2b354dd6c3ddd2de4afa13ec581f9cd59ec86a7", "sha256_in_prefix": "62fc0fe5c6cf624d2d8dddf9d2b354dd6c3ddd2de4afa13ec581f9cd59ec86a7", "size_in_bytes": 34304}, {"_path": "Library/bin/fstscript.dll", "path_type": "hardlink", "sha256": "6296251eb85c126ba433528caa5c24213c490253705b64c008099205cd62e809", "sha256_in_prefix": "6296251eb85c126ba433528caa5c24213c490253705b64c008099205cd62e809", "size_in_bytes": 4107264}, {"_path": "Library/bin/fstshortestdistance.exe", "path_type": "hardlink", "sha256": "e94c47e097be3c3c92d3a0c5b9791cc1e5663ee8609e3a845beb4daa710c563f", "sha256_in_prefix": "e94c47e097be3c3c92d3a0c5b9791cc1e5663ee8609e3a845beb4daa710c563f", "size_in_bytes": 33280}, {"_path": "Library/bin/fstshortestpath.exe", "path_type": "hardlink", "sha256": "71320dbad729b49dacd436816d448cf93ecd44305e4450d0f5e225c25d72be51", "sha256_in_prefix": "71320dbad729b49dacd436816d448cf93ecd44305e4450d0f5e225c25d72be51", "size_in_bytes": 37376}, {"_path": "Library/bin/fstspecial.dll", "path_type": "hardlink", "sha256": "e94ae9a45a214d3e65d21e05ea8e9188c4af2a1d3baf244f752b4272fbd628e4", "sha256_in_prefix": "e94ae9a45a214d3e65d21e05ea8e9188c4af2a1d3baf244f752b4272fbd628e4", "size_in_bytes": 387072}, {"_path": "Library/bin/fstspecialconvert.exe", "path_type": "hardlink", "sha256": "460fc4b04ca674cf75af448bdfb22e4b1e63fc6c75498a9b37a6aff5a9edbade", "sha256_in_prefix": "460fc4b04ca674cf75af448bdfb22e4b1e63fc6c75498a9b37a6aff5a9edbade", "size_in_bytes": 23040}, {"_path": "Library/bin/fstsymbols.exe", "path_type": "hardlink", "sha256": "f1b50e35e08ef1941cc9e9710e0c2afc0ac75d2eef637f02bd30fa9d350c4877", "sha256_in_prefix": "f1b50e35e08ef1941cc9e9710e0c2afc0ac75d2eef637f02bd30fa9d350c4877", "size_in_bytes": 51712}, {"_path": "Library/bin/fstsynchronize.exe", "path_type": "hardlink", "sha256": "236e7f1ab30466a852e4b9e05d407b5b5ccea1ce6dffd7b8891453fce3d79624", "sha256_in_prefix": "236e7f1ab30466a852e4b9e05d407b5b5ccea1ce6dffd7b8891453fce3d79624", "size_in_bytes": 16896}, {"_path": "Library/bin/fsttopsort.exe", "path_type": "hardlink", "sha256": "ba99e08d13a40f9126c5f8b36cc67e7a62b5a293b5c0b3be0b11ec2cf310af92", "sha256_in_prefix": "ba99e08d13a40f9126c5f8b36cc67e7a62b5a293b5c0b3be0b11ec2cf310af92", "size_in_bytes": 18944}, {"_path": "Library/bin/fstunion.exe", "path_type": "hardlink", "sha256": "036a0f5855677c8b0e548d1cc4a333a385aca1f272a381cc9ce25325424626f9", "sha256_in_prefix": "036a0f5855677c8b0e548d1cc4a333a385aca1f272a381cc9ce25325424626f9", "size_in_bytes": 19456}, {"_path": "Library/bin/ilabel_lookahead-fst.dll", "path_type": "hardlink", "sha256": "3e9c32445e7c7c95a37e0b48271b1607eaff9f6c40e74d941ff6a03172321961", "sha256_in_prefix": "3e9c32445e7c7c95a37e0b48271b1607eaff9f6c40e74d941ff6a03172321961", "size_in_bytes": 332800}, {"_path": "Library/bin/linear-classifier-fst.dll", "path_type": "hardlink", "sha256": "cade0a4d228d3123b5fe82de8e7047068ebe0291fae2c89c4e0c12a98a145b3b", "sha256_in_prefix": "cade0a4d228d3123b5fe82de8e7047068ebe0291fae2c89c4e0c12a98a145b3b", "size_in_bytes": 184832}, {"_path": "Library/bin/linear-tagger-fst.dll", "path_type": "hardlink", "sha256": "643e191ec1f191374799ed5677e2bc98b5d6bfbd3272e20eaac0e8d7402fd15e", "sha256_in_prefix": "643e191ec1f191374799ed5677e2bc98b5d6bfbd3272e20eaac0e8d7402fd15e", "size_in_bytes": 186368}, {"_path": "Library/bin/mpdtcompose.exe", "path_type": "hardlink", "sha256": "5e4f15f1eae8a9b86aa656b26e100458d6a6cbf1f41c1f16490fd7850f7412b2", "sha256_in_prefix": "5e4f15f1eae8a9b86aa656b26e100458d6a6cbf1f41c1f16490fd7850f7412b2", "size_in_bytes": 47104}, {"_path": "Library/bin/mpdtexpand.exe", "path_type": "hardlink", "sha256": "6c999c96d4e3508985dbc132ba7da133beb570842df115c4e1ee65fc64f61ae4", "sha256_in_prefix": "6c999c96d4e3508985dbc132ba7da133beb570842df115c4e1ee65fc64f61ae4", "size_in_bytes": 45056}, {"_path": "Library/bin/mpdtinfo.exe", "path_type": "hardlink", "sha256": "af92ef84a87c978a2c89f47d4ed306d46608c88d3a4858130a60029aff83ae8d", "sha256_in_prefix": "af92ef84a87c978a2c89f47d4ed306d46608c88d3a4858130a60029aff83ae8d", "size_in_bytes": 40960}, {"_path": "Library/bin/mpdtreverse.exe", "path_type": "hardlink", "sha256": "d15655d0c9a17c867697fdde4b9f5f15b977c7824b93907e8d0fc20189dd4bbe", "sha256_in_prefix": "d15655d0c9a17c867697fdde4b9f5f15b977c7824b93907e8d0fc20189dd4bbe", "size_in_bytes": 45568}, {"_path": "Library/bin/olabel_lookahead-fst.dll", "path_type": "hardlink", "sha256": "6cc850a787d1e1674dcaa07e319087f9217c724f5e7d4e6f5d06ccd51159eca4", "sha256_in_prefix": "6cc850a787d1e1674dcaa07e319087f9217c724f5e7d4e6f5d06ccd51159eca4", "size_in_bytes": 332800}, {"_path": "Library/bin/pdtcompose.exe", "path_type": "hardlink", "sha256": "3307e26d17be124335b08e15ad25d513470d98c311045b019dc62f192f4c106d", "sha256_in_prefix": "3307e26d17be124335b08e15ad25d513470d98c311045b019dc62f192f4c106d", "size_in_bytes": 46080}, {"_path": "Library/bin/pdtexpand.exe", "path_type": "hardlink", "sha256": "1debb3033ec074666122eb3d9536343332de3a85c4afd50e26947fb7a4eaad66", "sha256_in_prefix": "1debb3033ec074666122eb3d9536343332de3a85c4afd50e26947fb7a4eaad66", "size_in_bytes": 45568}, {"_path": "Library/bin/pdtinfo.exe", "path_type": "hardlink", "sha256": "eaf2d0414ca05c330010e990def160dbe97f389c071c11f16c063d02a55d4bc8", "sha256_in_prefix": "eaf2d0414ca05c330010e990def160dbe97f389c071c11f16c063d02a55d4bc8", "size_in_bytes": 39424}, {"_path": "Library/bin/pdtreplace.exe", "path_type": "hardlink", "sha256": "811a7fffb04f2b62c33e2c57bf72a52b1dd1beb3f2d90e26b9913a853109757e", "sha256_in_prefix": "811a7fffb04f2b62c33e2c57bf72a52b1dd1beb3f2d90e26b9913a853109757e", "size_in_bytes": 47616}, {"_path": "Library/bin/pdtreverse.exe", "path_type": "hardlink", "sha256": "b024ae44223bc417275185a7d540078ffea210fa8415cb243ce1d5933472dd4f", "sha256_in_prefix": "b024ae44223bc417275185a7d540078ffea210fa8415cb243ce1d5933472dd4f", "size_in_bytes": 40448}, {"_path": "Library/bin/pdtshortestpath.exe", "path_type": "hardlink", "sha256": "15bc9e97b1df2b0036a830730afa6bee64651e4ab694227a8e941ca270ee7012", "sha256_in_prefix": "15bc9e97b1df2b0036a830730afa6bee64651e4ab694227a8e941ca270ee7012", "size_in_bytes": 45056}, {"_path": "Library/include/fst/accumulator.h", "path_type": "hardlink", "sha256": "322cfe33245baa4891f9304fcec2f400e4c29927f0e21d2643f241acc0ec1391", "sha256_in_prefix": "322cfe33245baa4891f9304fcec2f400e4c29927f0e21d2643f241acc0ec1391", "size_in_bytes": 30493}, {"_path": "Library/include/fst/add-on.h", "path_type": "hardlink", "sha256": "0dd6932c8a66e976762ad9484affc6ca56c3a7f26ebda14561b86f0ec65782d9", "sha256_in_prefix": "0dd6932c8a66e976762ad9484affc6ca56c3a7f26ebda14561b86f0ec65782d9", "size_in_bytes": 7831}, {"_path": "Library/include/fst/arc-arena.h", "path_type": "hardlink", "sha256": "3fc68efe02dbde9f70d55afde54c9654faba7530b6f94ef60e234d201de30512", "sha256_in_prefix": "3fc68efe02dbde9f70d55afde54c9654faba7530b6f94ef60e234d201de30512", "size_in_bytes": 7121}, {"_path": "Library/include/fst/arc-map.h", "path_type": "hardlink", "sha256": "3f0cb66283af4a06dad4e33c2a811d8f117808bcb1facd43ef0563acc5893bb0", "sha256_in_prefix": "3f0cb66283af4a06dad4e33c2a811d8f117808bcb1facd43ef0563acc5893bb0", "size_in_bytes": 40788}, {"_path": "Library/include/fst/arc.h", "path_type": "hardlink", "sha256": "3cae072040d4c7c5d5004d61100b3dd0970f928246462e15121c8ec574cbb5c4", "sha256_in_prefix": "3cae072040d4c7c5d5004d61100b3dd0970f928246462e15121c8ec574cbb5c4", "size_in_bytes": 9234}, {"_path": "Library/include/fst/arcfilter.h", "path_type": "hardlink", "sha256": "15e0bf30c11583dcb008588453b4a4af297cb91256c1db8c4688deb66c1a8cf5", "sha256_in_prefix": "15e0bf30c11583dcb008588453b4a4af297cb91256c1db8c4688deb66c1a8cf5", "size_in_bytes": 2909}, {"_path": "Library/include/fst/arcsort.h", "path_type": "hardlink", "sha256": "2affae925add6e09d7f76fa121f2cb9349dbfd31c7d4a087804fef33a94f5325", "sha256_in_prefix": "2affae925add6e09d7f76fa121f2cb9349dbfd31c7d4a087804fef33a94f5325", "size_in_bytes": 7350}, {"_path": "Library/include/fst/bi-table.h", "path_type": "hardlink", "sha256": "dea68e73360f112c9b899636fd8511ef8457eabec26f4f1b3a72156ea0d01d71", "sha256_in_prefix": "dea68e73360f112c9b899636fd8511ef8457eabec26f4f1b3a72156ea0d01d71", "size_in_bytes": 14353}, {"_path": "Library/include/fst/cache.h", "path_type": "hardlink", "sha256": "81587005fea009f60a24e05ede56312e867e830413205d34785bf2f7a9923bbe", "sha256_in_prefix": "81587005fea009f60a24e05ede56312e867e830413205d34785bf2f7a9923bbe", "size_in_bytes": 42976}, {"_path": "Library/include/fst/closure.h", "path_type": "hardlink", "sha256": "e40826612143602d0567d71f79a6e7b187eef2e52960abbd81e3dc9063ab83b9", "sha256_in_prefix": "e40826612143602d0567d71f79a6e7b187eef2e52960abbd81e3dc9063ab83b9", "size_in_bytes": 4844}, {"_path": "Library/include/fst/compact-fst.h", "path_type": "hardlink", "sha256": "230dd632a147ecd6a0f782ac28f0c3ff60f6420d387c1762e237c14384f96883", "sha256_in_prefix": "230dd632a147ecd6a0f782ac28f0c3ff60f6420d387c1762e237c14384f96883", "size_in_bytes": 58069}, {"_path": "Library/include/fst/compat.h", "path_type": "hardlink", "sha256": "87b7725d6ff5e180a9ef1dc37b63402aded92d7cc5fa72ef40dc9bdbb37887fa", "sha256_in_prefix": "87b7725d6ff5e180a9ef1dc37b63402aded92d7cc5fa72ef40dc9bdbb37887fa", "size_in_bytes": 9491}, {"_path": "Library/include/fst/complement.h", "path_type": "hardlink", "sha256": "1719be3605243a6daab41f234c78b189de8c3a43f898368d8caba8ebc5fd5333", "sha256_in_prefix": "1719be3605243a6daab41f234c78b189de8c3a43f898368d8caba8ebc5fd5333", "size_in_bytes": 8317}, {"_path": "Library/include/fst/compose-filter.h", "path_type": "hardlink", "sha256": "9aae69deca898aa2b78bfd0a0646854ef07c1bb9fbfa935330b7e239d4f4dde2", "sha256_in_prefix": "9aae69deca898aa2b78bfd0a0646854ef07c1bb9fbfa935330b7e239d4f4dde2", "size_in_bytes": 20456}, {"_path": "Library/include/fst/compose.h", "path_type": "hardlink", "sha256": "403b5566b26d8aecad7edf4df11bea951158acc394fdff6e131e0455ac4f6c01", "sha256_in_prefix": "403b5566b26d8aecad7edf4df11bea951158acc394fdff6e131e0455ac4f6c01", "size_in_bytes": 39641}, {"_path": "Library/include/fst/concat.h", "path_type": "hardlink", "sha256": "4bf28053b5b20c8a11ee38cb70037eaba3fd220ee88c86d644bf00f6f58b434a", "sha256_in_prefix": "4bf28053b5b20c8a11ee38cb70037eaba3fd220ee88c86d644bf00f6f58b434a", "size_in_bytes": 8220}, {"_path": "Library/include/fst/connect.h", "path_type": "hardlink", "sha256": "a786dc1e5b0db5c28fab2831524c1f8aa83f8f79cebb4965f4c7f48cc457aeb5", "sha256_in_prefix": "a786dc1e5b0db5c28fab2831524c1f8aa83f8f79cebb4965f4c7f48cc457aeb5", "size_in_bytes": 10197}, {"_path": "Library/include/fst/const-fst.h", "path_type": "hardlink", "sha256": "504872147a8011a77e9bbea810180fd3d5a109100cd845cb8833a545671576a5", "sha256_in_prefix": "504872147a8011a77e9bbea810180fd3d5a109100cd845cb8833a545671576a5", "size_in_bytes": 15665}, {"_path": "Library/include/fst/determinize.h", "path_type": "hardlink", "sha256": "a22528f4a9d8cb0b2fcb01543b72709781ed2abac91215080c1b53ec497996e8", "sha256_in_prefix": "a22528f4a9d8cb0b2fcb01543b72709781ed2abac91215080c1b53ec497996e8", "size_in_bytes": 41661}, {"_path": "Library/include/fst/dfs-visit.h", "path_type": "hardlink", "sha256": "c0409d39a85cedaa3038a6be7cc826dc205a917e3396da28e41dc29b4b3f9f69", "sha256_in_prefix": "c0409d39a85cedaa3038a6be7cc826dc205a917e3396da28e41dc29b4b3f9f69", "size_in_bytes": 7376}, {"_path": "Library/include/fst/difference.h", "path_type": "hardlink", "sha256": "2ff017265d3cdf48e01959fd49e8222804aa07aa6d8ce22b06c926d8c008d812", "sha256_in_prefix": "2ff017265d3cdf48e01959fd49e8222804aa07aa6d8ce22b06c926d8c008d812", "size_in_bytes": 7544}, {"_path": "Library/include/fst/disambiguate.h", "path_type": "hardlink", "sha256": "99fc2b6eced971ffca9efcda4b59c0c20e0a69e31f3636c30ba80311741f99c5", "sha256_in_prefix": "99fc2b6eced971ffca9efcda4b59c0c20e0a69e31f3636c30ba80311741f99c5", "size_in_bytes": 21184}, {"_path": "Library/include/fst/edit-fst.h", "path_type": "hardlink", "sha256": "6053bdaaceaeb393e06f62726599aa3c681a049fd23ec94502b5d616084277fd", "sha256_in_prefix": "6053bdaaceaeb393e06f62726599aa3c681a049fd23ec94502b5d616084277fd", "size_in_bytes": 27683}, {"_path": "Library/include/fst/encode.h", "path_type": "hardlink", "sha256": "162b4c9305cd9896164766e3dbb95fa68c563d4f9839789361a362d4c72e1c14", "sha256_in_prefix": "162b4c9305cd9896164766e3dbb95fa68c563d4f9839789361a362d4c72e1c14", "size_in_bytes": 19300}, {"_path": "Library/include/fst/epsnormalize.h", "path_type": "hardlink", "sha256": "15977ac96adc0666e3a6215aa05120dc4c3d6a9a2e8a0c1511b4a0845377de7f", "sha256_in_prefix": "15977ac96adc0666e3a6215aa05120dc4c3d6a9a2e8a0c1511b4a0845377de7f", "size_in_bytes": 2706}, {"_path": "Library/include/fst/equal.h", "path_type": "hardlink", "sha256": "23258944756c87561c0a3d4c3e4c3769a4cd44181d9e4e50c38df9bfd5a710cd", "sha256_in_prefix": "23258944756c87561c0a3d4c3e4c3769a4cd44181d9e4e50c38df9bfd5a710cd", "size_in_bytes": 6807}, {"_path": "Library/include/fst/equivalent.h", "path_type": "hardlink", "sha256": "20d9f30e97e64535a37b83a4f05daeaa5cbe82e0327d9dafb9dfe57412284252", "sha256_in_prefix": "20d9f30e97e64535a37b83a4f05daeaa5cbe82e0327d9dafb9dfe57412284252", "size_in_bytes": 9176}, {"_path": "Library/include/fst/error-weight.h", "path_type": "hardlink", "sha256": "ba081530b0a0c9c8d0ce021627e95edb9e379b3ea999f76c7a437be66320ee68", "sha256_in_prefix": "ba081530b0a0c9c8d0ce021627e95edb9e379b3ea999f76c7a437be66320ee68", "size_in_bytes": 2327}, {"_path": "Library/include/fst/expanded-fst.h", "path_type": "hardlink", "sha256": "455d4e2062bc3fb3ed9128fdfb05bf2bdb6cc071a2751ec7ac9567efd28dca31", "sha256_in_prefix": "455d4e2062bc3fb3ed9128fdfb05bf2bdb6cc071a2751ec7ac9567efd28dca31", "size_in_bytes": 6454}, {"_path": "Library/include/fst/expander-cache.h", "path_type": "hardlink", "sha256": "e9cd3691a77073cc208e70ba399457b46335adc74459a902d499af1a7687a7a8", "sha256_in_prefix": "e9cd3691a77073cc208e70ba399457b46335adc74459a902d499af1a7687a7a8", "size_in_bytes": 6648}, {"_path": "Library/include/fst/expectation-weight.h", "path_type": "hardlink", "sha256": "c53d8601bc32eddd1f6e6cc68bb398242c5fce8ca2d0c446931fb60153ef4c66", "sha256_in_prefix": "c53d8601bc32eddd1f6e6cc68bb398242c5fce8ca2d0c446931fb60153ef4c66", "size_in_bytes": 7687}, {"_path": "Library/include/fst/exports/exports.h", "path_type": "hardlink", "sha256": "665ad6a5564ac7a942f1496bb49b28e8f76c2aefd19c3361535526fc6416d7e1", "sha256_in_prefix": "665ad6a5564ac7a942f1496bb49b28e8f76c2aefd19c3361535526fc6416d7e1", "size_in_bytes": 1040}, {"_path": "Library/include/fst/exports/fst_Export.h", "path_type": "hardlink", "sha256": "5b79eacfb9057b85475635811b1daa8551d158072cadca12756d6da34372cbb1", "sha256_in_prefix": "5b79eacfb9057b85475635811b1daa8551d158072cadca12756d6da34372cbb1", "size_in_bytes": 856}, {"_path": "Library/include/fst/exports/fstcompact_Export.h", "path_type": "hardlink", "sha256": "e0bff7338793f7235b19dbecb218e593ea4ed5acc248f7642c75b6b441659534", "sha256_in_prefix": "e0bff7338793f7235b19dbecb218e593ea4ed5acc248f7642c75b6b441659534", "size_in_bytes": 1024}, {"_path": "Library/include/fst/exports/fstcompressscript_Export.h", "path_type": "hardlink", "sha256": "07f6b4a27ae5228596b05a476a8dfe877c44b16b95bebbe91e456cd99bae5a10", "sha256_in_prefix": "07f6b4a27ae5228596b05a476a8dfe877c44b16b95bebbe91e456cd99bae5a10", "size_in_bytes": 1192}, {"_path": "Library/include/fst/exports/fstconst_Export.h", "path_type": "hardlink", "sha256": "d90bd78156c095305126d1b19d576c74982a2fa22018910fefcbe40173fd256c", "sha256_in_prefix": "d90bd78156c095305126d1b19d576c74982a2fa22018910fefcbe40173fd256c", "size_in_bytes": 976}, {"_path": "Library/include/fst/exports/fstfar_Export.h", "path_type": "hardlink", "sha256": "c5870b16aaf7ecb6ac3399883621c1a9aeaa89849745ea52db144fb06bccf14b", "sha256_in_prefix": "c5870b16aaf7ecb6ac3399883621c1a9aeaa89849745ea52db144fb06bccf14b", "size_in_bytes": 928}, {"_path": "Library/include/fst/exports/fstfarscript_Export.h", "path_type": "hardlink", "sha256": "8e16eeef81ecb372fa7c2ec406bd8015ec975b0e24086961745bc31a8f7b9090", "sha256_in_prefix": "8e16eeef81ecb372fa7c2ec406bd8015ec975b0e24086961745bc31a8f7b9090", "size_in_bytes": 1066}, {"_path": "Library/include/fst/exports/fstlinearscript_Export.h", "path_type": "hardlink", "sha256": "932159ee8b54eadaa5cab168fe32fd2ce636726ec307e5af43e781492bb07a16", "sha256_in_prefix": "932159ee8b54eadaa5cab168fe32fd2ce636726ec307e5af43e781492bb07a16", "size_in_bytes": 1144}, {"_path": "Library/include/fst/exports/fstmpdtscript_Export.h", "path_type": "hardlink", "sha256": "31719e6deae24f2d8124c8c47c4ff4e2a794befa8c6ba4ce5d225301401af657", "sha256_in_prefix": "31719e6deae24f2d8124c8c47c4ff4e2a794befa8c6ba4ce5d225301401af657", "size_in_bytes": 1096}, {"_path": "Library/include/fst/exports/fstngram_Export.h", "path_type": "hardlink", "sha256": "da73c5464e7087f869e456d0948738e8c4363fc686809724ef4e712fe3ba5a03", "sha256_in_prefix": "da73c5464e7087f869e456d0948738e8c4363fc686809724ef4e712fe3ba5a03", "size_in_bytes": 976}, {"_path": "Library/include/fst/exports/fstpdtscript_Export.h", "path_type": "hardlink", "sha256": "f43f21d660da51a16653f359549046abc1d0875c362472e8214a0c8710bed1d4", "sha256_in_prefix": "f43f21d660da51a16653f359549046abc1d0875c362472e8214a0c8710bed1d4", "size_in_bytes": 1072}, {"_path": "Library/include/fst/exports/fstscript_Export.h", "path_type": "hardlink", "sha256": "a21b78a0d833d1e5c597fe98cafa153daa43bc910e918f29cbf0cbba84304257", "sha256_in_prefix": "a21b78a0d833d1e5c597fe98cafa153daa43bc910e918f29cbf0cbba84304257", "size_in_bytes": 1000}, {"_path": "Library/include/fst/exports/fstspecial_Export.h", "path_type": "hardlink", "sha256": "31211fa0774385a99b0649f92a1246b0473a71472158c9038e0031d89faf595e", "sha256_in_prefix": "31211fa0774385a99b0649f92a1246b0473a71472158c9038e0031d89faf595e", "size_in_bytes": 1024}, {"_path": "Library/include/fst/extensions/compress/compress.h", "path_type": "hardlink", "sha256": "4c8aa69be5f58eff03c6f8d8e4d12c29dbf0c6172fce47f6398d09a9d23f7331", "sha256_in_prefix": "4c8aa69be5f58eff03c6f8d8e4d12c29dbf0c6172fce47f6398d09a9d23f7331", "size_in_bytes": 26618}, {"_path": "Library/include/fst/extensions/compress/compressscript.h", "path_type": "hardlink", "sha256": "e4ed87ec3d77b9d9afd8e9c58bcdbebf2c22e7db2eb149aeacd730507e2e4112", "sha256_in_prefix": "e4ed87ec3d77b9d9afd8e9c58bcdbebf2c22e7db2eb149aeacd730507e2e4112", "size_in_bytes": 2064}, {"_path": "Library/include/fst/extensions/compress/elias.h", "path_type": "hardlink", "sha256": "f338d7bdb04caa50f7c2d46d3d4e92a753e3c28f17c2b77d62ac00935d631f0c", "sha256_in_prefix": "f338d7bdb04caa50f7c2d46d3d4e92a753e3c28f17c2b77d62ac00935d631f0c", "size_in_bytes": 3015}, {"_path": "Library/include/fst/extensions/far/compile-strings.h", "path_type": "hardlink", "sha256": "b1f33ebf2a3ca7e538e5cb61dd461ad4583aee817ee7261145671d6fd6995f80", "sha256_in_prefix": "b1f33ebf2a3ca7e538e5cb61dd461ad4583aee817ee7261145671d6fd6995f80", "size_in_bytes": 8707}, {"_path": "Library/include/fst/extensions/far/convert.h", "path_type": "hardlink", "sha256": "4574e722918b024d8b7ba32bd0a111a6a91ce3345c2a8d7602a6e3150c085099", "sha256_in_prefix": "4574e722918b024d8b7ba32bd0a111a6a91ce3345c2a8d7602a6e3150c085099", "size_in_bytes": 1614}, {"_path": "Library/include/fst/extensions/far/create.h", "path_type": "hardlink", "sha256": "2a1161d0d6a170d300a0fb6d7dc926b090ccf067de00d945384a94ce38b3f841", "sha256_in_prefix": "2a1161d0d6a170d300a0fb6d7dc926b090ccf067de00d945384a94ce38b3f841", "size_in_bytes": 1885}, {"_path": "Library/include/fst/extensions/far/encode.h", "path_type": "hardlink", "sha256": "5331bf05dc0104d8e4a5893ecbf449cf85e33ac10772438999c3df7c4d2db19b", "sha256_in_prefix": "5331bf05dc0104d8e4a5893ecbf449cf85e33ac10772438999c3df7c4d2db19b", "size_in_bytes": 1742}, {"_path": "Library/include/fst/extensions/far/equal.h", "path_type": "hardlink", "sha256": "d48b9ae0fc482a8b4c081bb6ab2fe63fc65c1d3cbd4e6ec5ed9eccda8b15ee03", "sha256_in_prefix": "d48b9ae0fc482a8b4c081bb6ab2fe63fc65c1d3cbd4e6ec5ed9eccda8b15ee03", "size_in_bytes": 1533}, {"_path": "Library/include/fst/extensions/far/extract.h", "path_type": "hardlink", "sha256": "25b582abf655f97ef5daaa4399e4b34464989689832426fd424e299ae942a654", "sha256_in_prefix": "25b582abf655f97ef5daaa4399e4b34464989689832426fd424e299ae942a654", "size_in_bytes": 4214}, {"_path": "Library/include/fst/extensions/far/far-class.h", "path_type": "hardlink", "sha256": "5758580fa4cf86220bb0aec8807fe0fda0cf816c34e3bf2dc5261864a2e9ba63", "sha256_in_prefix": "5758580fa4cf86220bb0aec8807fe0fda0cf816c34e3bf2dc5261864a2e9ba63", "size_in_bytes": 8952}, {"_path": "Library/include/fst/extensions/far/far.h", "path_type": "hardlink", "sha256": "05f947783e40b580d973645f0584e058a288d26db2acfc3a19c0a0a0acea71f8", "sha256_in_prefix": "05f947783e40b580d973645f0584e058a288d26db2acfc3a19c0a0a0acea71f8", "size_in_bytes": 15338}, {"_path": "Library/include/fst/extensions/far/farlib.h", "path_type": "hardlink", "sha256": "f15b4b839153e897bebd27628989020d90fd5d7c4941ff16e8e1e2a96b6c5a63", "sha256_in_prefix": "f15b4b839153e897bebd27628989020d90fd5d7c4941ff16e8e1e2a96b6c5a63", "size_in_bytes": 1321}, {"_path": "Library/include/fst/extensions/far/farscript.h", "path_type": "hardlink", "sha256": "9816a2e1354a62ce5f8117c1acff78ef1878fe4cd3fb2fd9178fc3f055abd23e", "sha256_in_prefix": "9816a2e1354a62ce5f8117c1acff78ef1878fe4cd3fb2fd9178fc3f055abd23e", "size_in_bytes": 11344}, {"_path": "Library/include/fst/extensions/far/getters.h", "path_type": "hardlink", "sha256": "f51f10a11d3db2b8eb46e834dc83f8ed9bdf204b36c453a03935b99173d782f1", "sha256_in_prefix": "f51f10a11d3db2b8eb46e834dc83f8ed9bdf204b36c453a03935b99173d782f1", "size_in_bytes": 1512}, {"_path": "Library/include/fst/extensions/far/info.h", "path_type": "hardlink", "sha256": "dd7f31da086722e8592331d1060576c031217e78c6178342804aab1b212cc271", "sha256_in_prefix": "dd7f31da086722e8592331d1060576c031217e78c6178342804aab1b212cc271", "size_in_bytes": 5602}, {"_path": "Library/include/fst/extensions/far/isomorphic.h", "path_type": "hardlink", "sha256": "d216308c93e3dd5b880eb7371b0a77f94be255c9f30fc88548e919b6cc8d6ddc", "sha256_in_prefix": "d216308c93e3dd5b880eb7371b0a77f94be255c9f30fc88548e919b6cc8d6ddc", "size_in_bytes": 1577}, {"_path": "Library/include/fst/extensions/far/map-reduce.h", "path_type": "hardlink", "sha256": "5c61c254050b3048b58d00f7711f5b50f3763b569c7a7d0e431bb62d8e223edc", "sha256_in_prefix": "5c61c254050b3048b58d00f7711f5b50f3763b569c7a7d0e431bb62d8e223edc", "size_in_bytes": 4012}, {"_path": "Library/include/fst/extensions/far/print-strings.h", "path_type": "hardlink", "sha256": "b62d25656d79b340ad0fe9506be2ab2c74c90d65d9dec8f5f877c73eb5a703bc", "sha256_in_prefix": "b62d25656d79b340ad0fe9506be2ab2c74c90d65d9dec8f5f877c73eb5a703bc", "size_in_bytes": 3738}, {"_path": "Library/include/fst/extensions/far/script-impl.h", "path_type": "hardlink", "sha256": "ccd6f11ffa46f4cc8c3c0f367b367d60d7cbd16697e72abed6259847869f9f06", "sha256_in_prefix": "ccd6f11ffa46f4cc8c3c0f367b367d60d7cbd16697e72abed6259847869f9f06", "size_in_bytes": 1304}, {"_path": "Library/include/fst/extensions/far/stlist.h", "path_type": "hardlink", "sha256": "5a7824dbc8b82e58eb5062cfcac14ba15854279f5ff68e9ba7c264eb30264893", "sha256_in_prefix": "5a7824dbc8b82e58eb5062cfcac14ba15854279f5ff68e9ba7c264eb30264893", "size_in_bytes": 9638}, {"_path": "Library/include/fst/extensions/far/sttable.h", "path_type": "hardlink", "sha256": "3b26e59b2bcec4d937a822ba9e09cb454219c88b334b34f1dd3bd1dfca56c931", "sha256_in_prefix": "3b26e59b2bcec4d937a822ba9e09cb454219c88b334b34f1dd3bd1dfca56c931", "size_in_bytes": 12128}, {"_path": "Library/include/fst/extensions/linear/linear-fst-data-builder.h", "path_type": "hardlink", "sha256": "279bb864afbe856d7765129b1e84a2b5b3d0f87d4f5874882b9fdc3723bdf39d", "sha256_in_prefix": "279bb864afbe856d7765129b1e84a2b5b3d0f87d4f5874882b9fdc3723bdf39d", "size_in_bytes": 41867}, {"_path": "Library/include/fst/extensions/linear/linear-fst-data.h", "path_type": "hardlink", "sha256": "7e5afcf45cf1cb2a1114348d70e303febef5de503a5fe1ca67a968d610cdf795", "sha256_in_prefix": "7e5afcf45cf1cb2a1114348d70e303febef5de503a5fe1ca67a968d610cdf795", "size_in_bytes": 17952}, {"_path": "Library/include/fst/extensions/linear/linear-fst.h", "path_type": "hardlink", "sha256": "7c9b16a695c730f4a30ecb6b9f468a49ed58eb04825275442108f44c67ad876e", "sha256_in_prefix": "7c9b16a695c730f4a30ecb6b9f468a49ed58eb04825275442108f44c67ad876e", "size_in_bytes": 41031}, {"_path": "Library/include/fst/extensions/linear/linearscript.h", "path_type": "hardlink", "sha256": "bdca9142188f88509912c75dae19c97bf1fa676db9593d99037e73ee5c4935f6", "sha256_in_prefix": "bdca9142188f88509912c75dae19c97bf1fa676db9593d99037e73ee5c4935f6", "size_in_bytes": 16164}, {"_path": "Library/include/fst/extensions/linear/loglinear-apply.h", "path_type": "hardlink", "sha256": "a18090d1e55a700be749f44792ef62388a1ca0759e33bea6b3ea7df10fb0db88", "sha256_in_prefix": "a18090d1e55a700be749f44792ef62388a1ca0759e33bea6b3ea7df10fb0db88", "size_in_bytes": 3325}, {"_path": "Library/include/fst/extensions/linear/trie.h", "path_type": "hardlink", "sha256": "5f0225bf840f606c9b1aba2bb954360043f0193598d712a1ffc2de5aead3ead1", "sha256_in_prefix": "5f0225bf840f606c9b1aba2bb954360043f0193598d712a1ffc2de5aead3ead1", "size_in_bytes": 13321}, {"_path": "Library/include/fst/extensions/mpdt/compose.h", "path_type": "hardlink", "sha256": "5f1035408ec719408d72f5a31f49bb8b49e8df0ac1e50c9bfba3920299400f60", "sha256_in_prefix": "5f1035408ec719408d72f5a31f49bb8b49e8df0ac1e50c9bfba3920299400f60", "size_in_bytes": 11131}, {"_path": "Library/include/fst/extensions/mpdt/expand.h", "path_type": "hardlink", "sha256": "90c82124c865d8369a741fd981787a06d6c90100d1366ee00c014fd456dd93ea", "sha256_in_prefix": "90c82124c865d8369a741fd981787a06d6c90100d1366ee00c014fd456dd93ea", "size_in_bytes": 12535}, {"_path": "Library/include/fst/extensions/mpdt/info.h", "path_type": "hardlink", "sha256": "94f3b10dbb696b3264cba019b234f6786bec1cce524d5a0dabecc7601e49f15e", "sha256_in_prefix": "94f3b10dbb696b3264cba019b234f6786bec1cce524d5a0dabecc7601e49f15e", "size_in_bytes": 6810}, {"_path": "Library/include/fst/extensions/mpdt/mpdt.h", "path_type": "hardlink", "sha256": "38063cec4b63ff5aaf30dc114c5db764c1984aa13aae428c75c089b6147bf819", "sha256_in_prefix": "38063cec4b63ff5aaf30dc114c5db764c1984aa13aae428c75c089b6147bf819", "size_in_bytes": 10483}, {"_path": "Library/include/fst/extensions/mpdt/mpdtlib.h", "path_type": "hardlink", "sha256": "8fa513f08493da6dcba7114aee900a31bf47962cf27871653cb1226ce27af5ec", "sha256_in_prefix": "8fa513f08493da6dcba7114aee900a31bf47962cf27871653cb1226ce27af5ec", "size_in_bytes": 1301}, {"_path": "Library/include/fst/extensions/mpdt/mpdtscript.h", "path_type": "hardlink", "sha256": "7d66aede4f99c37367fcd4a1c9679c3bd87519a2a76030d257b3e17a4f471c6a", "sha256_in_prefix": "7d66aede4f99c37367fcd4a1c9679c3bd87519a2a76030d257b3e17a4f471c6a", "size_in_bytes": 7016}, {"_path": "Library/include/fst/extensions/mpdt/read_write_utils.h", "path_type": "hardlink", "sha256": "6a38358fce1dbeca24538a73db4a416720a01587a4452ee0dcfbb26a40083fdf", "sha256_in_prefix": "6a38358fce1dbeca24538a73db4a416720a01587a4452ee0dcfbb26a40083fdf", "size_in_bytes": 3496}, {"_path": "Library/include/fst/extensions/mpdt/reverse.h", "path_type": "hardlink", "sha256": "f422a3e88ae58fd00bf2238ee9122925dee30d51dd1191b2e1b43226110d337a", "sha256_in_prefix": "f422a3e88ae58fd00bf2238ee9122925dee30d51dd1191b2e1b43226110d337a", "size_in_bytes": 2224}, {"_path": "Library/include/fst/extensions/ngram/bitmap-index.h", "path_type": "hardlink", "sha256": "82409676e764a9a0ae79904ae9253fafca80c165e53146e6dfbea8221579d1b0", "sha256_in_prefix": "82409676e764a9a0ae79904ae9253fafca80c165e53146e6dfbea8221579d1b0", "size_in_bytes": 13788}, {"_path": "Library/include/fst/extensions/ngram/ngram-fst.h", "path_type": "hardlink", "sha256": "84029129145efb8046ae1525a9d60e7874566c25a33fe5594ba996ab9841a1a4", "sha256_in_prefix": "84029129145efb8046ae1525a9d60e7874566c25a33fe5594ba996ab9841a1a4", "size_in_bytes": 35454}, {"_path": "Library/include/fst/extensions/ngram/nthbit.h", "path_type": "hardlink", "sha256": "7ccf2e50045898b54ecadf67cc923d6cb62cf68e82fb263a44b8934e02c226be", "sha256_in_prefix": "7ccf2e50045898b54ecadf67cc923d6cb62cf68e82fb263a44b8934e02c226be", "size_in_bytes": 4331}, {"_path": "Library/include/fst/extensions/pdt/collection.h", "path_type": "hardlink", "sha256": "1de746b3d9c6be110efb2d50f718c5777d3c01d504b5d3097cefea9da590b9d5", "sha256_in_prefix": "1de746b3d9c6be110efb2d50f718c5777d3c01d504b5d3097cefea9da590b9d5", "size_in_bytes": 3445}, {"_path": "Library/include/fst/extensions/pdt/compose.h", "path_type": "hardlink", "sha256": "febabbfd2f32e56070362406a7ea3c539b53d98a7704e6ee332468cb06f460c9", "sha256_in_prefix": "febabbfd2f32e56070362406a7ea3c539b53d98a7704e6ee332468cb06f460c9", "size_in_bytes": 17449}, {"_path": "Library/include/fst/extensions/pdt/expand.h", "path_type": "hardlink", "sha256": "1b15fcf5e652002e9cda5be97b8f30e115c0fb1b56016b1584667b0f755170b6", "sha256_in_prefix": "1b15fcf5e652002e9cda5be97b8f30e115c0fb1b56016b1584667b0f755170b6", "size_in_bytes": 35836}, {"_path": "Library/include/fst/extensions/pdt/getters.h", "path_type": "hardlink", "sha256": "2a4dd406bc2fcbd32e7d28261262eee277f875dc2b060fede3d15d753f3bf7e5", "sha256_in_prefix": "2a4dd406bc2fcbd32e7d28261262eee277f875dc2b060fede3d15d753f3bf7e5", "size_in_bytes": 1230}, {"_path": "Library/include/fst/extensions/pdt/info.h", "path_type": "hardlink", "sha256": "a3c14cd63ef5f5c2e7103719ffc354da1734c9a54b36f309a63529a06bd1d33d", "sha256_in_prefix": "a3c14cd63ef5f5c2e7103719ffc354da1734c9a54b36f309a63529a06bd1d33d", "size_in_bytes": 5122}, {"_path": "Library/include/fst/extensions/pdt/paren.h", "path_type": "hardlink", "sha256": "521ff984543a2c4bf68ad04adf5783379043048c832ce0bcf2c1e3d5749ce5d8", "sha256_in_prefix": "521ff984543a2c4bf68ad04adf5783379043048c832ce0bcf2c1e3d5749ce5d8", "size_in_bytes": 16958}, {"_path": "Library/include/fst/extensions/pdt/pdt.h", "path_type": "hardlink", "sha256": "4e053ac88b1c24683c2e4e631600ed079658e6ce417eba8a3ded0f08c7ed22fa", "sha256_in_prefix": "4e053ac88b1c24683c2e4e631600ed079658e6ce417eba8a3ded0f08c7ed22fa", "size_in_bytes": 6030}, {"_path": "Library/include/fst/extensions/pdt/pdtlib.h", "path_type": "hardlink", "sha256": "7f66533ea185f978537f018a58580925eaa6a9d46d4d8190812672172b0f39f1", "sha256_in_prefix": "7f66533ea185f978537f018a58580925eaa6a9d46d4d8190812672172b0f39f1", "size_in_bytes": 1295}, {"_path": "Library/include/fst/extensions/pdt/pdtscript.h", "path_type": "hardlink", "sha256": "9a0024cb6f8d9a87cbb6486a940e88d5f90d8f5c64d99049565fed61e70fdc2e", "sha256_in_prefix": "9a0024cb6f8d9a87cbb6486a940e88d5f90d8f5c64d99049565fed61e70fdc2e", "size_in_bytes": 10868}, {"_path": "Library/include/fst/extensions/pdt/replace.h", "path_type": "hardlink", "sha256": "794b6b648883f74803fcc54b02fc065159a278a4aab903370341c2cd88c01a0a", "sha256_in_prefix": "794b6b648883f74803fcc54b02fc065159a278a4aab903370341c2cd88c01a0a", "size_in_bytes": 33361}, {"_path": "Library/include/fst/extensions/pdt/reverse.h", "path_type": "hardlink", "sha256": "4608b212dd5a17ebe17d76cbe3e06b71193c6d242dbb29bac5592de1a31cf4ea", "sha256_in_prefix": "4608b212dd5a17ebe17d76cbe3e06b71193c6d242dbb29bac5592de1a31cf4ea", "size_in_bytes": 1684}, {"_path": "Library/include/fst/extensions/pdt/shortest-path.h", "path_type": "hardlink", "sha256": "99ee914be3f00016594098238a98f38ce0fb5cd9fe703348e61555377b21ca43", "sha256_in_prefix": "99ee914be3f00016594098238a98f38ce0fb5cd9fe703348e61555377b21ca43", "size_in_bytes": 26117}, {"_path": "Library/include/fst/extensions/special/phi-fst.h", "path_type": "hardlink", "sha256": "bcbec8b862149c35565817bc40d539714e96f6455dd3c5429a71cba423ddc0cd", "sha256_in_prefix": "bcbec8b862149c35565817bc40d539714e96f6455dd3c5429a71cba423ddc0cd", "size_in_bytes": 6364}, {"_path": "Library/include/fst/extensions/special/rho-fst.h", "path_type": "hardlink", "sha256": "1dff37aca86a399741e2d72d164d0dce29a7d47fc80ba67bb7ffb812c6d43bea", "sha256_in_prefix": "1dff37aca86a399741e2d72d164d0dce29a7d47fc80ba67bb7ffb812c6d43bea", "size_in_bytes": 5896}, {"_path": "Library/include/fst/extensions/special/sigma-fst.h", "path_type": "hardlink", "sha256": "ca65e11cb5e7eb4f1b6a466413b2644a6d747b94171adc4248c50a234f38ab6d", "sha256_in_prefix": "ca65e11cb5e7eb4f1b6a466413b2644a6d747b94171adc4248c50a234f38ab6d", "size_in_bytes": 5949}, {"_path": "Library/include/fst/factor-weight.h", "path_type": "hardlink", "sha256": "7bc1a2bf0e05fe985b74ae37c21bbe2c06fae28df4ae6ca565dbb5d98250e548", "sha256_in_prefix": "7bc1a2bf0e05fe985b74ae37c21bbe2c06fae28df4ae6ca565dbb5d98250e548", "size_in_bytes": 17788}, {"_path": "Library/include/fst/filter-state.h", "path_type": "hardlink", "sha256": "7ba26d6c785239bc4dd13f853232e6e0fd1be08b6d83e515ad27128a4f0f5c38", "sha256_in_prefix": "7ba26d6c785239bc4dd13f853232e6e0fd1be08b6d83e515ad27128a4f0f5c38", "size_in_bytes": 5337}, {"_path": "Library/include/fst/flags.h", "path_type": "hardlink", "sha256": "ce7ae21f6d24f182d66f12dce7ddf11433f6beef51bf2f046ace4f677a945646", "sha256_in_prefix": "ce7ae21f6d24f182d66f12dce7ddf11433f6beef51bf2f046ace4f677a945646", "size_in_bytes": 8581}, {"_path": "Library/include/fst/float-weight.h", "path_type": "hardlink", "sha256": "f4501232cc1d816ded73f3556eb0d5105cd7ed8c5ba731e798e01842252feb82", "sha256_in_prefix": "f4501232cc1d816ded73f3556eb0d5105cd7ed8c5ba731e798e01842252feb82", "size_in_bytes": 39060}, {"_path": "Library/include/fst/fst-decl.h", "path_type": "hardlink", "sha256": "185b99b4f3c7a1c489714494fc1ce26db6376a797e9d564f6594d0c1e12ddca1", "sha256_in_prefix": "185b99b4f3c7a1c489714494fc1ce26db6376a797e9d564f6594d0c1e12ddca1", "size_in_bytes": 6399}, {"_path": "Library/include/fst/fst.h", "path_type": "hardlink", "sha256": "5ee07ae962a0e819e74b6a5c8091a1a065a19574f7e0f7fb602c37ef2d58be23", "sha256_in_prefix": "5ee07ae962a0e819e74b6a5c8091a1a065a19574f7e0f7fb602c37ef2d58be23", "size_in_bytes": 35930}, {"_path": "Library/include/fst/fstlib.h", "path_type": "hardlink", "sha256": "ca99e2fc9b6ca08f5569b300bb40f4a138a90199b5a915055c0046043c516822", "sha256_in_prefix": "ca99e2fc9b6ca08f5569b300bb40f4a138a90199b5a915055c0046043c516822", "size_in_bytes": 4491}, {"_path": "Library/include/fst/generic-register.h", "path_type": "hardlink", "sha256": "e6b0f00b6941f3dcfbf245989e7ca57732c5c385695c501037e2df24b793e82b", "sha256_in_prefix": "e6b0f00b6941f3dcfbf245989e7ca57732c5c385695c501037e2df24b793e82b", "size_in_bytes": 5247}, {"_path": "Library/include/fst/heap.h", "path_type": "hardlink", "sha256": "bd637002e89274af480fb3ec88154bf1f8577d98b7b4e21ee0d459005a755ea0", "sha256_in_prefix": "bd637002e89274af480fb3ec88154bf1f8577d98b7b4e21ee0d459005a755ea0", "size_in_bytes": 5187}, {"_path": "Library/include/fst/icu.h", "path_type": "hardlink", "sha256": "7f1db981de63089b2c6becdb3b843a5bec1e3af3c36e8a14261cc1777d739ad5", "sha256_in_prefix": "7f1db981de63089b2c6becdb3b843a5bec1e3af3c36e8a14261cc1777d739ad5", "size_in_bytes": 5231}, {"_path": "Library/include/fst/intersect.h", "path_type": "hardlink", "sha256": "c770e53821b66d881cdc297f95776ce566bcfae49cd73538269864fa78467c73", "sha256_in_prefix": "c770e53821b66d881cdc297f95776ce566bcfae49cd73538269864fa78467c73", "size_in_bytes": 6317}, {"_path": "Library/include/fst/interval-set.h", "path_type": "hardlink", "sha256": "8a46b1841b8997c3120a1c66904eaea5210d7715a75a5733812213b9294ed248", "sha256_in_prefix": "8a46b1841b8997c3120a1c66904eaea5210d7715a75a5733812213b9294ed248", "size_in_bytes": 12962}, {"_path": "Library/include/fst/invert.h", "path_type": "hardlink", "sha256": "93a257da5100e8c543a44e4b10c548ddf91e579079e5c2a48be8e82e2d532d6a", "sha256_in_prefix": "93a257da5100e8c543a44e4b10c548ddf91e579079e5c2a48be8e82e2d532d6a", "size_in_bytes": 4474}, {"_path": "Library/include/fst/isomorphic.h", "path_type": "hardlink", "sha256": "4fed88a62b49b7e02a91a50c203d488bd31b93a3c1d9013421b8d4f23fcda8c5", "sha256_in_prefix": "4fed88a62b49b7e02a91a50c203d488bd31b93a3c1d9013421b8d4f23fcda8c5", "size_in_bytes": 9817}, {"_path": "Library/include/fst/label-reachable.h", "path_type": "hardlink", "sha256": "594c7cc3c7f910f81729679d5d316c2f9097465f616b19189a004bf2f8ec0233", "sha256_in_prefix": "594c7cc3c7f910f81729679d5d316c2f9097465f616b19189a004bf2f8ec0233", "size_in_bytes": 23164}, {"_path": "Library/include/fst/lexicographic-weight.h", "path_type": "hardlink", "sha256": "ba1cf1fbd5b8938737c5a541f2fd975fa1f722fc7e0615dde18360c7c20511de", "sha256_in_prefix": "ba1cf1fbd5b8938737c5a541f2fd975fa1f722fc7e0615dde18360c7c20511de", "size_in_bytes": 6354}, {"_path": "Library/include/fst/lock.h", "path_type": "hardlink", "sha256": "9d36c351a5315e785a55d5a8489488f9d841064f7bceef08cddc7cf14eddfd76", "sha256_in_prefix": "9d36c351a5315e785a55d5a8489488f9d841064f7bceef08cddc7cf14eddfd76", "size_in_bytes": 1987}, {"_path": "Library/include/fst/log.h", "path_type": "hardlink", "sha256": "13395fa04d63ec859a7ac288c07fdbe152c321b02ee4452221e57a2740b35697", "sha256_in_prefix": "13395fa04d63ec859a7ac288c07fdbe152c321b02ee4452221e57a2740b35697", "size_in_bytes": 2258}, {"_path": "Library/include/fst/lookahead-filter.h", "path_type": "hardlink", "sha256": "3b94ad4e18d2ffb20ee09f91e3271cc0173a0d499b9a738f3b572deef8fa0e4b", "sha256_in_prefix": "3b94ad4e18d2ffb20ee09f91e3271cc0173a0d499b9a738f3b572deef8fa0e4b", "size_in_bytes": 23986}, {"_path": "Library/include/fst/lookahead-matcher.h", "path_type": "hardlink", "sha256": "a82c6c058ca594cef70d65b35729af1a0b7444d10d6f585c3c56477f114f2b30", "sha256_in_prefix": "a82c6c058ca594cef70d65b35729af1a0b7444d10d6f585c3c56477f114f2b30", "size_in_bytes": 30097}, {"_path": "Library/include/fst/mapped-file.h", "path_type": "hardlink", "sha256": "f551db65249e13799730594b8309b0ca5955f4d6a760917160210292724d38f3", "sha256_in_prefix": "f551db65249e13799730594b8309b0ca5955f4d6a760917160210292724d38f3", "size_in_bytes": 4146}, {"_path": "Library/include/fst/matcher-fst.h", "path_type": "hardlink", "sha256": "a015dc5d3ce9282fc206544a1f742bc5936befffb1eb87aed5df078f24894dac", "sha256_in_prefix": "a015dc5d3ce9282fc206544a1f742bc5936befffb1eb87aed5df078f24894dac", "size_in_bytes": 12874}, {"_path": "Library/include/fst/matcher.h", "path_type": "hardlink", "sha256": "5c12d7aeeef2b58e00060e90a9daf7a307ca7161be610b4bad4e25979b5defd1", "sha256_in_prefix": "5c12d7aeeef2b58e00060e90a9daf7a307ca7161be610b4bad4e25979b5defd1", "size_in_bytes": 52255}, {"_path": "Library/include/fst/memory.h", "path_type": "hardlink", "sha256": "1ee3858dccc248e44b4b470ef31d1e6cf806e5104f3b52708e96eebaefd6cfcf", "sha256_in_prefix": "1ee3858dccc248e44b4b470ef31d1e6cf806e5104f3b52708e96eebaefd6cfcf", "size_in_bytes": 11087}, {"_path": "Library/include/fst/minimize.h", "path_type": "hardlink", "sha256": "de350dbec7da05ba5d28c8ae5c2e31f3fba1e1886ab7d23b3335121ff495436e", "sha256_in_prefix": "de350dbec7da05ba5d28c8ae5c2e31f3fba1e1886ab7d23b3335121ff495436e", "size_in_bytes": 21386}, {"_path": "Library/include/fst/mutable-fst.h", "path_type": "hardlink", "sha256": "e576f0ceb90739d4c7c189a67edec9cfaa61d8f77f31d68eb279a07ba6dacc83", "sha256_in_prefix": "e576f0ceb90739d4c7c189a67edec9cfaa61d8f77f31d68eb279a07ba6dacc83", "size_in_bytes": 12946}, {"_path": "Library/include/fst/pair-weight.h", "path_type": "hardlink", "sha256": "54df00f0f2a2dfd11d5661dea5eca466ff446e1156ffc0d05fb269abbedc45c7", "sha256_in_prefix": "54df00f0f2a2dfd11d5661dea5eca466ff446e1156ffc0d05fb269abbedc45c7", "size_in_bytes": 4854}, {"_path": "Library/include/fst/partition.h", "path_type": "hardlink", "sha256": "8a3ded287f8695869a7aebf53a1b23d074a9c81cf634148a0f6fa98f76b6f4cb", "sha256_in_prefix": "8a3ded287f8695869a7aebf53a1b23d074a9c81cf634148a0f6fa98f76b6f4cb", "size_in_bytes": 13320}, {"_path": "Library/include/fst/power-weight-mappers.h", "path_type": "hardlink", "sha256": "6db5be5c45d7987cf7d13883e806eefc6aa7ec308a39db448b62c7a2391d46ad", "sha256_in_prefix": "6db5be5c45d7987cf7d13883e806eefc6aa7ec308a39db448b62c7a2391d46ad", "size_in_bytes": 3449}, {"_path": "Library/include/fst/power-weight.h", "path_type": "hardlink", "sha256": "2254c9b1b336ee8d4d7e990576fa20ba6512636fa955fd9448c546c454c7a765", "sha256_in_prefix": "2254c9b1b336ee8d4d7e990576fa20ba6512636fa955fd9448c546c454c7a765", "size_in_bytes": 5474}, {"_path": "Library/include/fst/product-weight.h", "path_type": "hardlink", "sha256": "43256b8940e8ca44570b189f990efd237f8169cd3c3f4d4d6f45b18945c5051f", "sha256_in_prefix": "43256b8940e8ca44570b189f990efd237f8169cd3c3f4d4d6f45b18945c5051f", "size_in_bytes": 4609}, {"_path": "Library/include/fst/project.h", "path_type": "hardlink", "sha256": "3150c337c8beccf585795e53353516d0199efe71940edbb44037b739f429e304", "sha256_in_prefix": "3150c337c8beccf585795e53353516d0199efe71940edbb44037b739f429e304", "size_in_bytes": 5473}, {"_path": "Library/include/fst/properties.h", "path_type": "hardlink", "sha256": "e5282535a8c2d4ad786c688f632330db250c7c9e419281b9a5bcc928f5ec38a8", "sha256_in_prefix": "e5282535a8c2d4ad786c688f632330db250c7c9e419281b9a5bcc928f5ec38a8", "size_in_bytes": 22355}, {"_path": "Library/include/fst/prune.h", "path_type": "hardlink", "sha256": "5b1ee7c947d319d34c9c6bbaf8c268c98ff46d483e9df1303bd7bd36eda2c55e", "sha256_in_prefix": "5b1ee7c947d319d34c9c6bbaf8c268c98ff46d483e9df1303bd7bd36eda2c55e", "size_in_bytes": 12863}, {"_path": "Library/include/fst/push.h", "path_type": "hardlink", "sha256": "312beefcfd5d1a373d5d574c08f5467cb79604d737cffd6d87b68b89353903af", "sha256_in_prefix": "312beefcfd5d1a373d5d574c08f5467cb79604d737cffd6d87b68b89353903af", "size_in_bytes": 6531}, {"_path": "Library/include/fst/queue.h", "path_type": "hardlink", "sha256": "748cc158717ffed4c038ee2276b247cb83a53e2b12c69ccf206db15292a3d032", "sha256_in_prefix": "748cc158717ffed4c038ee2276b247cb83a53e2b12c69ccf206db15292a3d032", "size_in_bytes": 32505}, {"_path": "Library/include/fst/randequivalent.h", "path_type": "hardlink", "sha256": "8022229844df0fbe864aa871aaf8898c5383a20b2f1360f546ca1f65d3d21baf", "sha256_in_prefix": "8022229844df0fbe864aa871aaf8898c5383a20b2f1360f546ca1f65d3d21baf", "size_in_bytes": 4934}, {"_path": "Library/include/fst/randgen.h", "path_type": "hardlink", "sha256": "b7c8ec6ed6b3726766cad52cbf7626b457f885ce67898f2e92165aaa1c091405", "sha256_in_prefix": "b7c8ec6ed6b3726766cad52cbf7626b457f885ce67898f2e92165aaa1c091405", "size_in_bytes": 26586}, {"_path": "Library/include/fst/rational.h", "path_type": "hardlink", "sha256": "a4b43c260c367f56b95f2d57e9d255a625c169ae93a3ffc6e295554a9abaca0a", "sha256_in_prefix": "a4b43c260c367f56b95f2d57e9d255a625c169ae93a3ffc6e295554a9abaca0a", "size_in_bytes": 10654}, {"_path": "Library/include/fst/register.h", "path_type": "hardlink", "sha256": "c5cf26ef263b1c5eea1d32d612f432c7a51439481f0bb79a6646f77377da5c52", "sha256_in_prefix": "c5cf26ef263b1c5eea1d32d612f432c7a51439481f0bb79a6646f77377da5c52", "size_in_bytes": 4469}, {"_path": "Library/include/fst/relabel.h", "path_type": "hardlink", "sha256": "cbb9ef0bd479a5cbd55ce2861a2b94475bc38cc8ef835d38844d725a87155482", "sha256_in_prefix": "cbb9ef0bd479a5cbd55ce2861a2b94475bc38cc8ef835d38844d725a87155482", "size_in_bytes": 16470}, {"_path": "Library/include/fst/replace-util.h", "path_type": "hardlink", "sha256": "c4600bc2776fdbd0447a9d6c7782b41117a1b495d1dfbd82d89dfb340c0bd3e7", "sha256_in_prefix": "c4600bc2776fdbd0447a9d6c7782b41117a1b495d1dfbd82d89dfb340c0bd3e7", "size_in_bytes": 23426}, {"_path": "Library/include/fst/replace.h", "path_type": "hardlink", "sha256": "ead488df26269121bc896748a0653c954586693788c8379c385eecd3af81f3b9", "sha256_in_prefix": "ead488df26269121bc896748a0653c954586693788c8379c385eecd3af81f3b9", "size_in_bytes": 57780}, {"_path": "Library/include/fst/reverse.h", "path_type": "hardlink", "sha256": "40f99da85ee2459bd4627fc6381efb0c795d2a1db9148b0551880d3b920e1916", "sha256_in_prefix": "40f99da85ee2459bd4627fc6381efb0c795d2a1db9148b0551880d3b920e1916", "size_in_bytes": 4791}, {"_path": "Library/include/fst/reweight.h", "path_type": "hardlink", "sha256": "3e42c8af24facf2b0b4274ae6e36ec6b32502c91328009d91f9fa8ff8e9e1fe6", "sha256_in_prefix": "3e42c8af24facf2b0b4274ae6e36ec6b32502c91328009d91f9fa8ff8e9e1fe6", "size_in_bytes": 5717}, {"_path": "Library/include/fst/rmepsilon.h", "path_type": "hardlink", "sha256": "c0decb7c39827e39c8fab9dddeded343c914e31c41e9f19d63f3ea12464d7208", "sha256_in_prefix": "c0decb7c39827e39c8fab9dddeded343c914e31c41e9f19d63f3ea12464d7208", "size_in_bytes": 19223}, {"_path": "Library/include/fst/rmfinalepsilon.h", "path_type": "hardlink", "sha256": "61cf3b3765aef0398a415b68a2749383831cc2922d50d63b97f97dd41bb5d358", "sha256_in_prefix": "61cf3b3765aef0398a415b68a2749383831cc2922d50d63b97f97dd41bb5d358", "size_in_bytes": 3153}, {"_path": "Library/include/fst/script/arc-class.h", "path_type": "hardlink", "sha256": "9152cb519e42cee1d3ce3be0bc73972cfc032e7d51e680c14b4b214f8db1e2d9", "sha256_in_prefix": "9152cb519e42cee1d3ce3be0bc73972cfc032e7d51e680c14b4b214f8db1e2d9", "size_in_bytes": 1704}, {"_path": "Library/include/fst/script/arcfilter-impl.h", "path_type": "hardlink", "sha256": "ebce93da726527f03c2ffa09bf7868e92a85a26dfe02789fe029c6fd907f9f0d", "sha256_in_prefix": "ebce93da726527f03c2ffa09bf7868e92a85a26dfe02789fe029c6fd907f9f0d", "size_in_bytes": 1032}, {"_path": "Library/include/fst/script/arciterator-class.h", "path_type": "hardlink", "sha256": "28d9058670337a6873c9dd8abd5cf49ca83ee9bf0fef48cdadaef8295a4c09c6", "sha256_in_prefix": "28d9058670337a6873c9dd8abd5cf49ca83ee9bf0fef48cdadaef8295a4c09c6", "size_in_bytes": 6658}, {"_path": "Library/include/fst/script/arcsort.h", "path_type": "hardlink", "sha256": "13f16d01f06e6a93ddb3ebfc571b2277d6ccde633b4dbe92615dc96dd487a7e8", "sha256_in_prefix": "13f16d01f06e6a93ddb3ebfc571b2277d6ccde633b4dbe92615dc96dd487a7e8", "size_in_bytes": 1647}, {"_path": "Library/include/fst/script/arg-packs.h", "path_type": "hardlink", "sha256": "1e9c204b912248538d83dc42d2edefd332416dc6dd57f3c59e1a134d558d4a4c", "sha256_in_prefix": "1e9c204b912248538d83dc42d2edefd332416dc6dd57f3c59e1a134d558d4a4c", "size_in_bytes": 1637}, {"_path": "Library/include/fst/script/closure.h", "path_type": "hardlink", "sha256": "0054139abfd07560b13c5338d49f1dad327855d179feaea2bfc8ecec2c2402f7", "sha256_in_prefix": "0054139abfd07560b13c5338d49f1dad327855d179feaea2bfc8ecec2c2402f7", "size_in_bytes": 1343}, {"_path": "Library/include/fst/script/compile-impl.h", "path_type": "hardlink", "sha256": "360ee816eb182479b88f4ff2f4d661ba1c34451a811d1053bffb10bfa2aef0ae", "sha256_in_prefix": "360ee816eb182479b88f4ff2f4d661ba1c34451a811d1053bffb10bfa2aef0ae", "size_in_bytes": 8371}, {"_path": "Library/include/fst/script/compile.h", "path_type": "hardlink", "sha256": "7009663c16b90bcf3113706a32b85392e814d4d2dd3ccc57093712cc39629b4a", "sha256_in_prefix": "7009663c16b90bcf3113706a32b85392e814d4d2dd3ccc57093712cc39629b4a", "size_in_bytes": 3644}, {"_path": "Library/include/fst/script/compose.h", "path_type": "hardlink", "sha256": "a32244aee7afa4d4ee9c907f1689dd820600cf5a56499c1fc8433ebb24d8b127", "sha256_in_prefix": "a32244aee7afa4d4ee9c907f1689dd820600cf5a56499c1fc8433ebb24d8b127", "size_in_bytes": 1674}, {"_path": "Library/include/fst/script/concat.h", "path_type": "hardlink", "sha256": "7b59f5c69f5dd8dc68b84603ddf55acbb08d811b205cd2ca44fadd570af63ee2", "sha256_in_prefix": "7b59f5c69f5dd8dc68b84603ddf55acbb08d811b205cd2ca44fadd570af63ee2", "size_in_bytes": 2330}, {"_path": "Library/include/fst/script/connect.h", "path_type": "hardlink", "sha256": "970e1cb8d7d9a7fe74bb669af9df9a90a64f560ce897cd3fcd3c4de2f591d9e2", "sha256_in_prefix": "970e1cb8d7d9a7fe74bb669af9df9a90a64f560ce897cd3fcd3c4de2f591d9e2", "size_in_bytes": 1157}, {"_path": "Library/include/fst/script/convert.h", "path_type": "hardlink", "sha256": "ef58ec6ae0292cd635c9237ead67b6d7d0f2e6c0f634e000106ba7546d7b7372", "sha256_in_prefix": "ef58ec6ae0292cd635c9237ead67b6d7d0f2e6c0f634e000106ba7546d7b7372", "size_in_bytes": 1735}, {"_path": "Library/include/fst/script/decode.h", "path_type": "hardlink", "sha256": "23250aff4ee476bc79f879d4c4230f12bf96cf4e65ca4f21cdee3dfc182408fe", "sha256_in_prefix": "23250aff4ee476bc79f879d4c4230f12bf96cf4e65ca4f21cdee3dfc182408fe", "size_in_bytes": 1476}, {"_path": "Library/include/fst/script/determinize.h", "path_type": "hardlink", "sha256": "607f3c9558baf9ac15f687ec8f993ed0c337917fca2b9cad1770736baec34698", "sha256_in_prefix": "607f3c9558baf9ac15f687ec8f993ed0c337917fca2b9cad1770736baec34698", "size_in_bytes": 2736}, {"_path": "Library/include/fst/script/difference.h", "path_type": "hardlink", "sha256": "2ba6bfb87bada702fdfa465cb382066808d2554ed7b1eb992c7f9648fcc0dd84", "sha256_in_prefix": "2ba6bfb87bada702fdfa465cb382066808d2554ed7b1eb992c7f9648fcc0dd84", "size_in_bytes": 1741}, {"_path": "Library/include/fst/script/disambiguate.h", "path_type": "hardlink", "sha256": "90d45a37f6bc7075ef6660dfbc6aaf56bd0a09d53677f5a7dc05492ead566d52", "sha256_in_prefix": "90d45a37f6bc7075ef6660dfbc6aaf56bd0a09d53677f5a7dc05492ead566d52", "size_in_bytes": 2524}, {"_path": "Library/include/fst/script/draw-impl.h", "path_type": "hardlink", "sha256": "d0a56a0c2fd13df5db79e4225bbc8a69ac8b0043e368585ebdfd8855bdaebe41", "sha256_in_prefix": "d0a56a0c2fd13df5db79e4225bbc8a69ac8b0043e368585ebdfd8855bdaebe41", "size_in_bytes": 6507}, {"_path": "Library/include/fst/script/draw.h", "path_type": "hardlink", "sha256": "57153ae032279ace27196c19d7095d547ec4978b05a18256c91ae4789b8c5ccf", "sha256_in_prefix": "57153ae032279ace27196c19d7095d547ec4978b05a18256c91ae4789b8c5ccf", "size_in_bytes": 2685}, {"_path": "Library/include/fst/script/encode.h", "path_type": "hardlink", "sha256": "10d18a8e3a2e9a672c03ca7f9ef76a00e47465ce684ce3c56d939a72455934f2", "sha256_in_prefix": "10d18a8e3a2e9a672c03ca7f9ef76a00e47465ce684ce3c56d939a72455934f2", "size_in_bytes": 1458}, {"_path": "Library/include/fst/script/encodemapper-class.h", "path_type": "hardlink", "sha256": "b4346bbfb0b4342aa834fbbc009e6a3b830d6410b64cb4c3da2c7d3e418b1467", "sha256_in_prefix": "b4346bbfb0b4342aa834fbbc009e6a3b830d6410b64cb4c3da2c7d3e418b1467", "size_in_bytes": 9539}, {"_path": "Library/include/fst/script/epsnormalize.h", "path_type": "hardlink", "sha256": "4f8e088fefa6841df27c93c693343757660f01c463ba41ca323062d1e662caf0", "sha256_in_prefix": "4f8e088fefa6841df27c93c693343757660f01c463ba41ca323062d1e662caf0", "size_in_bytes": 1534}, {"_path": "Library/include/fst/script/equal.h", "path_type": "hardlink", "sha256": "c6ae635b5495d8adf5e42447eea72a82e0f095d604dcdeb022cee6d9f6e381e5", "sha256_in_prefix": "c6ae635b5495d8adf5e42447eea72a82e0f095d604dcdeb022cee6d9f6e381e5", "size_in_bytes": 1538}, {"_path": "Library/include/fst/script/equivalent.h", "path_type": "hardlink", "sha256": "5a0248cf97d3a38503272e4eb53961c4a84777a43a5504eff6dccc595c80ce11", "sha256_in_prefix": "5a0248cf97d3a38503272e4eb53961c4a84777a43a5504eff6dccc595c80ce11", "size_in_bytes": 1613}, {"_path": "Library/include/fst/script/fst-class.h", "path_type": "hardlink", "sha256": "28598286f03621000948afc3b4ca9998d53672aeaf625d878747ee8a87845e43", "sha256_in_prefix": "28598286f03621000948afc3b4ca9998d53672aeaf625d878747ee8a87845e43", "size_in_bytes": 23019}, {"_path": "Library/include/fst/script/fstscript-decl.h", "path_type": "hardlink", "sha256": "3727c957b1dd0011fd713697ee649db7fb365eeb3a76749a9c0ef82abf0690ca", "sha256_in_prefix": "3727c957b1dd0011fd713697ee649db7fb365eeb3a76749a9c0ef82abf0690ca", "size_in_bytes": 1379}, {"_path": "Library/include/fst/script/fstscript.h", "path_type": "hardlink", "sha256": "d20aaa5793a0c66fa2d753faef964c20718fd4295282be0013146521a0d76ca9", "sha256_in_prefix": "d20aaa5793a0c66fa2d753faef964c20718fd4295282be0013146521a0d76ca9", "size_in_bytes": 6989}, {"_path": "Library/include/fst/script/getters.h", "path_type": "hardlink", "sha256": "49be835ca241f50c4cd26e41129d013cccf8a82c5ff41150de16de91d591308a", "sha256_in_prefix": "49be835ca241f50c4cd26e41129d013cccf8a82c5ff41150de16de91d591308a", "size_in_bytes": 3712}, {"_path": "Library/include/fst/script/info-impl.h", "path_type": "hardlink", "sha256": "4cffefc7edbc8b95e72df0e238cf9b5fd6717e5b4e3b32a79b1d25d5bb4a5cb7", "sha256_in_prefix": "4cffefc7edbc8b95e72df0e238cf9b5fd6717e5b4e3b32a79b1d25d5bb4a5cb7", "size_in_bytes": 9783}, {"_path": "Library/include/fst/script/info.h", "path_type": "hardlink", "sha256": "ad3af606d0d89f1a0aeaaa92afeed0243c14426d1f5028296228ee20ddf40c10", "sha256_in_prefix": "ad3af606d0d89f1a0aeaaa92afeed0243c14426d1f5028296228ee20ddf40c10", "size_in_bytes": 1651}, {"_path": "Library/include/fst/script/intersect.h", "path_type": "hardlink", "sha256": "f495f03ba8856fc91ef913a5dbdbb87098c06b288525d8559c2b29eb45968042", "sha256_in_prefix": "f495f03ba8856fc91ef913a5dbdbb87098c06b288525d8559c2b29eb45968042", "size_in_bytes": 1729}, {"_path": "Library/include/fst/script/invert.h", "path_type": "hardlink", "sha256": "9f8379d79b4ca78ba9146965d5da4c5a53cf51eb9ce6df398b7644f0050c36ce", "sha256_in_prefix": "9f8379d79b4ca78ba9146965d5da4c5a53cf51eb9ce6df398b7644f0050c36ce", "size_in_bytes": 1150}, {"_path": "Library/include/fst/script/isomorphic.h", "path_type": "hardlink", "sha256": "822c2f72de93a280dc75a87d50160e036f2f219ab9123aee06c9e84bccc7016f", "sha256_in_prefix": "822c2f72de93a280dc75a87d50160e036f2f219ab9123aee06c9e84bccc7016f", "size_in_bytes": 1613}, {"_path": "Library/include/fst/script/map.h", "path_type": "hardlink", "sha256": "896fb768ace48857c34ffd08ec854e4b648100f087725d679b5a2a8a4f731144", "sha256_in_prefix": "896fb768ace48857c34ffd08ec854e4b648100f087725d679b5a2a8a4f731144", "size_in_bytes": 5422}, {"_path": "Library/include/fst/script/minimize.h", "path_type": "hardlink", "sha256": "f3e7b396c9edb403b9dbdff99c8c90bdb55af38b21b4186a45477c918b5655cf", "sha256_in_prefix": "f3e7b396c9edb403b9dbdff99c8c90bdb55af38b21b4186a45477c918b5655cf", "size_in_bytes": 1584}, {"_path": "Library/include/fst/script/print-impl.h", "path_type": "hardlink", "sha256": "8bb72fe3656da75b38de63e55636c93ce140d2cae5e02d13d3b741eb4ce5c7c1", "sha256_in_prefix": "8bb72fe3656da75b38de63e55636c93ce140d2cae5e02d13d3b741eb4ce5c7c1", "size_in_bytes": 4827}, {"_path": "Library/include/fst/script/print.h", "path_type": "hardlink", "sha256": "6f536659fef64af4581f2daae2e6d74dcfe337ac7305f4a1c5bda146278aa567", "sha256_in_prefix": "6f536659fef64af4581f2daae2e6d74dcfe337ac7305f4a1c5bda146278aa567", "size_in_bytes": 3155}, {"_path": "Library/include/fst/script/project.h", "path_type": "hardlink", "sha256": "314bf72d4588e202a0769d8c9624a5e3b18855213877e4cc16820cfd7e21d2a6", "sha256_in_prefix": "314bf72d4588e202a0769d8c9624a5e3b18855213877e4cc16820cfd7e21d2a6", "size_in_bytes": 1335}, {"_path": "Library/include/fst/script/prune.h", "path_type": "hardlink", "sha256": "db4de7993541763be8f175aeb0a92864df0d59c13e296d90457442ca56db8812", "sha256_in_prefix": "db4de7993541763be8f175aeb0a92864df0d59c13e296d90457442ca56db8812", "size_in_bytes": 2725}, {"_path": "Library/include/fst/script/push.h", "path_type": "hardlink", "sha256": "ec5837991be40e4e624f2e574eefd6a2e2b3409556e66df44777557199d31c00", "sha256_in_prefix": "ec5837991be40e4e624f2e574eefd6a2e2b3409556e66df44777557199d31c00", "size_in_bytes": 2328}, {"_path": "Library/include/fst/script/randequivalent.h", "path_type": "hardlink", "sha256": "a2f4458889446f19728e07e3b83b636ecae1c600df78814267e23bff62a0bb55", "sha256_in_prefix": "a2f4458889446f19728e07e3b83b636ecae1c600df78814267e23bff62a0bb55", "size_in_bytes": 3084}, {"_path": "Library/include/fst/script/randgen.h", "path_type": "hardlink", "sha256": "36c3bbb31c7d317b726115a0f6ce36d2d7b10a1907c26ea52a3efd6d9dea9df7", "sha256_in_prefix": "36c3bbb31c7d317b726115a0f6ce36d2d7b10a1907c26ea52a3efd6d9dea9df7", "size_in_bytes": 2702}, {"_path": "Library/include/fst/script/relabel.h", "path_type": "hardlink", "sha256": "95f22e37fe75312a7e583bef2dbc70acdd738acf9c984c791c81e45796dc99a8", "sha256_in_prefix": "95f22e37fe75312a7e583bef2dbc70acdd738acf9c984c791c81e45796dc99a8", "size_in_bytes": 3089}, {"_path": "Library/include/fst/script/replace.h", "path_type": "hardlink", "sha256": "49469976059d77c37037933542a8344eaf9e539add30ef1456abd34131e6896c", "sha256_in_prefix": "49469976059d77c37037933542a8344eaf9e539add30ef1456abd34131e6896c", "size_in_bytes": 3283}, {"_path": "Library/include/fst/script/reverse.h", "path_type": "hardlink", "sha256": "facc0f780f0e6d4e8b82c3bdfadc722362fa9bfd4541e976fd6032fe5ab5c085", "sha256_in_prefix": "facc0f780f0e6d4e8b82c3bdfadc722362fa9bfd4541e976fd6032fe5ab5c085", "size_in_bytes": 1457}, {"_path": "Library/include/fst/script/reweight.h", "path_type": "hardlink", "sha256": "c2fddbea608e09741a3410964289312bb6da8719daa2c0ae0a15bf42b021f65a", "sha256_in_prefix": "c2fddbea608e09741a3410964289312bb6da8719daa2c0ae0a15bf42b021f65a", "size_in_bytes": 1731}, {"_path": "Library/include/fst/script/rmepsilon.h", "path_type": "hardlink", "sha256": "d79118e320366601a5d0339c2da23219330247a6e3b3bb5a701b26f7a58c1425", "sha256_in_prefix": "d79118e320366601a5d0339c2da23219330247a6e3b3bb5a701b26f7a58c1425", "size_in_bytes": 4244}, {"_path": "Library/include/fst/script/script-impl.h", "path_type": "hardlink", "sha256": "a677205fbd5ad395760ddd63228a193c1a0d921f857d87d5613f349c02ff8480", "sha256_in_prefix": "a677205fbd5ad395760ddd63228a193c1a0d921f857d87d5613f349c02ff8480", "size_in_bytes": 8826}, {"_path": "Library/include/fst/script/shortest-distance.h", "path_type": "hardlink", "sha256": "be377ff751ec6734a3b0a56f8031132b0393855853484d8ebcc97434672f683f", "sha256_in_prefix": "be377ff751ec6734a3b0a56f8031132b0393855853484d8ebcc97434672f683f", "size_in_bytes": 8627}, {"_path": "Library/include/fst/script/shortest-path.h", "path_type": "hardlink", "sha256": "31fa22726b3686b533f414933669ee0769cafd35cbfa96306f0900cfeff797b5", "sha256_in_prefix": "31fa22726b3686b533f414933669ee0769cafd35cbfa96306f0900cfeff797b5", "size_in_bytes": 5044}, {"_path": "Library/include/fst/script/stateiterator-class.h", "path_type": "hardlink", "sha256": "8d09f7565b3889b3536684917570c936633736f856e845809c3ca4e5b55569b5", "sha256_in_prefix": "8d09f7565b3889b3536684917570c936633736f856e845809c3ca4e5b55569b5", "size_in_bytes": 2883}, {"_path": "Library/include/fst/script/synchronize.h", "path_type": "hardlink", "sha256": "4f6ef26c18b5b26b229679876155a74568c41f37e229f057c3e071b1d46829ef", "sha256_in_prefix": "4f6ef26c18b5b26b229679876155a74568c41f37e229f057c3e071b1d46829ef", "size_in_bytes": 1421}, {"_path": "Library/include/fst/script/text-io.h", "path_type": "hardlink", "sha256": "388b7e3bf10c50e5c38c37c581542253b324d9bfd2e2db0870920416ac00a304", "sha256_in_prefix": "388b7e3bf10c50e5c38c37c581542253b324d9bfd2e2db0870920416ac00a304", "size_in_bytes": 1473}, {"_path": "Library/include/fst/script/topsort.h", "path_type": "hardlink", "sha256": "1da9895fb150d1295dd7a31ed08706aaaea62759e96ed6268810b8a797106f12", "sha256_in_prefix": "1da9895fb150d1295dd7a31ed08706aaaea62759e96ed6268810b8a797106f12", "size_in_bytes": 1279}, {"_path": "Library/include/fst/script/union.h", "path_type": "hardlink", "sha256": "0e5570f4e45bd5745edf5e489fe034d8b608fd1e3a542ecc34f7f1caa3cd2c81", "sha256_in_prefix": "0e5570f4e45bd5745edf5e489fe034d8b608fd1e3a542ecc34f7f1caa3cd2c81", "size_in_bytes": 1989}, {"_path": "Library/include/fst/script/verify.h", "path_type": "hardlink", "sha256": "cc77709bd4f6045dd9dbb28d065514d8c9989945f2d2454add60cf084a5735a7", "sha256_in_prefix": "cc77709bd4f6045dd9dbb28d065514d8c9989945f2d2454add60cf084a5735a7", "size_in_bytes": 1291}, {"_path": "Library/include/fst/script/weight-class.h", "path_type": "hardlink", "sha256": "4888148c6f2d6a2392142dfcbb8c66d2c6166296b4927e7f85c56baa824157e0", "sha256_in_prefix": "4888148c6f2d6a2392142dfcbb8c66d2c6166296b4927e7f85c56baa824157e0", "size_in_bytes": 8409}, {"_path": "Library/include/fst/set-weight.h", "path_type": "hardlink", "sha256": "d2346cbe1e78b4305cbdb2ed980f81e7707cdffa93de6a5f0b60bc07d8244761", "sha256_in_prefix": "d2346cbe1e78b4305cbdb2ed980f81e7707cdffa93de6a5f0b60bc07d8244761", "size_in_bytes": 19265}, {"_path": "Library/include/fst/shortest-distance.h", "path_type": "hardlink", "sha256": "3a7c0c1e44f671dd9c563265f21fc6c6e08c41e873c798cc93527e3a5d65b62c", "sha256_in_prefix": "3a7c0c1e44f671dd9c563265f21fc6c6e08c41e873c798cc93527e3a5d65b62c", "size_in_bytes": 13952}, {"_path": "Library/include/fst/shortest-path.h", "path_type": "hardlink", "sha256": "1e72ff543ccf781740e3ca9692b29d626f01081542ad5d456a1e61b67de204a5", "sha256_in_prefix": "1e72ff543ccf781740e3ca9692b29d626f01081542ad5d456a1e61b67de204a5", "size_in_bytes": 21985}, {"_path": "Library/include/fst/signed-log-weight.h", "path_type": "hardlink", "sha256": "99bdd7f492f7a21af54a65bebe95a12059c5b93b0ca3cf503f9d72fc9e4b1a95", "sha256_in_prefix": "99bdd7f492f7a21af54a65bebe95a12059c5b93b0ca3cf503f9d72fc9e4b1a95", "size_in_bytes": 19475}, {"_path": "Library/include/fst/sparse-power-weight.h", "path_type": "hardlink", "sha256": "241c55b610ac6bdfdf240332e8bd20b603c90388b643522f43458cf68605113b", "sha256_in_prefix": "241c55b610ac6bdfdf240332e8bd20b603c90388b643522f43458cf68605113b", "size_in_bytes": 7549}, {"_path": "Library/include/fst/sparse-tuple-weight.h", "path_type": "hardlink", "sha256": "6058e67a5a52c2666bf615597d75826b9a7d1b7ff4ac94c5dbc59e9844ec8fdd", "sha256_in_prefix": "6058e67a5a52c2666bf615597d75826b9a7d1b7ff4ac94c5dbc59e9844ec8fdd", "size_in_bytes": 13227}, {"_path": "Library/include/fst/state-map.h", "path_type": "hardlink", "sha256": "010f82d1f6971908a0ec733c482351743c2eeba2d8cc490031a90b4f364f4159", "sha256_in_prefix": "010f82d1f6971908a0ec733c482351743c2eeba2d8cc490031a90b4f364f4159", "size_in_bytes": 19080}, {"_path": "Library/include/fst/state-reachable.h", "path_type": "hardlink", "sha256": "46a9d08310d46caeeff0a0bb89303a8597926b104ea53a213c1a23af8d825ae0", "sha256_in_prefix": "46a9d08310d46caeeff0a0bb89303a8597926b104ea53a213c1a23af8d825ae0", "size_in_bytes": 7770}, {"_path": "Library/include/fst/state-table.h", "path_type": "hardlink", "sha256": "431873c3e910167a54ea5e33716981df0ba304d9832dab9b694418062f072785", "sha256_in_prefix": "431873c3e910167a54ea5e33716981df0ba304d9832dab9b694418062f072785", "size_in_bytes": 17461}, {"_path": "Library/include/fst/statesort.h", "path_type": "hardlink", "sha256": "f5d5dcc0446ca05f899229bc254f41a070ebbf9b461ffeece1cf2c20b8868ca8", "sha256_in_prefix": "f5d5dcc0446ca05f899229bc254f41a070ebbf9b461ffeece1cf2c20b8868ca8", "size_in_bytes": 2807}, {"_path": "Library/include/fst/string-weight.h", "path_type": "hardlink", "sha256": "544574ee8bbf35e00c1bbe439e67f969e93f0a9436d5b02428ef53ffded3fee0", "sha256_in_prefix": "544574ee8bbf35e00c1bbe439e67f969e93f0a9436d5b02428ef53ffded3fee0", "size_in_bytes": 26836}, {"_path": "Library/include/fst/string.h", "path_type": "hardlink", "sha256": "447408d994d4e1cff925522e5accceeae5d4db97fc54871b4fb591b10a5d77bb", "sha256_in_prefix": "447408d994d4e1cff925522e5accceeae5d4db97fc54871b4fb591b10a5d77bb", "size_in_bytes": 12392}, {"_path": "Library/include/fst/symbol-table-ops.h", "path_type": "hardlink", "sha256": "6158de066e4c5824bdb2c027332ab156d3bb475ba29095ea2dc4ead2f26048ae", "sha256_in_prefix": "6158de066e4c5824bdb2c027332ab156d3bb475ba29095ea2dc4ead2f26048ae", "size_in_bytes": 3690}, {"_path": "Library/include/fst/symbol-table.h", "path_type": "hardlink", "sha256": "ecd5e883d493949a0bd43f700dfb206c546a7d0466ba3f2f1612d6bb7ca9779c", "sha256_in_prefix": "ecd5e883d493949a0bd43f700dfb206c546a7d0466ba3f2f1612d6bb7ca9779c", "size_in_bytes": 18805}, {"_path": "Library/include/fst/synchronize.h", "path_type": "hardlink", "sha256": "7413adc0cd8922fe1d23a3c5efdaf5f782eb1de6b83efcbf0e1ec27eaa1eee9d", "sha256_in_prefix": "7413adc0cd8922fe1d23a3c5efdaf5f782eb1de6b83efcbf0e1ec27eaa1eee9d", "size_in_bytes": 13611}, {"_path": "Library/include/fst/test-properties.h", "path_type": "hardlink", "sha256": "f8d5c61cdc3f67a8d306c942ad43b230fcac7c21826ea47d692ba122b4ee35f1", "sha256_in_prefix": "f8d5c61cdc3f67a8d306c942ad43b230fcac7c21826ea47d692ba122b4ee35f1", "size_in_bytes": 9222}, {"_path": "Library/include/fst/test/algo_test.h", "path_type": "hardlink", "sha256": "491b0691229bef70b28d45001d939659260e9b19203cd3da75c7710233bc4295", "sha256_in_prefix": "491b0691229bef70b28d45001d939659260e9b19203cd3da75c7710233bc4295", "size_in_bytes": 42244}, {"_path": "Library/include/fst/test/compactors.h", "path_type": "hardlink", "sha256": "e59cfa4a6593653a49306b0cc7b161b30156308ddce5e6d48c110230157b3b7e", "sha256_in_prefix": "e59cfa4a6593653a49306b0cc7b161b30156308ddce5e6d48c110230157b3b7e", "size_in_bytes": 5136}, {"_path": "Library/include/fst/test/fst_test.h", "path_type": "hardlink", "sha256": "3b47531312b7a47e234a378de89006fa958d83eee3f638a50ba6ec73866a373b", "sha256_in_prefix": "3b47531312b7a47e234a378de89006fa958d83eee3f638a50ba6ec73866a373b", "size_in_bytes": 10275}, {"_path": "Library/include/fst/test/rand-fst.h", "path_type": "hardlink", "sha256": "581073d23108302aae8b1089aae275bb7abf5bcbceb1fd09c7aff47e5866e8ec", "sha256_in_prefix": "581073d23108302aae8b1089aae275bb7abf5bcbceb1fd09c7aff47e5866e8ec", "size_in_bytes": 3512}, {"_path": "Library/include/fst/test/weight-tester.h", "path_type": "hardlink", "sha256": "ec771f2eadd27d6d1b5d6619238e2e6e7a8db99d579baef5a3860b510b2e03d9", "sha256_in_prefix": "ec771f2eadd27d6d1b5d6619238e2e6e7a8db99d579baef5a3860b510b2e03d9", "size_in_bytes": 7434}, {"_path": "Library/include/fst/topsort.h", "path_type": "hardlink", "sha256": "4442530db7f17e4211706f2f99bcd6ec3fe8ef6bdf0f5a52c3d1d0e5a9814c14", "sha256_in_prefix": "4442530db7f17e4211706f2f99bcd6ec3fe8ef6bdf0f5a52c3d1d0e5a9814c14", "size_in_bytes": 3086}, {"_path": "Library/include/fst/tuple-weight.h", "path_type": "hardlink", "sha256": "c7a70f31f4e153402e28117834315a72faffa36874b77c99ed8dbe84170d4b9e", "sha256_in_prefix": "c7a70f31f4e153402e28117834315a72faffa36874b77c99ed8dbe84170d4b9e", "size_in_bytes": 4733}, {"_path": "Library/include/fst/union-find.h", "path_type": "hardlink", "sha256": "88c46e88358fbbd4d0e0f7c42aefa55549d4197d611ac00644e20d71f7d83b94", "sha256_in_prefix": "88c46e88358fbbd4d0e0f7c42aefa55549d4197d611ac00644e20d71f7d83b94", "size_in_bytes": 3237}, {"_path": "Library/include/fst/union-weight.h", "path_type": "hardlink", "sha256": "69f935213c8437d6cf83ef579940b0449eb62fc346a18a24617a5c7c8368e874", "sha256_in_prefix": "69f935213c8437d6cf83ef579940b0449eb62fc346a18a24617a5c7c8368e874", "size_in_bytes": 15343}, {"_path": "Library/include/fst/union.h", "path_type": "hardlink", "sha256": "7def08dd1eed447c1c67a37c0d16ab41e3b088aa4dbe701f63f3c4406ed39597", "sha256_in_prefix": "7def08dd1eed447c1c67a37c0d16ab41e3b088aa4dbe701f63f3c4406ed39597", "size_in_bytes": 6033}, {"_path": "Library/include/fst/util.h", "path_type": "hardlink", "sha256": "5f03d86cc790054172abf1ca1534cf577ba06d46b5ede79def8fe745277a9542", "sha256_in_prefix": "5f03d86cc790054172abf1ca1534cf577ba06d46b5ede79def8fe745277a9542", "size_in_bytes": 14808}, {"_path": "Library/include/fst/vector-fst.h", "path_type": "hardlink", "sha256": "3ef0d2e6154b6b37ffb84e15a5f2c39f76729cb2026f86799d2cd100f92090c5", "sha256_in_prefix": "3ef0d2e6154b6b37ffb84e15a5f2c39f76729cb2026f86799d2cd100f92090c5", "size_in_bytes": 25478}, {"_path": "Library/include/fst/verify.h", "path_type": "hardlink", "sha256": "301d078fc2c5d1ea6a039cf0638d6945078d5baff1bc5d2a3b941b8b87f650d1", "sha256_in_prefix": "301d078fc2c5d1ea6a039cf0638d6945078d5baff1bc5d2a3b941b8b87f650d1", "size_in_bytes": 4188}, {"_path": "Library/include/fst/visit.h", "path_type": "hardlink", "sha256": "f3a4d7c310ae32217a43f6b266b845a3407adeb3486ae217ba50571416c76df6", "sha256_in_prefix": "f3a4d7c310ae32217a43f6b266b845a3407adeb3486ae217ba50571416c76df6", "size_in_bytes": 9971}, {"_path": "Library/include/fst/weight.h", "path_type": "hardlink", "sha256": "b8cb7f1a5ebcf125dddf49cdf988129163e8b8b33814af1764a47e430166b6c3", "sha256_in_prefix": "b8cb7f1a5ebcf125dddf49cdf988129163e8b8b33814af1764a47e430166b6c3", "size_in_bytes": 13804}, {"_path": "Library/include/fst/windows_defs.inc", "path_type": "hardlink", "sha256": "bc2105e5ff199a51845212897dbf74cc9ebb37a103bbc9e3102962f477afde35", "sha256_in_prefix": "bc2105e5ff199a51845212897dbf74cc9ebb37a103bbc9e3102962f477afde35", "size_in_bytes": 754}, {"_path": "Library/lib/fst.lib", "path_type": "hardlink", "sha256": "e136b7d7f8734079c2cd5c7ee504b3159ed77fa42eab29c2be54bb4f436c1a86", "sha256_in_prefix": "e136b7d7f8734079c2cd5c7ee504b3159ed77fa42eab29c2be54bb4f436c1a86", "size_in_bytes": 98090}, {"_path": "Library/lib/fstcompressscript.lib", "path_type": "hardlink", "sha256": "d7c02b739c9f3c6571c882ad49c1d4459270340f1f7aa0e4be5b22fedcecc0ce", "sha256_in_prefix": "d7c02b739c9f3c6571c882ad49c1d4459270340f1f7aa0e4be5b22fedcecc0ce", "size_in_bytes": 3134}, {"_path": "Library/lib/fstfar.lib", "path_type": "hardlink", "sha256": "f7d99d75147ddc0774caef722e604caa94426a399e79d9bcb5b1c82f3ba1f36a", "sha256_in_prefix": "f7d99d75147ddc0774caef722e604caa94426a399e79d9bcb5b1c82f3ba1f36a", "size_in_bytes": 2640}, {"_path": "Library/lib/fstfarscript.lib", "path_type": "hardlink", "sha256": "4bffd6fab22cd046f2bdfe6c83f4ef99449a606d9d035bd1034932a7d323c92b", "sha256_in_prefix": "4bffd6fab22cd046f2bdfe6c83f4ef99449a606d9d035bd1034932a7d323c92b", "size_in_bytes": 35894}, {"_path": "Library/lib/fstlinearscript.lib", "path_type": "hardlink", "sha256": "a2ded0dd378d584d6fc5667312de36392cffabf05ac8fccbdd6fb8d4c073446d", "sha256_in_prefix": "a2ded0dd378d584d6fc5667312de36392cffabf05ac8fccbdd6fb8d4c073446d", "size_in_bytes": 6562}, {"_path": "Library/lib/fstmpdtscript.lib", "path_type": "hardlink", "sha256": "5047fe1170e038636c9937a4fcdc4ffc8a5556f4cd5527e9a1c49775a8b02a85", "sha256_in_prefix": "5047fe1170e038636c9937a4fcdc4ffc8a5556f4cd5527e9a1c49775a8b02a85", "size_in_bytes": 5830}, {"_path": "Library/lib/fstngram.lib", "path_type": "hardlink", "sha256": "2b2905109ef2247e2dcd7777be70037ffa780317ebd6255e26afda18f53df214", "sha256_in_prefix": "2b2905109ef2247e2dcd7777be70037ffa780317ebd6255e26afda18f53df214", "size_in_bytes": 13140}, {"_path": "Library/lib/fstpdtscript.lib", "path_type": "hardlink", "sha256": "c5e8e8e08a0c14406c1dd22ed9cce0dd5a5f665a5361632fa2c96732d1557425", "sha256_in_prefix": "c5e8e8e08a0c14406c1dd22ed9cce0dd5a5f665a5361632fa2c96732d1557425", "size_in_bytes": 10016}, {"_path": "Library/lib/fstscript.lib", "path_type": "hardlink", "sha256": "c16cb894cb4c176fcfb4800d730baf7a27df1255e849101830de7c2e2e9e3ae1", "sha256_in_prefix": "c16cb894cb4c176fcfb4800d730baf7a27df1255e849101830de7c2e2e9e3ae1", "size_in_bytes": 135698}, {"_path": "Library/lib/fstspecial.lib", "path_type": "hardlink", "sha256": "56e47ca8ae6265d1494beb1d99a842770b86ceaaa5c77124d27d54ed9bbf5ca2", "sha256_in_prefix": "56e47ca8ae6265d1494beb1d99a842770b86ceaaa5c77124d27d54ed9bbf5ca2", "size_in_bytes": 3712}], "paths_version": 1}, "requested_spec": "None", "sha256": "96a8adb79e9c9069cbcc19b1514536b87925573bc57eec9dfb463576d1be214b", "size": 4414368, "subdir": "win-64", "timestamp": 1659886094000, "url": "https://conda.anaconda.org/conda-forge/win-64/openfst-1.8.2-h91493d7_2.tar.bz2", "version": "1.8.2"}