{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libnvjpeg-12.3.1.117-hd77b12b_1", "files": ["Library/bin/nvjpeg64_12.dll"], "fn": "libnvjpeg-12.3.1.117-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libnvjpeg-12.3.1.117-hd77b12b_1", "type": 1}, "md5": "dfd0934b68e94803f33c96a82dc5404c", "name": "libnvjpeg", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libnvjpeg-12.3.1.117-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/bin/nvjpeg64_12.dll", "path_type": "hardlink", "sha256": "ae60b58ff90f87e2b5002c6ddc3b7eef0538e0537a9011042303e646175ce7af", "sha256_in_prefix": "ae60b58ff90f87e2b5002c6ddc3b7eef0538e0537a9011042303e646175ce7af", "size_in_bytes": 4913152}], "paths_version": 1}, "requested_spec": "None", "sha256": "b8f59dd9b71d8978f52883c2f6f65ac98b3a903ce0124d4ab9176f3dc9019861", "size": 2118416, "subdir": "win-64", "timestamp": 1715371701000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libnvjpeg-12.3.1.117-hd77b12b_1.conda", "version": "12.3.1.117"}