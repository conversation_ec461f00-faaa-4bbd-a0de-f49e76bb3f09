{"build": "hcfcfb64_2", "build_number": 2, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": [], "depends": ["ucrt >=10.0.20348.0", "vc >=14.2,<15", "vc14_runtime >=14.29.30139"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libiconv-1.17-hcfcfb64_2", "files": ["Library/bin/charset.dll", "Library/bin/iconv.dll", "Library/include/iconv.h", "Library/include/libcharset.h", "Library/include/localcharset.h", "Library/lib/charset.lib", "Library/lib/iconv.lib"], "fn": "libiconv-1.17-hcfcfb64_2.conda", "license": "LGPL-2.1-only", "link": {"source": "D:\\anaconda3\\pkgs\\libiconv-1.17-hcfcfb64_2", "type": 1}, "md5": "e1eb10b1cca179f2baa3601e4efc8712", "name": "libiconv", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libiconv-1.17-hcfcfb64_2.conda", "paths_data": {"paths": [{"_path": "Library/bin/charset.dll", "path_type": "hardlink", "sha256": "8fd754b8284bf99cac2569cc3358c970103a5dc3f9eb9dde81046c0754ebd75f", "sha256_in_prefix": "8fd754b8284bf99cac2569cc3358c970103a5dc3f9eb9dde81046c0754ebd75f", "size_in_bytes": 11776}, {"_path": "Library/bin/iconv.dll", "path_type": "hardlink", "sha256": "cb108fa99613c60b732221c6edf9a7ee0484c2758c63252708969616912c256c", "sha256_in_prefix": "cb108fa99613c60b732221c6edf9a7ee0484c2758c63252708969616912c256c", "size_in_bytes": 937472}, {"_path": "Library/include/iconv.h", "path_type": "hardlink", "sha256": "bc0fd9f7bfdb4e52780603a245de85ba99969593e368174e6ae4da5d26bc839b", "sha256_in_prefix": "bc0fd9f7bfdb4e52780603a245de85ba99969593e368174e6ae4da5d26bc839b", "size_in_bytes": 9935}, {"_path": "Library/include/libcharset.h", "path_type": "hardlink", "sha256": "f0fd9ef4735c0c47f957c7b831b3bb5bf65f70859d7d14706e17e4e60d5f4ed0", "sha256_in_prefix": "f0fd9ef4735c0c47f957c7b831b3bb5bf65f70859d7d14706e17e4e60d5f4ed0", "size_in_bytes": 1555}, {"_path": "Library/include/localcharset.h", "path_type": "hardlink", "sha256": "862bf4adfc78181b6b1c2272d7c27939c356bf011919368f7034e4e1a7ea1724", "sha256_in_prefix": "862bf4adfc78181b6b1c2272d7c27939c356bf011919368f7034e4e1a7ea1724", "size_in_bytes": 6350}, {"_path": "Library/lib/charset.lib", "path_type": "hardlink", "sha256": "158cf5a25b38c8d67bb45327957ea9e707972e26cbd99b198bb293d6080a29c2", "sha256_in_prefix": "158cf5a25b38c8d67bb45327957ea9e707972e26cbd99b198bb293d6080a29c2", "size_in_bytes": 2524}, {"_path": "Library/lib/iconv.lib", "path_type": "hardlink", "sha256": "56bc2696e019aca379a68b44349cf872080b4829c4f79c12e84ec3a5fd003810", "sha256_in_prefix": "56bc2696e019aca379a68b44349cf872080b4829c4f79c12e84ec3a5fd003810", "size_in_bytes": 3666}], "paths_version": 1}, "requested_spec": "None", "sha256": "5f844dd19b046d43174ad80c6ea75b5d504020e3b63cfbc4ace97b8730d35c7b", "size": 636146, "subdir": "win-64", "timestamp": 1702682547000, "url": "https://conda.anaconda.org/conda-forge/win-64/libiconv-1.17-hcfcfb64_2.conda", "version": "1.17"}