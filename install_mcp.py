#!/usr/bin/env python3
"""
安装脚本：为BEN2项目安装支持MCP的Gradio版本
"""

import subprocess
import sys
import importlib.util

def check_gradio_version():
    """检查当前Gradio版本是否支持MCP"""
    try:
        import gradio as gr
        version = gr.__version__
        print(f"当前Gradio版本：{version}")
        
        # 检查是否支持MCP
        try:
            # 尝试导入MCP相关模块
            from gradio import Interface
            interface = Interface(lambda x: x, "text", "text")
            # 检查launch方法是否支持mcp_server参数
            import inspect
            sig = inspect.signature(interface.launch)
            if 'mcp_server' in sig.parameters:
                print("✅ 当前Gradio版本支持MCP功能")
                return True
            else:
                print("❌ 当前Gradio版本不支持MCP功能")
                return False
        except Exception as e:
            print(f"❌ 检查MCP支持时出错：{e}")
            return False
            
    except ImportError:
        print("❌ 未安装Gradio")
        return False

def install_all_dependencies():
    """安装所有必需的依赖"""
    print("正在安装所有必需的依赖...")

    dependencies = [
        "spaces",
        "loadimg",
        "einops",
        "timm",
        "gradio[mcp]"
    ]

    try:
        for dep in dependencies:
            print(f"安装 {dep}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])

        # 尝试解决wandb冲突
        print("尝试修复wandb依赖冲突...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "wandb"])
        except:
            print("wandb升级失败，但不影响主要功能")

        print("✅ 所有依赖安装完成！")
        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败：{e}")
        return False

def main():
    print("BEN2 MCP安装工具")
    print("=" * 50)
    
    # 检查当前版本
    if check_gradio_version():
        print("\n✅ 您的Gradio版本已支持MCP功能，无需更新。")
        return
    
    # 询问是否要安装
    response = input("\n是否要安装所有必需的依赖（包括MCP支持）？(y/n): ").lower().strip()

    if response in ['y', 'yes', '是', '确定']:
        if install_all_dependencies():
            print("\n🎉 安装成功！现在您可以使用MCP功能了。")
            print("运行 'python app.py' 启动带有MCP支持的BEN2服务器。")
            print("\n如果仍有错误，请查看MCP_USAGE.md中的故障排除部分。")
        else:
            print("\n❌ 安装失败。请手动运行：")
            print("pip install spaces loadimg einops timm 'gradio[mcp]'")
    else:
        print("\n取消安装。请手动安装必需的依赖。")

if __name__ == "__main__":
    main()
