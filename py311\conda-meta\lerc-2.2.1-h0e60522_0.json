{"build": "h0e60522_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\lerc-2.2.1-h0e60522_0", "files": ["Library/bin/Lerc.dll", "Library/include/Lerc_c_api.h", "Library/include/Lerc_types.h", "Library/lib/Lerc.lib"], "fn": "lerc-2.2.1-h0e60522_0.tar.bz2", "license": "Apache-2.0", "link": {"source": "D:\\anaconda3\\pkgs\\lerc-2.2.1-h0e60522_0", "type": 1}, "md5": "5ea14f204e4caaea47598f719adbf80b", "name": "lerc", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\lerc-2.2.1-h0e60522_0.tar.bz2", "paths_data": {"paths": [{"_path": "Library/bin/Lerc.dll", "path_type": "hardlink", "sha256": "a7c3754a8cdcbfa894d01e38a58f4ea86db692afc2bd645c3b47e06fdf74f5bd", "sha256_in_prefix": "a7c3754a8cdcbfa894d01e38a58f4ea86db692afc2bd645c3b47e06fdf74f5bd", "size_in_bytes": 367616}, {"_path": "Library/include/Lerc_c_api.h", "path_type": "hardlink", "sha256": "718ad9269a364cfd77b5bb10712cb8eb636c080eb6c378890257c570f4bf4e1e", "sha256_in_prefix": "718ad9269a364cfd77b5bb10712cb8eb636c080eb6c378890257c570f4bf4e1e", "size_in_bytes": 10071}, {"_path": "Library/include/Lerc_types.h", "path_type": "hardlink", "sha256": "3505e9cf0152d830602cd1b3d6706b21a140a5b145e20367bcb5490cd1444e3f", "sha256_in_prefix": "3505e9cf0152d830602cd1b3d6706b21a140a5b145e20367bcb5490cd1444e3f", "size_in_bytes": 822}, {"_path": "Library/lib/Lerc.lib", "path_type": "hardlink", "sha256": "3685dca4794c1cd85a940d41279662f4702ec09c0c8310858bf7ad45170937e1", "sha256_in_prefix": "3685dca4794c1cd85a940d41279662f4702ec09c0c8310858bf7ad45170937e1", "size_in_bytes": 3062}], "paths_version": 1}, "requested_spec": "None", "sha256": "ff0736ff8c386b6a2ba8c77778915cb42ff28f8fb414de16bad50d16eafaca88", "size": 136662, "subdir": "win-64", "timestamp": 1609193445000, "url": "https://conda.anaconda.org/conda-forge/win-64/lerc-2.2.1-h0e60522_0.tar.bz2", "version": "2.2.1"}