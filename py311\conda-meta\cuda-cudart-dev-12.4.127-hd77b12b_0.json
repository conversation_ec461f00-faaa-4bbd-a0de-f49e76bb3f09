{"build": "hd77b12b_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0", "cuda-cudart 12.4.127 hd77b12b_0", "cuda-cudart-dev_win-64 12.4.127 hd77b12b_0", "cuda-cudart-static 12.4.127 hd77b12b_0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-cudart-dev-12.4.127-hd77b12b_0", "files": [], "fn": "cuda-cudart-dev-12.4.127-hd77b12b_0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-cudart-dev-12.4.127-hd77b12b_0", "type": 1}, "md5": "698837c8b8bb832e0c8d53d4bf1ae9ed", "name": "cuda-cudart-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-cudart-dev-12.4.127-hd77b12b_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "3e095b572172b93eae780c5530f834a26c94b15472c3bac6c4186cccdc719196", "size": 21735, "subdir": "win-64", "timestamp": 1714768751000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-cudart-dev-12.4.127-hd77b12b_0.conda", "version": "12.4.127"}