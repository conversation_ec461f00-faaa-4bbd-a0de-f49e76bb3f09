{"build": "py311h005e61a_6", "build_number": 6, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": [], "depends": ["dlfcn-win32 >=1.3.0,<2.0a0", "graphviz", "openfst 1.8.2", "openfst >=1.8.2,<1.8.3.0a0", "python >=3.11,<3.12.0a0", "python_abi 3.11.* *_cp311", "ucrt >=10.0.20348.0", "vc >=14.2,<15", "vc14_runtime >=14.29.30139"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\pynini-2.1.5-py311h005e61a_6", "files": ["Lib/site-packages/_pynini.cp311-win_amd64.pyd", "Lib/site-packages/_pywrapfst.cp311-win_amd64.pyd", "Lib/site-packages/pynini-2.1.5.dist-info/AUTHORS", "Lib/site-packages/pynini-2.1.5.dist-info/INSTALLER", "Lib/site-packages/pynini-2.1.5.dist-info/LICENSE", "Lib/site-packages/pynini-2.1.5.dist-info/METADATA", "Lib/site-packages/pynini-2.1.5.dist-info/RECORD", "Lib/site-packages/pynini-2.1.5.dist-info/REQUESTED", "Lib/site-packages/pynini-2.1.5.dist-info/WHEEL", "Lib/site-packages/pynini-2.1.5.dist-info/direct_url.json", "Lib/site-packages/pynini-2.1.5.dist-info/top_level.txt", "Lib/site-packages/pynini/__init__.py", "Lib/site-packages/pynini/__init__.pyi", "Lib/site-packages/pynini/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/pynini/examples/__init__.py", "Lib/site-packages/pynini/examples/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/pynini/examples/__pycache__/case.cpython-311.pyc", "Lib/site-packages/pynini/examples/__pycache__/chatspeak.cpython-311.pyc", "Lib/site-packages/pynini/examples/__pycache__/chatspeak_model.cpython-311.pyc", "Lib/site-packages/pynini/examples/__pycache__/dates.cpython-311.pyc", "Lib/site-packages/pynini/examples/__pycache__/g2p.cpython-311.pyc", "Lib/site-packages/pynini/examples/__pycache__/numbers.cpython-311.pyc", "Lib/site-packages/pynini/examples/__pycache__/plurals.cpython-311.pyc", "Lib/site-packages/pynini/examples/__pycache__/t9.cpython-311.pyc", "Lib/site-packages/pynini/examples/__pycache__/weather.cpython-311.pyc", "Lib/site-packages/pynini/examples/case.py", "Lib/site-packages/pynini/examples/chatspeak.py", "Lib/site-packages/pynini/examples/chatspeak_model.py", "Lib/site-packages/pynini/examples/dates.py", "Lib/site-packages/pynini/examples/g2p.py", "Lib/site-packages/pynini/examples/numbers.py", "Lib/site-packages/pynini/examples/plurals.py", "Lib/site-packages/pynini/examples/py.typed", "Lib/site-packages/pynini/examples/t9.py", "Lib/site-packages/pynini/examples/weather.py", "Lib/site-packages/pynini/export/__init__.py", "Lib/site-packages/pynini/export/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/pynini/export/__pycache__/export.cpython-311.pyc", "Lib/site-packages/pynini/export/__pycache__/grm.cpython-311.pyc", "Lib/site-packages/pynini/export/__pycache__/grm_example.cpython-311.pyc", "Lib/site-packages/pynini/export/__pycache__/multi_grm.cpython-311.pyc", "Lib/site-packages/pynini/export/__pycache__/multi_grm_example.cpython-311.pyc", "Lib/site-packages/pynini/export/export.py", "Lib/site-packages/pynini/export/grm.py", "Lib/site-packages/pynini/export/grm_example.py", "Lib/site-packages/pynini/export/multi_grm.py", "Lib/site-packages/pynini/export/multi_grm_example.py", "Lib/site-packages/pynini/export/py.typed", "Lib/site-packages/pynini/lib/__init__.py", "Lib/site-packages/pynini/lib/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/pynini/lib/__pycache__/byte.cpython-311.pyc", "Lib/site-packages/pynini/lib/__pycache__/edit_transducer.cpython-311.pyc", "Lib/site-packages/pynini/lib/__pycache__/features.cpython-311.pyc", "Lib/site-packages/pynini/lib/__pycache__/paradigms.cpython-311.pyc", "Lib/site-packages/pynini/lib/__pycache__/pynutil.cpython-311.pyc", "Lib/site-packages/pynini/lib/__pycache__/rewrite.cpython-311.pyc", "Lib/site-packages/pynini/lib/__pycache__/rule_cascade.cpython-311.pyc", "Lib/site-packages/pynini/lib/__pycache__/tagger.cpython-311.pyc", "Lib/site-packages/pynini/lib/__pycache__/utf8.cpython-311.pyc", "Lib/site-packages/pynini/lib/byte.py", "Lib/site-packages/pynini/lib/edit_transducer.py", "Lib/site-packages/pynini/lib/features.py", "Lib/site-packages/pynini/lib/paradigms.py", "Lib/site-packages/pynini/lib/py.typed", "Lib/site-packages/pynini/lib/pynutil.py", "Lib/site-packages/pynini/lib/rewrite.py", "Lib/site-packages/pynini/lib/rule_cascade.py", "Lib/site-packages/pynini/lib/tagger.py", "Lib/site-packages/pynini/lib/utf8.py", "Lib/site-packages/pynini/py.typed", "Lib/site-packages/pywrapfst/__init__.py", "Lib/site-packages/pywrapfst/__init__.pyi", "Lib/site-packages/pywrapfst/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/pywrapfst/py.typed", "Library/include/_pywrapfst.h"], "fn": "pynini-2.1.5-py311h005e61a_6.conda", "license": "Apache-2.0", "link": {"source": "D:\\anaconda3\\pkgs\\pynini-2.1.5-py311h005e61a_6", "type": 1}, "md5": "b340c15c8ed55385d903c2d15df6d8c8", "name": "p<PERSON><PERSON>", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\pynini-2.1.5-py311h005e61a_6.conda", "paths_data": {"paths": [{"_path": "Lib/site-packages/_pynini.cp311-win_amd64.pyd", "path_type": "hardlink", "sha256": "83dcfafbfffeb88d0acae3edeaeccb2c4340d5c97e04eca15939cb60f887f9d7", "sha256_in_prefix": "83dcfafbfffeb88d0acae3edeaeccb2c4340d5c97e04eca15939cb60f887f9d7", "size_in_bytes": 2620416}, {"_path": "Lib/site-packages/_pywrapfst.cp311-win_amd64.pyd", "path_type": "hardlink", "sha256": "f9a162bdef7946395c6a734885619fec124fe3896c54e03c46aac29ab1c27b30", "sha256_in_prefix": "f9a162bdef7946395c6a734885619fec124fe3896c54e03c46aac29ab1c27b30", "size_in_bytes": 587776}, {"_path": "Lib/site-packages/pynini-2.1.5.dist-info/AUTHORS", "path_type": "hardlink", "sha256": "627c695f80fdac6412d66e6ad4160a4acb41923ca7ee3fd7e112e105f0ff46b9", "sha256_in_prefix": "627c695f80fdac6412d66e6ad4160a4acb41923ca7ee3fd7e112e105f0ff46b9", "size_in_bytes": 12}, {"_path": "Lib/site-packages/pynini-2.1.5.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/pynini-2.1.5.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "sha256_in_prefix": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "size_in_bytes": 11358}, {"_path": "Lib/site-packages/pynini-2.1.5.dist-info/METADATA", "path_type": "hardlink", "sha256": "34bf7d50a41df259e6a4dfd81613bdc8dee1441746ee5dba4bad673a44da45fe", "sha256_in_prefix": "34bf7d50a41df259e6a4dfd81613bdc8dee1441746ee5dba4bad673a44da45fe", "size_in_bytes": 4699}, {"_path": "Lib/site-packages/pynini-2.1.5.dist-info/RECORD", "path_type": "hardlink", "sha256": "7bd6e6aae3d483ffd77fcae24bb3c91bb88f2071976f97247cb459e63e95a6c6", "sha256_in_prefix": "7bd6e6aae3d483ffd77fcae24bb3c91bb88f2071976f97247cb459e63e95a6c6", "size_in_bytes": 5219}, {"_path": "Lib/site-packages/pynini-2.1.5.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pynini-2.1.5.dist-info/WHEEL", "path_type": "hardlink", "sha256": "6da76f352fb2f5f12ad17faacdd658bea97f2458c8ed77f0fb047c16c8e82b42", "sha256_in_prefix": "6da76f352fb2f5f12ad17faacdd658bea97f2458c8ed77f0fb047c16c8e82b42", "size_in_bytes": 102}, {"_path": "Lib/site-packages/pynini-2.1.5.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "fe844570c334f1643288cbe08a39a17c0e5410e125bdbf5a52a42f2ceea26323", "sha256_in_prefix": "fe844570c334f1643288cbe08a39a17c0e5410e125bdbf5a52a42f2ceea26323", "size_in_bytes": 67}, {"_path": "Lib/site-packages/pynini-2.1.5.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "5f4da728fd3603821bc65e4b634137314d2387851b5713c3e9978a9c6404fbdf", "sha256_in_prefix": "5f4da728fd3603821bc65e4b634137314d2387851b5713c3e9978a9c6404fbdf", "size_in_bytes": 36}, {"_path": "Lib/site-packages/pynini/__init__.py", "path_type": "hardlink", "sha256": "503e55c387d30923c8dacfb9fa381b5c8184d9c1be3e73f113e72cd210b7863d", "sha256_in_prefix": "503e55c387d30923c8dacfb9fa381b5c8184d9c1be3e73f113e72cd210b7863d", "size_in_bytes": 79}, {"_path": "Lib/site-packages/pynini/__init__.pyi", "path_type": "hardlink", "sha256": "ff0fe8611706106ebb6cc5ab1bd16c7db7d6eab089b8de8144aacf41ceda19fb", "sha256_in_prefix": "ff0fe8611706106ebb6cc5ab1bd16c7db7d6eab089b8de8144aacf41ceda19fb", "size_in_bytes": 19348}, {"_path": "Lib/site-packages/pynini/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "09d50d2be79d86380170494665d03afedf6f9f4749789792763c262d071b6a40", "sha256_in_prefix": "09d50d2be79d86380170494665d03afedf6f9f4749789792763c262d071b6a40", "size_in_bytes": 206}, {"_path": "Lib/site-packages/pynini/examples/__init__.py", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "20a28104d2d43396d5ce29d97590916e2d5f37c595468940bcdcaeb0008edfb3", "sha256_in_prefix": "20a28104d2d43396d5ce29d97590916e2d5f37c595468940bcdcaeb0008edfb3", "size_in_bytes": 154}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/case.cpython-311.pyc", "path_type": "hardlink", "sha256": "2ead03a8361ffdb78557f2b3b69f6d2a64edfbea4a7743146ad63cd957d327c9", "sha256_in_prefix": "2ead03a8361ffdb78557f2b3b69f6d2a64edfbea4a7743146ad63cd957d327c9", "size_in_bytes": 3613}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/chatspeak.cpython-311.pyc", "path_type": "hardlink", "sha256": "ee3d10b1086a7653cdfe321ff4ecd870e310464c06082820a9a5627efd06a690", "sha256_in_prefix": "ee3d10b1086a7653cdfe321ff4ecd870e310464c06082820a9a5627efd06a690", "size_in_bytes": 17102}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/chatspeak_model.cpython-311.pyc", "path_type": "hardlink", "sha256": "0e7491a38bf0921648b6b7c9a2ff3e97ec8fe35ffcb524b1fba7266f55899ee3", "sha256_in_prefix": "0e7491a38bf0921648b6b7c9a2ff3e97ec8fe35ffcb524b1fba7266f55899ee3", "size_in_bytes": 4506}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/dates.cpython-311.pyc", "path_type": "hardlink", "sha256": "dda8563d32e17adeca5e0bc4af9a1a8726235db491e5bc82e2f449bf5787ea54", "sha256_in_prefix": "dda8563d32e17adeca5e0bc4af9a1a8726235db491e5bc82e2f449bf5787ea54", "size_in_bytes": 8657}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/g2p.cpython-311.pyc", "path_type": "hardlink", "sha256": "288655477da498e592685be31b93ada2c7c6099471b8f113eec1eb0338c0e9da", "sha256_in_prefix": "288655477da498e592685be31b93ada2c7c6099471b8f113eec1eb0338c0e9da", "size_in_bytes": 2925}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/numbers.cpython-311.pyc", "path_type": "hardlink", "sha256": "b12c56dda826cc7882d7064ebb63acf53eb016db5d1f863190524f0ac42e944b", "sha256_in_prefix": "b12c56dda826cc7882d7064ebb63acf53eb016db5d1f863190524f0ac42e944b", "size_in_bytes": 3686}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/plurals.cpython-311.pyc", "path_type": "hardlink", "sha256": "ab1056d52b6f10f28ce6ff794555ed2ab794329c6db22877ff993a8f292f3931", "sha256_in_prefix": "ab1056d52b6f10f28ce6ff794555ed2ab794329c6db22877ff993a8f292f3931", "size_in_bytes": 2343}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/t9.cpython-311.pyc", "path_type": "hardlink", "sha256": "8f46262858e2260cacd908166b936bf8867e02b0a816b27f5838df495115bdab", "sha256_in_prefix": "8f46262858e2260cacd908166b936bf8867e02b0a816b27f5838df495115bdab", "size_in_bytes": 3284}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/weather.cpython-311.pyc", "path_type": "hardlink", "sha256": "02706482093801a7d4e67e2d2ce9e1012029f03e064af7b10effb93cab7bf3a6", "sha256_in_prefix": "02706482093801a7d4e67e2d2ce9e1012029f03e064af7b10effb93cab7bf3a6", "size_in_bytes": 4690}, {"_path": "Lib/site-packages/pynini/examples/case.py", "path_type": "hardlink", "sha256": "fa1efa735956f8f188c4dfba9668e800d141cf5e452e8fd34b381059e271d94f", "sha256_in_prefix": "fa1efa735956f8f188c4dfba9668e800d141cf5e452e8fd34b381059e271d94f", "size_in_bytes": 2729}, {"_path": "Lib/site-packages/pynini/examples/chatspeak.py", "path_type": "hardlink", "sha256": "c9ad39150cb613caf05c6de4b11dc363b807d2ff521bb9937e6d8b014ff0385e", "sha256_in_prefix": "c9ad39150cb613caf05c6de4b11dc363b807d2ff521bb9937e6d8b014ff0385e", "size_in_bytes": 9293}, {"_path": "Lib/site-packages/pynini/examples/chatspeak_model.py", "path_type": "hardlink", "sha256": "ce0bed0c7dcf73de2d14c02ec10ce62ed77054879f04bfacc3b62a3ce0a31dec", "sha256_in_prefix": "ce0bed0c7dcf73de2d14c02ec10ce62ed77054879f04bfacc3b62a3ce0a31dec", "size_in_bytes": 3066}, {"_path": "Lib/site-packages/pynini/examples/dates.py", "path_type": "hardlink", "sha256": "586f935741c2b42b6cb685321c5311257beed298ecfe0ff2193092dbb3e42fce", "sha256_in_prefix": "586f935741c2b42b6cb685321c5311257beed298ecfe0ff2193092dbb3e42fce", "size_in_bytes": 5233}, {"_path": "Lib/site-packages/pynini/examples/g2p.py", "path_type": "hardlink", "sha256": "d501080950a85202b2ab9e139b6e90dbfc7862b73694a44f174cd7742685aaff", "sha256_in_prefix": "d501080950a85202b2ab9e139b6e90dbfc7862b73694a44f174cd7742685aaff", "size_in_bytes": 2455}, {"_path": "Lib/site-packages/pynini/examples/numbers.py", "path_type": "hardlink", "sha256": "d627cdadc43a3a2548b33e7f21ac1d701ae173173f370c1e292f7d2eccf26bce", "sha256_in_prefix": "d627cdadc43a3a2548b33e7f21ac1d701ae173173f370c1e292f7d2eccf26bce", "size_in_bytes": 4173}, {"_path": "Lib/site-packages/pynini/examples/plurals.py", "path_type": "hardlink", "sha256": "08cbc7c7569e0c26c8267ccb90eef010d12357685573c051a968139987ed2261", "sha256_in_prefix": "08cbc7c7569e0c26c8267ccb90eef010d12357685573c051a968139987ed2261", "size_in_bytes": 2435}, {"_path": "Lib/site-packages/pynini/examples/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pynini/examples/t9.py", "path_type": "hardlink", "sha256": "7fa4d241020cc2217f92ddd0e629a02d539f6871135cef58e18f891d177dfbd8", "sha256_in_prefix": "7fa4d241020cc2217f92ddd0e629a02d539f6871135cef58e18f891d177dfbd8", "size_in_bytes": 2145}, {"_path": "Lib/site-packages/pynini/examples/weather.py", "path_type": "hardlink", "sha256": "89ac6dec65168a17b5f4914baa3c7c8676653b499fc534c681ba016ffb590fec", "sha256_in_prefix": "89ac6dec65168a17b5f4914baa3c7c8676653b499fc534c681ba016ffb590fec", "size_in_bytes": 3314}, {"_path": "Lib/site-packages/pynini/export/__init__.py", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pynini/export/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "9fa04423bb19aa4f90c441895a0849c1ca18f26a707e187ead95f4b4ffdd6cb2", "sha256_in_prefix": "9fa04423bb19aa4f90c441895a0849c1ca18f26a707e187ead95f4b4ffdd6cb2", "size_in_bytes": 152}, {"_path": "Lib/site-packages/pynini/export/__pycache__/export.cpython-311.pyc", "path_type": "hardlink", "sha256": "046af7698097e8765b6714f2a6bf86d0bbccf0743ef915686a670c5373edece0", "sha256_in_prefix": "046af7698097e8765b6714f2a6bf86d0bbccf0743ef915686a670c5373edece0", "size_in_bytes": 3708}, {"_path": "Lib/site-packages/pynini/export/__pycache__/grm.cpython-311.pyc", "path_type": "hardlink", "sha256": "4585ffc8056fbbe8488df8b0c5b3856adbcf5588b02f363b8c4427fbc91df8f4", "sha256_in_prefix": "4585ffc8056fbbe8488df8b0c5b3856adbcf5588b02f363b8c4427fbc91df8f4", "size_in_bytes": 2663}, {"_path": "Lib/site-packages/pynini/export/__pycache__/grm_example.cpython-311.pyc", "path_type": "hardlink", "sha256": "eb461fe4ec9184c572cef2222243d039e2a48b8abde8824fbe6bde612bd0ad02", "sha256_in_prefix": "eb461fe4ec9184c572cef2222243d039e2a48b8abde8824fbe6bde612bd0ad02", "size_in_bytes": 821}, {"_path": "Lib/site-packages/pynini/export/__pycache__/multi_grm.cpython-311.pyc", "path_type": "hardlink", "sha256": "022e5e542becbd4ed291745f8d0f37190b6d608c5598d97dccd7aed97f0a1c0c", "sha256_in_prefix": "022e5e542becbd4ed291745f8d0f37190b6d608c5598d97dccd7aed97f0a1c0c", "size_in_bytes": 4714}, {"_path": "Lib/site-packages/pynini/export/__pycache__/multi_grm_example.cpython-311.pyc", "path_type": "hardlink", "sha256": "1d2a5d61dc921dc1fb2a988ffe3c2bf1d3099f0c1a130170f10954dbb083c1e3", "sha256_in_prefix": "1d2a5d61dc921dc1fb2a988ffe3c2bf1d3099f0c1a130170f10954dbb083c1e3", "size_in_bytes": 908}, {"_path": "Lib/site-packages/pynini/export/export.py", "path_type": "hardlink", "sha256": "3d7e6e909f6f1da54f3870bf660d998142150c0bf53f438590080beef6287e63", "sha256_in_prefix": "3d7e6e909f6f1da54f3870bf660d998142150c0bf53f438590080beef6287e63", "size_in_bytes": 3273}, {"_path": "Lib/site-packages/pynini/export/grm.py", "path_type": "hardlink", "sha256": "2418bf63399eb34da35a2080538123199f1d49e1e9aabbb8cb8c6c907acc7d5e", "sha256_in_prefix": "2418bf63399eb34da35a2080538123199f1d49e1e9aabbb8cb8c6c907acc7d5e", "size_in_bytes": 2368}, {"_path": "Lib/site-packages/pynini/export/grm_example.py", "path_type": "hardlink", "sha256": "d985352e98ad7f64096e9e7cfd551a305c00f1cf71b0c1eeeb6f5ea5e5559d23", "sha256_in_prefix": "d985352e98ad7f64096e9e7cfd551a305c00f1cf71b0c1eeeb6f5ea5e5559d23", "size_in_bytes": 893}, {"_path": "Lib/site-packages/pynini/export/multi_grm.py", "path_type": "hardlink", "sha256": "8dbf0805fef19ccc817c52fad0dc05913599227e1b9e7a81697e47843a399741", "sha256_in_prefix": "8dbf0805fef19ccc817c52fad0dc05913599227e1b9e7a81697e47843a399741", "size_in_bytes": 3786}, {"_path": "Lib/site-packages/pynini/export/multi_grm_example.py", "path_type": "hardlink", "sha256": "e716130790d1ce14d90c13413f6f7312190d89d3a480e0bb7b952a43154eb225", "sha256_in_prefix": "e716130790d1ce14d90c13413f6f7312190d89d3a480e0bb7b952a43154eb225", "size_in_bytes": 955}, {"_path": "Lib/site-packages/pynini/export/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pynini/lib/__init__.py", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "b2da02fbaefcd833a9f17ccc75db154f440b4e807a17d1f690a9cd72f0a8c995", "sha256_in_prefix": "b2da02fbaefcd833a9f17ccc75db154f440b4e807a17d1f690a9cd72f0a8c995", "size_in_bytes": 149}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/byte.cpython-311.pyc", "path_type": "hardlink", "sha256": "387ce24e4b80fda70c049dbecd93898814d022b643d5b9ba3ef10ffe96e81351", "sha256_in_prefix": "387ce24e4b80fda70c049dbecd93898814d022b643d5b9ba3ef10ffe96e81351", "size_in_bytes": 2002}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/edit_transducer.cpython-311.pyc", "path_type": "hardlink", "sha256": "13aa6013cdb93ed16ffe59ce5c8c09f50165734f11bdf99014d0eedb654ffdc0", "sha256_in_prefix": "13aa6013cdb93ed16ffe59ce5c8c09f50165734f11bdf99014d0eedb654ffdc0", "size_in_bytes": 11606}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/features.cpython-311.pyc", "path_type": "hardlink", "sha256": "6a9b8a53794655d6b36f741d2b2ce7547c256d576a6b5acd664c82632871f2a5", "sha256_in_prefix": "6a9b8a53794655d6b36f741d2b2ce7547c256d576a6b5acd664c82632871f2a5", "size_in_bytes": 17943}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/paradigms.cpython-311.pyc", "path_type": "hardlink", "sha256": "958aa980547dbaf6e477b6e0c2e4391a8cb1fe312d8a98361cc75289f9e143c7", "sha256_in_prefix": "958aa980547dbaf6e477b6e0c2e4391a8cb1fe312d8a98361cc75289f9e143c7", "size_in_bytes": 25475}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/pynutil.cpython-311.pyc", "path_type": "hardlink", "sha256": "fa82036730bbbb70fdf39ec5b562f24e6a33df969fdd73225af6d046781ca310", "sha256_in_prefix": "fa82036730bbbb70fdf39ec5b562f24e6a33df969fdd73225af6d046781ca310", "size_in_bytes": 2483}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/rewrite.cpython-311.pyc", "path_type": "hardlink", "sha256": "0652e530a71b2d7770d5ef972a434fec535426582ee94168562a467d2f6712a4", "sha256_in_prefix": "0652e530a71b2d7770d5ef972a434fec535426582ee94168562a467d2f6712a4", "size_in_bytes": 14502}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/rule_cascade.cpython-311.pyc", "path_type": "hardlink", "sha256": "07b6b1cb89e846e9784401762a1e9538a0a8774b3654a9e8c63f8605ad5d0633", "sha256_in_prefix": "07b6b1cb89e846e9784401762a1e9538a0a8774b3654a9e8c63f8605ad5d0633", "size_in_bytes": 9603}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/tagger.cpython-311.pyc", "path_type": "hardlink", "sha256": "46d6d98180de4dd6abfc0f9b48cc00c08a7931bb1af33671be1535fe9e3b2c4c", "sha256_in_prefix": "46d6d98180de4dd6abfc0f9b48cc00c08a7931bb1af33671be1535fe9e3b2c4c", "size_in_bytes": 3013}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/utf8.cpython-311.pyc", "path_type": "hardlink", "sha256": "cdcf3364eeca42bdc9a68a10fabe06b88188417bb64f3a7c6fb22127a656f041", "sha256_in_prefix": "cdcf3364eeca42bdc9a68a10fabe06b88188417bb64f3a7c6fb22127a656f041", "size_in_bytes": 3229}, {"_path": "Lib/site-packages/pynini/lib/byte.py", "path_type": "hardlink", "sha256": "c1ca3eede319017eeb2cb9657730cadfe942a28f9e506a183436d4bf91b2eecf", "sha256_in_prefix": "c1ca3eede319017eeb2cb9657730cadfe942a28f9e506a183436d4bf91b2eecf", "size_in_bytes": 1624}, {"_path": "Lib/site-packages/pynini/lib/edit_transducer.py", "path_type": "hardlink", "sha256": "efe60f5c6b595dd2db85d3489baf39d793310ab66d2212ffe0f46a15809b6a77", "sha256_in_prefix": "efe60f5c6b595dd2db85d3489baf39d793310ab66d2212ffe0f46a15809b6a77", "size_in_bytes": 8667}, {"_path": "Lib/site-packages/pynini/lib/features.py", "path_type": "hardlink", "sha256": "48118545184502176db5798297e11f17a0ad0037ec347a7dfaa2ec33df5e4197", "sha256_in_prefix": "48118545184502176db5798297e11f17a0ad0037ec347a7dfaa2ec33df5e4197", "size_in_bytes": 9909}, {"_path": "Lib/site-packages/pynini/lib/paradigms.py", "path_type": "hardlink", "sha256": "afccd74267be9b81e8932017d04237a256d2647adaab43ecd6339fcf61d4cbad", "sha256_in_prefix": "afccd74267be9b81e8932017d04237a256d2647adaab43ecd6339fcf61d4cbad", "size_in_bytes": 18822}, {"_path": "Lib/site-packages/pynini/lib/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pynini/lib/pynutil.py", "path_type": "hardlink", "sha256": "980ff46d584589978dbe2be88e2b39b5a31458c4079de78989428e3955ebf033", "sha256_in_prefix": "980ff46d584589978dbe2be88e2b39b5a31458c4079de78989428e3955ebf033", "size_in_bytes": 2253}, {"_path": "Lib/site-packages/pynini/lib/rewrite.py", "path_type": "hardlink", "sha256": "380521d27f7d86e6fc7630adab1ee2d3f6341630d1b328e3ee0a1c8e15710e5d", "sha256_in_prefix": "380521d27f7d86e6fc7630adab1ee2d3f6341630d1b328e3ee0a1c8e15710e5d", "size_in_bytes": 12433}, {"_path": "Lib/site-packages/pynini/lib/rule_cascade.py", "path_type": "hardlink", "sha256": "674ea525e91ebcd8ae9ce3c3a10b61310b6a1c14d709c1d1d23fb5acee97f9b3", "sha256_in_prefix": "674ea525e91ebcd8ae9ce3c3a10b61310b6a1c14d709c1d1d23fb5acee97f9b3", "size_in_bytes": 7743}, {"_path": "Lib/site-packages/pynini/lib/tagger.py", "path_type": "hardlink", "sha256": "7256107b465b46968cb1614234e94775d9a99e17b3c1a3eed6e95d60cd8eb5a4", "sha256_in_prefix": "7256107b465b46968cb1614234e94775d9a99e17b3c1a3eed6e95d60cd8eb5a4", "size_in_bytes": 2557}, {"_path": "Lib/site-packages/pynini/lib/utf8.py", "path_type": "hardlink", "sha256": "5f5ed6582fbbca5d1a8dca3cb962f78943658f0d3cea1e8df4925437265a1c4b", "sha256_in_prefix": "5f5ed6582fbbca5d1a8dca3cb962f78943658f0d3cea1e8df4925437265a1c4b", "size_in_bytes": 3956}, {"_path": "Lib/site-packages/pynini/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pywrapfst/__init__.py", "path_type": "hardlink", "sha256": "004b691d8c8360b5c5accfdeb5835e02c0a5efb7cd6dfe4604888033c7b25fcd", "sha256_in_prefix": "004b691d8c8360b5c5accfdeb5835e02c0a5efb7cd6dfe4604888033c7b25fcd", "size_in_bytes": 25}, {"_path": "Lib/site-packages/pywrapfst/__init__.pyi", "path_type": "hardlink", "sha256": "ce1c1b815caf9155a27d4607e0373902d9f7112cbc792497051e79bff2186d7c", "sha256_in_prefix": "ce1c1b815caf9155a27d4607e0373902d9f7112cbc792497051e79bff2186d7c", "size_in_bytes": 23938}, {"_path": "Lib/site-packages/pywrapfst/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "986c006d2871737c211a506d7e73fc14245de98cae1eab84c2e75bd287a21688", "sha256_in_prefix": "986c006d2871737c211a506d7e73fc14245de98cae1eab84c2e75bd287a21688", "size_in_bytes": 183}, {"_path": "Lib/site-packages/pywrapfst/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Library/include/_pywrapfst.h", "path_type": "hardlink", "sha256": "6571828f31d08a27b9f8006cb6f6d9d62aeb41c536a095378b35f73f4290d72d", "sha256_in_prefix": "6571828f31d08a27b9f8006cb6f6d9d62aeb41c536a095378b35f73f4290d72d", "size_in_bytes": 9718}], "paths_version": 1}, "requested_spec": "pynini==2.1.5", "sha256": "bd460ffe0f231d2e3a15c1ffdf33ebaf622087b2b1151f9f30bfe0ecf30371f5", "size": 833134, "subdir": "win-64", "timestamp": 1696661508000, "url": "https://conda.anaconda.org/conda-forge/win-64/pynini-2.1.5-py311h005e61a_6.conda", "version": "2.1.5"}