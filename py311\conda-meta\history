==> 2025-01-18 18:00:04 <==
# cmd: D:\anaconda3\Scripts\conda-script.py create -n py311 -y python=3.11
# conda version: 24.9.2
+defaults/noarch::tzdata-2024b-h04d1e81_0
+defaults/win-64::bzip2-1.0.8-h2bbff1b_6
+defaults/win-64::ca-certificates-2024.12.31-haa95532_0
+defaults/win-64::libffi-3.4.4-hd77b12b_1
+defaults/win-64::openssl-3.0.15-h827c3e9_0
+defaults/win-64::pip-24.2-py311haa95532_0
+defaults/win-64::python-3.11.11-h4607a30_0
+defaults/win-64::setuptools-75.1.0-py311haa95532_0
+defaults/win-64::sqlite-3.45.3-h2bbff1b_0
+defaults/win-64::tk-8.6.14-h0416ee5_0
+defaults/win-64::vc-14.40-haa95532_2
+defaults/win-64::vs2015_runtime-14.42.34433-h9531ae6_2
+defaults/win-64::wheel-0.44.0-py311haa95532_0
+defaults/win-64::xz-5.4.6-h8cc25b3_1
+defaults/win-64::zlib-1.2.13-h8cc25b3_1
# update specs: ['python=3.11']
==> 2025-01-18 18:03:31 <==
# cmd: D:\anaconda3\Scripts\conda-script.py install -y -c conda-forge pynini==2.1.5
# conda version: 24.9.2
-defaults/win-64::openssl-3.0.15-h827c3e9_0
-defaults/win-64::vs2015_runtime-14.42.34433-h9531ae6_2
+conda-forge/noarch::font-ttf-dejavu-sans-mono-2.37-hab24e00_0
+conda-forge/noarch::font-ttf-inconsolata-3.000-h77eed37_0
+conda-forge/noarch::font-ttf-source-code-pro-2.038-h77eed37_0
+conda-forge/noarch::font-ttf-ubuntu-0.83-h77eed37_3
+conda-forge/noarch::fonts-conda-ecosystem-1-0
+conda-forge/noarch::fonts-conda-forge-1-0
+conda-forge/win-64::dlfcn-win32-1.4.1-h63175ca_0
+conda-forge/win-64::expat-2.6.4-he0c23c2_0
+conda-forge/win-64::fribidi-1.0.10-h8d14728_0
+conda-forge/win-64::getopt-win32-0.1-hcfcfb64_1
+conda-forge/win-64::graphite2-1.3.13-h63175ca_1003
+conda-forge/win-64::graphviz-7.1.0-h51cb2cd_0
+conda-forge/win-64::gts-0.7.6-h6b5321d_4
+conda-forge/win-64::harfbuzz-6.0.0-h196d34a_1
+conda-forge/win-64::icu-72.1-h63175ca_0
+conda-forge/win-64::jbig-2.1-h8d14728_2003
+conda-forge/win-64::jpeg-9e-hcfcfb64_3
+conda-forge/win-64::lerc-2.2.1-h0e60522_0
+conda-forge/win-64::libdeflate-1.7-h8ffe710_5
+conda-forge/win-64::libexpat-2.6.4-he0c23c2_0
+conda-forge/win-64::libiconv-1.17-hcfcfb64_2
+conda-forge/win-64::libtiff-4.3.0-h0c97f57_1
+conda-forge/win-64::libwebp-base-1.5.0-h3b0e114_0
+conda-forge/win-64::lz4-c-1.9.3-h8ffe710_1
+conda-forge/win-64::openfst-1.8.2-h91493d7_2
+conda-forge/win-64::openssl-3.4.0-ha4e3fda_1
+conda-forge/win-64::pango-1.50.12-hdffb7b3_1
+conda-forge/win-64::pixman-0.44.2-had0cd8c_0
+conda-forge/win-64::pynini-2.1.5-py311h005e61a_6
+conda-forge/win-64::python_abi-3.11-2_cp311
+conda-forge/win-64::ucrt-10.0.22621.0-h57928b3_1
+conda-forge/win-64::vc14_runtime-14.42.34433-he29a5d6_23
+conda-forge/win-64::vs2015_runtime-14.42.34433-hdffcdeb_23
+conda-forge/win-64::zstd-1.5.0-h6255e5f_0
+defaults/win-64::cairo-1.16.0-hc68a040_5
+defaults/win-64::fontconfig-2.14.1-hb33846d_3
+defaults/win-64::freetype-2.12.1-ha860e81_0
+defaults/win-64::glib-2.78.4-hd77b12b_0
+defaults/win-64::glib-tools-2.78.4-hd77b12b_0
+defaults/win-64::libgd-2.3.3-ha43c60c_1
+defaults/win-64::libglib-2.78.4-ha17d25a_0
+defaults/win-64::libpng-1.6.39-h8cc25b3_0
+defaults/win-64::libxml2-2.13.5-h24da03e_0
+defaults/win-64::pcre2-10.42-h0ff8eda_1
# update specs: ['pynini==2.1.5']
==> 2025-01-18 18:10:10 <==
# cmd: D:\anaconda3\Scripts\conda-script.py install nvidia/label/cuda-12.4.0::cuda-toolkit
# conda version: 24.9.2
+defaults/noarch::cuda-cccl_win-64-12.4.127-haa95532_2
+defaults/noarch::cuda-crt-dev_win-64-12.4.131-haa95532_0
+defaults/noarch::cuda-cudart-dev_win-64-12.4.127-hd77b12b_0
+defaults/noarch::cuda-cudart-static_win-64-12.4.127-hd77b12b_0
+defaults/noarch::cuda-cudart_win-64-12.4.127-hd77b12b_0
+defaults/noarch::cuda-nvcc-dev_win-64-12.4.131-haa95532_0
+defaults/noarch::cuda-nvvm-dev_win-64-12.4.131-haa95532_0
+defaults/noarch::cuda-version-12.4-hbda6634_3
+defaults/win-64::cccl-2.3.2-h47f531a_0
+defaults/win-64::cuda-cccl-12.4.127-haa95532_2
+defaults/win-64::cuda-command-line-tools-12.4.1-haa95532_1
+defaults/win-64::cuda-compiler-12.4.1-hd77b12b_1
+defaults/win-64::cuda-crt-tools-12.4.131-haa95532_0
+defaults/win-64::cuda-cudart-12.4.127-hd77b12b_0
+defaults/win-64::cuda-cudart-dev-12.4.127-hd77b12b_0
+defaults/win-64::cuda-cudart-static-12.4.127-hd77b12b_0
+defaults/win-64::cuda-cuobjdump-12.4.127-hd77b12b_1
+defaults/win-64::cuda-cupti-12.4.127-hd77b12b_1
+defaults/win-64::cuda-cupti-dev-12.4.127-hd77b12b_1
+defaults/win-64::cuda-cuxxfilt-12.4.127-hd77b12b_1
+defaults/win-64::cuda-libraries-12.4.1-haa95532_1
+defaults/win-64::cuda-libraries-dev-12.4.1-haa95532_1
+defaults/win-64::cuda-nvcc-12.4.131-h1fd813f_0
+defaults/win-64::cuda-nvcc-impl-12.4.131-h35fed64_0
+defaults/win-64::cuda-nvcc-tools-12.4.131-hd77b12b_0
+defaults/win-64::cuda-nvcc_win-64-12.4.131-h1fd813f_0
+defaults/win-64::cuda-nvdisasm-12.4.127-hd77b12b_1
+defaults/win-64::cuda-nvml-dev-12.4.127-hd77b12b_1
+defaults/win-64::cuda-nvprof-12.4.127-hd77b12b_1
+defaults/win-64::cuda-nvprune-12.4.127-hd77b12b_1
+defaults/win-64::cuda-nvrtc-12.4.127-hd77b12b_1
+defaults/win-64::cuda-nvrtc-dev-12.4.127-hd77b12b_1
+defaults/win-64::cuda-nvvm-impl-12.4.131-hd77b12b_0
+defaults/win-64::cuda-nvvm-tools-12.4.131-hd77b12b_0
+defaults/win-64::cuda-nvvp-12.4.127-hd77b12b_1
+defaults/win-64::cuda-opencl-12.4.127-hd77b12b_0
+defaults/win-64::cuda-opencl-dev-12.4.127-hd77b12b_0
+defaults/win-64::cuda-profiler-api-12.4.127-haa95532_1
+defaults/win-64::cuda-sanitizer-api-12.4.127-hd77b12b_1
+defaults/win-64::cuda-tools-12.4.1-haa95532_1
+defaults/win-64::cuda-visual-tools-12.4.1-haa95532_1
+defaults/win-64::khronos-opencl-icd-loader-2024.05.08-h8cc25b3_0
+defaults/win-64::krb5-1.20.1-h5b6d351_0
+defaults/win-64::libcublas-12.4.5.8-hd77b12b_1
+defaults/win-64::libcublas-dev-12.4.5.8-hd77b12b_1
+defaults/win-64::libcufft-11.2.1.3-hd77b12b_1
+defaults/win-64::libcufft-dev-11.2.1.3-hd77b12b_1
+defaults/win-64::libcurand-10.3.5.147-hd77b12b_1
+defaults/win-64::libcurand-dev-10.3.5.147-hd77b12b_1
+defaults/win-64::libcusolver-11.6.1.9-hd77b12b_1
+defaults/win-64::libcusolver-dev-11.6.1.9-hd77b12b_1
+defaults/win-64::libcusparse-12.3.1.170-hd77b12b_1
+defaults/win-64::libcusparse-dev-12.3.1.170-hd77b12b_1
+defaults/win-64::libnpp-12.2.5.30-hd77b12b_1
+defaults/win-64::libnpp-dev-12.2.5.30-hd77b12b_1
+defaults/win-64::libnvfatbin-12.4.127-h20ee8b7_2
+defaults/win-64::libnvfatbin-dev-12.4.127-h20ee8b7_2
+defaults/win-64::libnvjitlink-12.4.127-hd77b12b_1
+defaults/win-64::libnvjitlink-dev-12.4.127-hd77b12b_1
+defaults/win-64::libnvjpeg-**********-hd77b12b_1
+defaults/win-64::libnvjpeg-dev-**********-haa95532_1
+defaults/win-64::nsight-compute-2024.1.1.4-hb5e1e24_2
+defaults/win-64::vs2017_win-64-19.16.27032.1-hb4161e2_3
+defaults/win-64::vswhere-2.8.4-haa95532_0
+nvidia/label/cuda-12.4.0/win-64::cuda-documentation-12.4.99-0
+nvidia/label/cuda-12.4.0/win-64::cuda-toolkit-12.4.0-0
# update specs: ['nvidia/label/cuda-12.4.0::cuda-toolkit']
==> 2025-01-18 18:12:32 <==
# cmd: D:\anaconda3\Scripts\conda-script.py install -c conda-forge cudnn
# conda version: 24.9.2
+conda-forge/win-64::cudnn-********-h1361d0a_2
# update specs: ['cudnn']
==> 2025-01-18 18:20:04 <==
# cmd: D:\anaconda3\Scripts\conda-script.py install requests
# conda version: 24.9.2
+defaults/noarch::charset-normalizer-3.3.2-pyhd3eb1b0_0
+defaults/win-64::brotli-python-1.0.9-py311h5da7b33_9
+defaults/win-64::certifi-2024.12.14-py311haa95532_0
+defaults/win-64::idna-3.7-py311haa95532_0
+defaults/win-64::pysocks-1.7.1-py311haa95532_0
+defaults/win-64::requests-2.32.3-py311haa95532_1
+defaults/win-64::urllib3-2.3.0-py311haa95532_0
+defaults/win-64::win_inet_pton-1.1.0-py311haa95532_0
# update specs: ['requests']
