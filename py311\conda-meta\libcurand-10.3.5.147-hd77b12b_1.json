{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libcurand-10.3.5.147-hd77b12b_1", "files": ["Library/bin/curand64_10.dll"], "fn": "libcurand-10.3.5.147-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libcurand-10.3.5.147-hd77b12b_1", "type": 1}, "md5": "460f7110b4dd9def79461479b7fcadb7", "name": "lib<PERSON><PERSON>", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libcurand-10.3.5.147-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/bin/curand64_10.dll", "path_type": "hardlink", "sha256": "1d27e79c273e3963d4876afe1011526904c5182e1400c81ca292e061581f1e72", "sha256_in_prefix": "1d27e79c273e3963d4876afe1011526904c5182e1400c81ca292e061581f1e72", "size_in_bytes": 63308800}], "paths_version": 1}, "requested_spec": "None", "sha256": "49e147da8951f1b8963ec6a58da8b0fb1283090096483b141ea49b382084e297", "size": 44213692, "subdir": "win-64", "timestamp": 1714677970000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libcurand-10.3.5.147-hd77b12b_1.conda", "version": "10.3.5.147"}