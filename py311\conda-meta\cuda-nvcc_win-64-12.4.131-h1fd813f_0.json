{"build": "h1fd813f_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["cuda-cudart-dev_win-64 12.4.*", "cuda-nvcc-dev_win-64 12.4.131.*", "cuda-nvcc-impl 12.4.131.*", "cuda-nvcc-tools 12.4.131.*"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-nvcc_win-64-12.4.131-h1fd813f_0", "files": ["etc/conda/activate.d/~cuda-nvcc_activate.bat", "etc/conda/deactivate.d/~cuda-nvcc_deactivate.bat"], "fn": "cuda-nvcc_win-64-12.4.131-h1fd813f_0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-nvcc_win-64-12.4.131-h1fd813f_0", "type": 1}, "md5": "180a0c489098ee370b3c2a50bd234c9b", "name": "cuda-nvcc_win-64", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-nvcc_win-64-12.4.131-h1fd813f_0.conda", "paths_data": {"paths": [{"_path": "etc/conda/activate.d/~cuda-nvcc_activate.bat", "path_type": "hardlink", "sha256": "587a52d24c7111b76b4d7fd1b78d022ba4cbcac20af0a35e88892cd8b6c5230b", "sha256_in_prefix": "587a52d24c7111b76b4d7fd1b78d022ba4cbcac20af0a35e88892cd8b6c5230b", "size_in_bytes": 263}, {"_path": "etc/conda/deactivate.d/~cuda-nvcc_deactivate.bat", "path_type": "hardlink", "sha256": "707e936e0f29c6244eb8ec10c98d8bbb4b5cb51c2294ab45f1c2a145c1433329", "sha256_in_prefix": "707e936e0f29c6244eb8ec10c98d8bbb4b5cb51c2294ab45f1c2a145c1433329", "size_in_bytes": 195}], "paths_version": 1}, "requested_spec": "None", "sha256": "da3ab95ae807018261ec7fde974be5fe4c09af0d80373077874bbf5c3ea7920b", "size": 24172, "subdir": "win-64", "timestamp": 1714773588000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-nvcc_win-64-12.4.131-h1fd813f_0.conda", "version": "12.4.131"}