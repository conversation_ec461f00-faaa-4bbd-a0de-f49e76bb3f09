---
title: BEN2
emoji: 🚀
colorFrom: purple
colorTo: gray
sdk: gradio
sdk_version: 5.13.1
app_file: app.py
pinned: false
---

# BEN2 - 支持MCP的背景移除工具

BEN2是一个强大的背景移除工具，可以处理图像和视频。本项目现已支持模型上下文协议（MCP），允许AI助手将背景移除功能作为工具使用。

## 功能特性

- 🖼️ **图像背景移除**：高质量移除图像背景
- 🎥 **视频背景移除**：处理视频移除背景（实验性功能）
- 🤖 **MCP支持**：作为MCP服务器供AI助手和客户端使用
- 🌐 **Web界面**：易于使用的Gradio界面
- ☁️ **Hugging Face Spaces**：可免费部署到Hugging Face

## 快速开始

### 本地安装

**方法一：自动安装（推荐）**
```bash
python install_mcp.py
```

**方法二：手动安装**
```bash
# 安装基础依赖
pip install -r requirements.txt

# 安装额外必需模块
pip install spaces loadimg einops timm "gradio[mcp]"
```

### 运行应用

```bash
python app.py
```

应用将在 `http://localhost:7860` 可用

### MCP集成

本项目支持模型上下文协议，允许AI客户端使用背景移除工具。

**MCP端点**：`http://localhost:7860/gradio_api/mcp/sse`

详细的MCP设置说明请参见 [MCP_USAGE.md](MCP_USAGE.md)

### 可用的MCP工具

- `fn`：移除图像背景
- `process_video`：移除视频背景
- `process_file`：处理文件移除背景

## 测试

测试MCP功能：
```bash
python test_mcp.py
```

## 故障排除

如果遇到问题：

1. **依赖错误**：运行 `python install_mcp.py` 自动安装
2. **wandb错误**：尝试 `pip install --upgrade wandb`
3. **MCP不支持**：应用会自动回退到标准模式
4. **CUDA错误**：确保PyTorch版本正确

## Configuration Reference

Check out the configuration reference at https://huggingface.co/docs/hub/spaces-config-reference
