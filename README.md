---
title: BEN2
emoji: 🚀
colorFrom: purple
colorTo: gray
sdk: gradio
sdk_version: 5.13.1
app_file: app.py
pinned: false
---

# BEN2 - Background Removal with MCP Support

BEN2 is a powerful background removal tool that can process both images and videos. This project now supports the Model Context Protocol (MCP), allowing AI assistants to use the background removal capabilities as tools.

## Features

- 🖼️ **Image Background Removal**: Remove backgrounds from images with high quality results
- 🎥 **Video Background Removal**: Process videos to remove backgrounds (experimental)
- 🤖 **MCP Support**: Use as an MCP server for AI assistants and clients
- 🌐 **Web Interface**: Easy-to-use Gradio interface
- ☁️ **Hugging Face Spaces**: Deploy for free on Hugging Face

## Quick Start

### Local Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Run the application:
```bash
python app.py
```

The app will be available at `http://localhost:7860`

### MCP Integration

This project supports the Model Context Protocol, allowing AI clients to use the background removal tools.

**MCP Endpoint**: `http://localhost:7860/gradio_api/mcp/sse`

For detailed MCP setup instructions, see [MCP_USAGE.md](MCP_USAGE.md)

### Available MCP Tools

- `fn`: Remove background from images
- `process_video`: Remove background from videos
- `process_file`: Process files with background removal

## Testing

Test the MCP functionality:
```bash
python test_mcp.py
```

## Configuration Reference

Check out the configuration reference at https://huggingface.co/docs/hub/spaces-config-reference
