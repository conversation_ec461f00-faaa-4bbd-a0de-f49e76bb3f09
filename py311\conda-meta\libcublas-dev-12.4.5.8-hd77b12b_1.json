{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0", "libcublas 12.4.5.8 hd77b12b_1"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libcublas-dev-12.4.5.8-hd77b12b_1", "files": ["Library/include/cublas.h", "Library/include/cublasLt.h", "Library/include/cublasXt.h", "Library/include/cublas_api.h", "Library/include/cublas_v2.h", "Library/include/nvblas.h", "Library/lib/cublas.lib", "Library/lib/cublasLt.lib", "Library/lib/nvblas.lib"], "fn": "libcublas-dev-12.4.5.8-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libcublas-dev-12.4.5.8-hd77b12b_1", "type": 1}, "md5": "957befaeff4a49f6b09bd3ce8b161219", "name": "libcublas-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libcublas-dev-12.4.5.8-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/include/cublas.h", "path_type": "hardlink", "sha256": "917979843ed4b042674a0df27b86c06fe1e28b2643c8e24e6709c1e0d85c8b41", "sha256_in_prefix": "917979843ed4b042674a0df27b86c06fe1e28b2643c8e24e6709c1e0d85c8b41", "size_in_bytes": 42137}, {"_path": "Library/include/cublasLt.h", "path_type": "hardlink", "sha256": "41bdb3e5003a78900b9e2c0c07a4cfe91b369a6383d08c2f9621503c3d42b739", "sha256_in_prefix": "41bdb3e5003a78900b9e2c0c07a4cfe91b369a6383d08c2f9621503c3d42b739", "size_in_bytes": 79471}, {"_path": "Library/include/cublasXt.h", "path_type": "hardlink", "sha256": "a51299616004afcc0ed44d3394508d4878c66d6e5fd241ba027bc17e4dcc4fb5", "sha256_in_prefix": "a51299616004afcc0ed44d3394508d4878c66d6e5fd241ba027bc17e4dcc4fb5", "size_in_bytes": 38073}, {"_path": "Library/include/cublas_api.h", "path_type": "hardlink", "sha256": "3b709678522dc65e8005733726a27a768953217f41d060f1959de7222c6668d0", "sha256_in_prefix": "3b709678522dc65e8005733726a27a768953217f41d060f1959de7222c6668d0", "size_in_bytes": 376456}, {"_path": "Library/include/cublas_v2.h", "path_type": "hardlink", "sha256": "c00d426fbc7aa24c10702492c0df2530fcf45786fc3e78832a1dccb3fba2c4ee", "sha256_in_prefix": "c00d426fbc7aa24c10702492c0df2530fcf45786fc3e78832a1dccb3fba2c4ee", "size_in_bytes": 15938}, {"_path": "Library/include/nvblas.h", "path_type": "hardlink", "sha256": "0bebae64184148e78c709fd283616ec831f0802ca915f1951412a9185a627a0c", "sha256_in_prefix": "0bebae64184148e78c709fd283616ec831f0802ca915f1951412a9185a627a0c", "size_in_bytes": 24165}, {"_path": "Library/lib/cublas.lib", "path_type": "hardlink", "sha256": "bb9bbd55b424e0c4838eb9af7b82886c9ce371469d67b8b39431b6989e08b9ec", "sha256_in_prefix": "bb9bbd55b424e0c4838eb9af7b82886c9ce371469d67b8b39431b6989e08b9ec", "size_in_bytes": 154354}, {"_path": "Library/lib/cublasLt.lib", "path_type": "hardlink", "sha256": "05812693cd041a55727eb81b9a99cbabb8cce59b62b0de45e7b8943ef838821e", "sha256_in_prefix": "05812693cd041a55727eb81b9a99cbabb8cce59b62b0de45e7b8943ef838821e", "size_in_bytes": 89208}, {"_path": "Library/lib/nvblas.lib", "path_type": "hardlink", "sha256": "efaa3300ca6589006be8651d6362acb75dab73a8de6587761e85eab00bb8cc18", "sha256_in_prefix": "efaa3300ca6589006be8651d6362acb75dab73a8de6587761e85eab00bb8cc18", "size_in_bytes": 11250}], "paths_version": 1}, "requested_spec": "None", "sha256": "899d34fc3dc5347a113a2ff77d54d5c99ae56b8a3bb54390150a2cf79c0274da", "size": 89557, "subdir": "win-64", "timestamp": 1714775846000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libcublas-dev-12.4.5.8-hd77b12b_1.conda", "version": "12.4.5.8"}